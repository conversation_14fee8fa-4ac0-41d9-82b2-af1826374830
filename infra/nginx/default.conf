
server {
    listen 80;
    listen [::]:80;  # IPv6 support
    server_name api.nepaltracks.com;

    # Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;  # IPv6 support
    server_name api.nepaltracks.com;

    # Dummy SSL certificate (since <PERSON><PERSON><PERSON><PERSON> terminates SSL)
    ssl_certificate /etc/nginx/ssl/dummy.pem;
    ssl_certificate_key /etc/nginx/ssl/dummy.key;

    # Trust Cloudflare's real IP addresses
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    location / {
        proxy_pass http://127.0.0.1:3434;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Prevent slowloris attacks
        client_body_timeout 10;
        send_timeout 10;
        keepalive_timeout 15;

        # Limit request body size
        client_max_body_size 10M;
    }

    # Security: block setup pages & phpMyAdmin
    location /setup {
        deny all;
    }

    location /phpmyadmin/setup {
        deny all;
    }

    # Security: block dangerous file types
    location ~* \.(php|asp|jsp|exe|sh|bat|cmd)$ {
        deny all;
    }

    # Buffer overflow protection
    large_client_header_buffers 4 16k;

    # Security headers
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options nosniff;

    # Logging
    access_log /var/log/nginx/api-access.log;
    error_log /var/log/nginx/api-error.log warn;
}