{"private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev --parallel", "prod": "turbo run prod", "analyze": "turbo run analyze", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "stripe:listen": "stripe listen --forward-to localhost:3001/api/v1/payment/webhook", "version": "changeset version", "release": "changeset publish", "prepare": "husky install", "prisma": "turbo run prisma --filter api", "prisma:migrate-prod": "turbo run prisma:migrate-prod --filter api", "download:langs": "node apps/translations/lang/index.mjs && rm -rf apps/web/src/locales/* && cp -r apps/translations/lang/i18n/* apps/web/src/locales", "copy:langs": "rm -rf apps/web/src/locales/* && cp -r apps/translations/lang/i18n/* apps/web/src/locales"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}, "devDependencies": {"images-scraper": "7.0.0", "@commitlint/cli": "17.6.7", "@commitlint/config-conventional": "17.6.7", "commitizen": "4.3.0", "cz-conventional-changelog": "3.3.0", "eslint": "^7.32.0", "eslint-config-custom": "*", "graphql-request": "7.1.2", "husky": "^8.0.3", "node-notifier": "^10.0.1", "p-limit": "6.2.0", "prettier": "^2.5.1", "turbo": "^1.9.3"}, "name": "rajniti-report", "packageManager": "yarn@1.22.19", "workspaces": ["apps/*", "packages/*"], "lint-staged": {"**/*.js": ["prettier-standard"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "git-cz"}}, "dependencies": {"sitemap": "8.0.0", "@adminjs/nestjs": "^6.1.0", "git-cz": "^4.9.0", "glob": "^11.0.2"}}