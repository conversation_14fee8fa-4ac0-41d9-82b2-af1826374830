"use strict";

import puppeteer from "puppeteer";
import * as fs from "fs";
import axios from "axios";
import * as path from "path";
import { BraveSearch } from "brave-search";

type ImageResult = { query: string; url: string };

async function getSummary() {
  const braveSearch = new BraveSearch(process.env.BRAVE_API_KEY);
  const webSearchResults = await braveSearch.webSearch("Rabi Lamichhane", {
    count: 10,
    search_lang: "en",
    country: "US",
    text_decorations: false,
  });

  const { summary, webSearch } = braveSearch.getSummarizedAnswer(
    "Rabi Lamichhane",
    {
      count: 10, // Number of search results to return
      search_lang: "en", // Optional: Language of the search results (default is "en")
      country: "US", // Optional: Country for the search results (default is "us")
      text_decorations: false, // Optional: Whether to include text decorations (default is true)
      spellcheck: false, // Optional: Whether to enable spellcheck (default is true)
      extra_snippets: true, // Optional: Whether to include extra snippets (default is false)
    }
  );

  // Wait for the web search results (almost immediately)
  const webSearchResponse = await webSearch;
  console.log(
    "Web Search Response:",
    JSON.stringify(webSearchResponse, null, 2)
  );

  // Wait for the summarized answer (can take up to couple of seconds)
  const summarizedAnswer = await summary;
  console.log("Summarized Answer:", JSON.stringify(summarizedAnswer, null, 2));
  return { summarizedAnswer, webSearchResponse, webSearchResults };
}

getSummary()
  .then(({ summarizedAnswer, webSearchResponse, webSearchResults }) => {
    console.log(summarizedAnswer);
    console.log(webSearchResponse);
    console.log(webSearchResults);
    fs.writeFileSync(
      "webSearchResults.json",
      JSON.stringify(webSearchResults, null, 2)
    );
    fs.writeFileSync(
      "webSearchResponse.json",
      JSON.stringify(webSearchResponse, null, 2)
    );
    fs.writeFileSync("summary.json", JSON.stringify(summarizedAnswer, null, 2));
  })
  .catch((err) => {
    console.error(err);
  });
