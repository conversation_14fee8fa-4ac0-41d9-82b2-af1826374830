module.exports = {
  apps: [
    {
      name: "nepaltracks-api", // Name of the application
      script: "apps/api/dist/src/main.js", // Script to execute
      instances: 1, // Scale application based on CPU cores
      exec_mode: "fork", // Enables clustering
      env_production: {
        NODE_ENV: "production",
        PORT: 3000,
      },
      node_args: "-r dotenv/config --no-opt --max-old-space-size=500",
      max_memory_restart: "1G", // Restart if memory exceeds 200 MB
      watch: false, // Disable watching in production
      ignore_watch: ["node_modules"], // Don't watch node_modules
    },
  ],

  deploy: {
    production: {
      user: "ubuntu", // User to SSH as
      host: ["target_server"], // Server IP/hostname
      ref: "origin/main", // Git branch to deploy from
      repo: "**************:samundrak/rajniti-report.git", // Git repository
      path: "/home/<USER>/apps", // Path on the server
      "pre-deploy": "git fetch", // Commands to run before deploying
      "post-deploy": `yarn && cp .env ./apps/api/.env && yarn run prisma:migrate-prod && yarn run prisma && yarn run build --filter=api  && npx pm2 startOrRestart ecosystem.config.js --env production --only nepaltracks-api`, // Post-deployment actions
    },
    localtoproduction: {
      user: "ubuntu", // User to SSH as
      host: ["***************"], // Server IP/hostname
      ref: "origin/main", // Git branch to deploy from
      repo: "**************:samundrak/rajniti-report.git", // Git repository
      path: "/home/<USER>/apps", // Path on the server
      "pre-deploy": "git fetch", // Commands to run before deploying
      "post-deploy": `pwd && yarn && cp .env ./apps/api/.env && yarn run prisma:migrate-prod && yarn run prisma && yarn run build --filter=api  && npx pm2 startOrRestart ecosystem.config.js --env production --only nepaltracks-api`, // Post-deployment actions
    },
    onlyredeploy: {
      user: "ubuntu", // User to SSH as
      host: ["***************"], // Server IP/hostname
      ref: "origin/main", // Git branch to deploy from
      repo: "**************:samundrak/rajniti-report.git", // Git repository
      path: "/home/<USER>/apps", // Path on the server
      "post-deploy": ` npx pm2 startOrRestart ecosystem.config.js --env production --only nepaltracks-api`, // Post-deployment actions
    },
    buildanddeploy: {
      user: "ubuntu", // User to SSH as
      host: ["***************"], // Server IP/hostname
      ref: "origin/main", // Git branch to deploy from
      repo: "**************:samundrak/rajniti-report.git", // Git repository
      path: "/home/<USER>/apps", // Path on the server
      "post-deploy": `yarn run build --filter=api && npx pm2 startOrRestart ecosystem.config.js --env production --only nepaltracks-api`, // Post-deployment actions
    },
  },
};
