{"name": "@rajniti/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3232", "prod": "next start -p 3232", "build": "next build", "analyze": "ANALYZE=true next build", "start": "next start -p 3000", "lint": "next lint"}, "devDependencies": {"postcss": "latest", "postcss-preset-mantine": "latest", "postcss-simple-vars": "latest"}, "dependencies": {"@dhaiwat10/react-link-preview": "1.15.0", "@emotion/react": "11.11.0", "@emotion/server": "11.11.0", "@emotion/styled": "11.11.0", "@mantine/carousel": "7.6.0", "@mantine/core": "7.6.0", "@mantine/dates": "7.6.0", "@mantine/ds": "7.2.2", "@mantine/form": "7.6.0", "@mantine/hooks": "7.6.0", "@mantine/next": "6.0.21", "@mantine/notifications": "7.6.0", "@mantine/prism": "6.0.21", "@ri/fe-auth": "*", "@svgr/webpack": "8.1.0", "@tabler/icons-react": "2.23.0", "@tanstack/react-virtual": "3.13.8", "@types/node": "20.2.5", "@types/react": "18.2.7", "@types/react-dom": "18.2.4", "autoprefixer": "10.4.14", "axios": "1.4.0", "chart.js": "4.3.2", "daisyui": "latest", "date-fns": "^4.1.0", "dayjs": "1.11.7", "disqus-react": "latest", "dot-prop-immutable": "2.1.1", "embla-carousel-react": "8.0.0-rc07", "eslint": "8.41.0", "eslint-config-next": "13.4.4", "i18next": "23.7.7", "jwt-decode": "3.1.2", "lodash": "4.17.21", "lottie-react": "2.4.0", "next": "13.4.4", "next-auth": "4.22.1", "next-i18next": "15.0.0", "next-seo": "6.6.0", "nextjs-progressbar": "0.0.16", "postcss": "8.4.24", "prettysize": "2.0.0", "react": "18.2.0", "react-chartjs-2": "5.2.0", "react-dom": "18.2.0", "react-i18next": "12.3.1", "react-icons": "5.5.0", "react-infinite-scroll-component": "6.1.0", "react-intersection-observer": "9.16.0", "react-lite-youtube-embed": "2.5.1", "react-markdown": "8.0.7", "react-query": "^3.39.3", "react-share": "5.2.2", "react-social-icons": "6.24.0", "react-youtube": "10.1.0", "recharts": "2.7.2", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.3.2", "typescript": "5.0.4", "use-debounce": "9.0.4", "zustand": "4.3.9"}}