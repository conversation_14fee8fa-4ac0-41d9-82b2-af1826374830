import { AUTH_TOKEN_NAME } from "@/consts";
import Storage from "@/core/Storage";
import axios from "axios";
import { ResourceService } from "./Resourceful";

export const httpClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_HOST + "/api/v1",
});
export const privateHttpClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_HOST + "/api/v1",
  validateStatus: (status) => status >= 200 && status < 300, // default,
});

privateHttpClient.interceptors.request.use((config) => {
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME);

  if (config.headers && token?.accessToken)
    config.headers["Authorization"] = `Bearer ${token?.accessToken || ""}`;

  return config;
});

const getLeader = (leaderId: string) => httpClient.get(`/leaders/${leaderId}`);
const getLeaders = (params: object) =>
  httpClient.get(`/leaders`, { params: params });
const login = (payload: object) => httpClient.post("/auth/login", payload);
const register = (payload: object) =>
  httpClient.post("/auth/register", payload);

const getProfile = () => privateHttpClient.get("/auth/profile");
const updateProfile = (payload: Record<string, any>) =>
  privateHttpClient.post("/auth/profile", payload);
const review = (
  ratingOn: string,
  ratingOnId: string,
  payload: {
    turnstileToken: string;
    rate: number;
    review?: string;
  }
) =>
  privateHttpClient.post(`/ratings/${ratingOn}/${ratingOnId}`, {
    comment: payload.review,
    value: payload.rate,
    turnstileToken: payload.turnstileToken,
  });

const removeReview = (ratingOnType: string, ratingId: number) =>
  privateHttpClient.delete(`/ratings/${ratingOnType}/${ratingId}`);

const getReviews = (
  ratingOn: string,
  ratingOnId: string,
  filter: {
    page: string;
    limit: number;
  }
) =>
  httpClient.get(`/ratings/${ratingOn}/${ratingOnId}`, {
    params: filter,
  });
const getReviewAnalysis = (ratingOn: string, ratingOnId: string) => {
  return httpClient.get(`/ratings/${ratingOn}/${ratingOnId}/analysis`);
};

const getAnalytics = (entityType: string, query: object = {}) => {
  return httpClient.get(`/${entityType}/analytics`, { params: query });
};
const getAuthAnalytics = (entityType: string) => {
  return privateHttpClient.get(`/${entityType}/analytics`);
};

const getUserReviews = () => {
  return privateHttpClient.get(`/users/my-reviews`);
};

// System Polls API methods
const getSystemPolls = (
  resourceType: string,
  resourceId: string,
  includeUserStatus: boolean = false
) => {
  const params = includeUserStatus ? { includeUserStatus: "true" } : {};
  return httpClient.get(`/polls/${resourceType}/${resourceId}`, { params });
};

const getSystemPollsWithAuth = (
  resourceType: string,
  resourceId: string,
  includeUserStatus: boolean = true
) => {
  const params = includeUserStatus ? { includeUserStatus: "true" } : {};
  return privateHttpClient.get(`/polls/${resourceType}/${resourceId}`, {
    params,
  });
};

const voteOnPoll = (pollId: number, optionIds: number[]) => {
  return privateHttpClient.post(`/polls/${pollId}/vote`, { optionIds });
};

const checkSystemPollsExist = (resourceType: string, resourceId: string) => {
  return httpClient.get(`/polls/${resourceType}/${resourceId}/exists`);
};

const getPollStatistics = (resourceType: string, resourceId: string) => {
  return httpClient.get(`/polls/${resourceType}/${resourceId}/statistics`);
};

const getTrendingPolls = (resourceType?: string, limit: number = 10) => {
  const params: any = { limit };
  if (resourceType) params.resourceType = resourceType;
  return httpClient.get(`/polls/trending`, { params });
};

const forceCreateSystemPolls = (resourceType: string, resourceId: string) => {
  return privateHttpClient.post(`/polls/${resourceType}/${resourceId}/create`);
};

const validateResource = (resourceType: string, resourceId: string) => {
  return httpClient.get(
    `/polls/resource/${resourceType}/${resourceId}/validate`
  );
};

const resource = new ResourceService();

export const ApiService = {
  resource,
  getAuthAnalytics,
  getAnalytics,
  getReviewAnalysis,
  getLeader,
  getProfile,
  login,
  register,
  getLeaders,
  updateProfile,
  review,
  getReviews,
  getUserReviews,
  removeReview,
  // System Polls
  getSystemPolls,
  getSystemPollsWithAuth,
  voteOnPoll,
  checkSystemPollsExist,
  getPollStatistics,
  getTrendingPolls,
  forceCreateSystemPolls,
  validateResource,
};
