import { useTranslation } from "next-i18next";
import { Grid } from "@mantine/core";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IParty } from "@/interfaces/IParty";
import Link from "next/link";
import { EntityInfo } from "@/components/EntityInfo";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Parties = () => {
  const { t } = useTranslation();

  return (
    <DefaultIndexPage<IParty>
      entityType={EntityTypeEnum.Leader}
      resource={"parties"}
      pageTitle={t("common:parties")}
      renderItems={(items: IParty[]) => {
        return (
          <>
            {items.map((party) => (
              <Grid.Col key={party.id} span={{ base: 12, sm: 6, md: 4, lg: 3 }}>
                <EntityInfo
                  linkComponent={Link}
                  resources={"parties"}
                  partyId={party.id + ""}
                  id={party.id + ""}
                  name={party.code}
                  avatar={party.logo}
                  phone={" "}
                  rate={party.rating?.average}
                />
              </Grid.Col>
            ))}
          </>
        );
      }}
    />
  );
};
export default Parties;
