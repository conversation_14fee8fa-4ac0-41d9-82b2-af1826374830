import { useTranslation } from "next-i18next";
import { ILeader } from "@/interfaces/ILeader";
import { formatDateToEnglish, getAgeFromDate } from "@/utils";
import {
  Avatar,
  Badge,
  Table,
  Group,
  Text,
  ActionIcon,
  ScrollArea,
  useMantineTheme,
} from "@mantine/core";
import { IconPencil, IconTrash } from "@tabler/icons-react";
import Link from "next/link";

interface UsersTableProps {
  resource: string;
  data: ILeader[];
}

const jobColors: Record<string, string> = {
  engineer: "blue",
  manager: "cyan",
  designer: "pink",
};

export function EntityTable({ data, ...props }: UsersTableProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const rows = data.map((item) => (
    <Table.Tr key={item.name}>
      <Table.Td>
        <Link
          href={`/${props.resource}/${item.id}`}
          className="no-underline text-inherit"
        >
          <Group gap="sm">
            <Avatar
              size={"md"}
              src={item.img || `/images/leaders/${item.ecCandidateID}.jpg`}
            />
            <Text fz="sm" fw={500}>
              {item.localName}
            </Text>
            <Badge color="gray">{getAgeFromDate(item.birthDate)}</Badge>
          </Group>
        </Link>
      </Table.Td>

      <Table.Td>
        <Group>{formatDateToEnglish(item.birthDate)}</Group>
      </Table.Td>

      <Table.Td>
        <Text fz="sm" c="dimmed">
          {item.address}
        </Text>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <ScrollArea w={"100%"}>
      <Table style={{ minWidth: 800 }} verticalSpacing="sm">
        <Table.Thead>
          <Table.Tr>
            <Table.Th>{t("common:name")}</Table.Th>
            <Table.Th>{t("common:birth_date")}</Table.Th>
            <Table.Th>{t("common:address")}</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    </ScrollArea>
  );
}
