import { truncateString } from "@/utils";
import { Group, Paper, Tabs, Title } from "@mantine/core";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import { useTranslation } from "next-i18next";

const SummaryMD = ({
  summary,
  readMoreLink,
  summaryNP,
}: {
  summary: string;
  summaryNP?: string;
  defaultValue?: string;
  readMoreLink?: string;
}) => {
  const { t, i18n } = useTranslation();
  const lang = i18n.language;

  if (readMoreLink) {
    return (
      <Paper p={"md"}>
        <Title order={3}>{t("common:summary", "Summary")}</Title>
        <ReactMarkdown>
          {readMoreLink
            ? truncateString(lang === "en" ? summary : summaryNP, 500) || ""
            : lang === "en"
            ? summary
            : summaryNP || ""}
        </ReactMarkdown>
        {readMoreLink && (
          <Link href={readMoreLink || ""} className="no-underline">
            <Group align="center" gap={5}>
              {t("common:read_more", "Read more")}
              {">"}
            </Group>
          </Link>
        )}
      </Paper>
    );
  }
  return (
    <Tabs defaultValue={"summary"}>
      <Tabs.List>
        <Tabs.Tab value="summary">{t("common:summary", "Summary")}</Tabs.Tab>
        <Tabs.Tab value="summary_np">
          {t("common:summary_np", "सारांश")}
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="summary">
        <Paper p={"md"}>
          <Title order={3}>Summary</Title>
          <ReactMarkdown>
            {readMoreLink ? truncateString(summary, 500) || "" : summary}
          </ReactMarkdown>
        </Paper>
      </Tabs.Panel>

      <Tabs.Panel value="summary_np">
        <Paper p={"md"}>
          <Title order={3}>सारांश</Title>
          <ReactMarkdown>
            {readMoreLink
              ? truncateString(summaryNP || "", 500) || ""
              : summaryNP || ""}
          </ReactMarkdown>
        </Paper>
      </Tabs.Panel>
    </Tabs>
  );
};

export default SummaryMD;
