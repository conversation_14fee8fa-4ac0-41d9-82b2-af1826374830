import { useCallback, useEffect, useState } from "react";
import {
  Group,
  Burger,
  Box,
  Drawer,
  Stack,
  Button,
  Text,
  useMantineTheme,
  useMantineColorScheme,
  Switch,
  localStorageColorSchemeManager,
  ActionIcon,
  Input,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import classes from "./style.module.css";
import Link from "next/link";
import { useProfile } from "@/store";
import MyAccountMenu from "../MyAccountMenu";
import { useRouter } from "next/router";
import { IconMoonStars, IconSearch, IconSun } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { FaHamburger } from "react-icons/fa";
import { LinksGroup } from "../NavbarLinksGroup";
import SearchBox from "../SearchBox";

interface LinkProps {
  label: string;
  link: string;
}

interface DoubleHeaderProps {
  mainLinks: LinkProps[];
  userLinks: LinkProps[];
  onMainHamClick?: () => void;
  isHamOpened?: boolean;
  onSearchIconClick?: () => void;
}

export function AppHeader({
  mainLinks,
  userLinks,
  isHamOpened: isHamOpenedProp,
  onMainHamClick,
  onSearchIconClick,
}: DoubleHeaderProps) {
  const [opened, { toggle }] = useDisclosure(false);
  const [isHamOpened, { close: closeHamMemu, open: openHamMemu }] =
    useDisclosure(false);
  const user = useProfile((state) => state.profile);
  const theme = useMantineTheme();
  const router = useRouter();
  const { colorScheme } = useMantineColorScheme();
  const colorSchemeManager = localStorageColorSchemeManager({
    key: "my-app-color-scheme",
  });
  const [colorTheme, setColorTheme] = useState(colorScheme);
  const { t, i18n } = useTranslation();
  useEffect(() => {
    closeHamMemu();
  }, [router.pathname]);
  const mainItems = mainLinks.map((item) => (
    // @ts-expect-error
    <LinksGroup
      {...item}
      key={item.label}
      isTopPage={true}
      pathname={router.pathname}
      label={t(item.label)}
    />
  ));

  const additionalMenu = () => {
    return (
      <>
        <Switch
          checked={colorTheme === "dark"}
          onChange={(event) => {
            setColorTheme(event.currentTarget.checked ? "dark" : "light");
            const newColorScheme = event.currentTarget.checked
              ? "dark"
              : "light";
            document.cookie = `mantine-color-scheme=${newColorScheme}; path=/; max-age=********`;
            colorSchemeManager.set(newColorScheme);
            window.location.reload();
          }}
          size="md"
          color="dark.4"
          onLabel={
            <IconSun
              size={16}
              stroke={2.5}
              color="var(--mantine-color-yellow-4)"
            />
          }
          offLabel={
            <IconMoonStars
              size={16}
              stroke={2.5}
              color="var(--mantine-color-blue-6)"
            />
          }
        />
        <Button
          variant="subtle"
          onClick={() => {
            // Add logic to switch language here
            const nextLang = i18n.language === "en" ? "np" : "/";
            i18n.changeLanguage(nextLang);
            window.location.href =
              nextLang === "np" ? `/np` + router.asPath : router.asPath;
          }}
        >
          {i18n.language === "en" ? "ने" : "EN"}
        </Button>
      </>
    );
  };

  const accountMenu = useCallback(
    (user: any) => {
      return (
        <Group>
          <Text c="myColor" fw={"bold"} className="capitalize">
            Hi, {user?.firstName}
            {"!"}
          </Text>
        </Group>
      );
    },
    [user]
  );

  return (
    <header className={classes.header}>
      <div className={classes.inner}>
        <Group wrap="nowrap" gap={2}>
          <Burger
            visibleFrom="md"
            size={"md"}
            opened={false}
            onClick={onMainHamClick}
          />
          <Link href={"/"} className="no-underline">
            <img
              src={
                colorScheme === "dark"
                  ? "/logos/nepaltracks-dark.png"
                  : "/logos/nepaltracks.png"
              }
              alt="nepaltracks"
              width={200}
              height={30}
            />
          </Link>
        </Group>

        <Group gap={50} wrap="nowrap">
          <Box visibleFrom="md" w={"25rem"}>
            <SearchBox />
          </Box>

          {user?.firstName ? (
            <Box visibleFrom="md">{<MyAccountMenu user={user} />}</Box>
          ) : (
            <Group justify="center" align="center" gap={0} visibleFrom="md">
              <Button
                component={Link}
                href="/auth/signin"
                variant="transparent"
                className="rounded-lg"
              >
                {t("common:login")}
              </Button>
            </Group>
          )}
          <Group visibleFrom="md" gap={5} wrap="nowrap">
            <Switch
              checked={colorTheme === "dark"}
              onChange={(event) => {
                setColorTheme(event.currentTarget.checked ? "dark" : "light");
                const newColorScheme = event.currentTarget.checked
                  ? "dark"
                  : "light";
                document.cookie = `mantine-color-scheme=${newColorScheme}; path=/; max-age=********`;
                colorSchemeManager.set(newColorScheme);
                window.location.reload();
              }}
              size="md"
              color="dark.4"
              onLabel={
                <IconSun
                  size={16}
                  stroke={2.5}
                  color="var(--mantine-color-yellow-4)"
                />
              }
              offLabel={
                <IconMoonStars
                  size={16}
                  stroke={2.5}
                  color="var(--mantine-color-blue-6)"
                />
              }
            />

            <Button
              variant="subtle"
              onClick={() => {
                // Add logic to switch language here
                const nextLang = i18n.language === "en" ? "np" : "/";
                i18n.changeLanguage(nextLang);
                window.location.href =
                  nextLang === "np" ? `/np` + router.asPath : router.asPath;
              }}
            >
              {i18n.language === "en" ? "ने" : "EN"}
            </Button>
          </Group>
          <Group wrap="nowrap" gap={3} align="center" justify="center">
            {user?.firstName ? (
              <Box hiddenFrom="md">{<MyAccountMenu user={user} />}</Box>
            ) : null}
            <Box hiddenFrom="md" mt={"2px"}>
              <ActionIcon
                onClick={onSearchIconClick}
                variant="transparent"
                size={"sm"}
              >
                <IconSearch />
              </ActionIcon>
            </Box>{" "}
            <Burger
              ml={"md"}
              opened={opened}
              onClick={openHamMemu}
              className={classes.burger}
              size="sm"
              hiddenFrom="md"
            />
          </Group>
        </Group>
        <Drawer
          opened={isHamOpened}
          onClose={closeHamMemu}
          position="right"
          hiddenFrom="md"
          title={
            <Group>
              <Link href={"/"} className="no-underline">
                <img
                  src={
                    colorScheme === "dark"
                      ? "/logos/nepaltracks-dark.png"
                      : "/logos/nepaltracks.png"
                  }
                  alt="nepaltracks"
                  width={200}
                  height={30}
                />
              </Link>
            </Group>
          }
        >
          <Stack justify="flex-end" gap={20}>
            {user?.firstName ? (
              <Group>
                {accountMenu(user)}
                {additionalMenu()}
              </Group>
            ) : (
              <Group justify="start" hiddenFrom="md" gap={0}>
                <Button
                  component="a"
                  href="/auth/signup"
                  variant="transparent"
                  className="rounded-lg"
                >
                  {t("common:register")}
                </Button>
                /
                <Button
                  component="a"
                  href="/auth/signin"
                  variant="transparent"
                  className="rounded-lg"
                >
                  {t("common:login")}
                </Button>
                {additionalMenu()}
              </Group>
            )}
            <Stack>{mainItems} </Stack>
          </Stack>
        </Drawer>
      </div>
    </header>
  );
}
