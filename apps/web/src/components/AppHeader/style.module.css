.header {
    height: rem(100px);
    margin-bottom: rem(120px);
    background-color: var(--mantine-color-body);
    border-bottom: rem(1px) solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4));
    z-index: 999;
  }
  
  .inner {
    height: rem(100px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--mantine-spacing-sm);
    padding-left: var(--mantine-spacing-lg);
    padding-right: var(--mantine-spacing-lg);
  }
  
  .links {
    height: rem(100px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .mainLinks {
    margin-right: calc(var(--mantine-spacing-sm) * -1);
  }
  
  .mainLink {
    text-transform: uppercase;
    font-size: var(--mantine-font-size-xs);
    color: light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-1));
    padding: rem(7px) var(--mantine-spacing-sm);
    font-weight: 700;
    border-bottom: rem(2px) solid transparent;
    transition:
      border-color 100ms ease,
      color 100ms ease;
  
    @mixin hover {
      color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
      text-decoration: none;
    }
  
    &[data-active] {
      color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
      border-bottom-color: var(--mantine-color-blue-6);
    }
  }
  
  .secondaryLink {
    color: light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-1));
    font-size: var(--mantine-font-size-xs);
    text-transform: uppercase;
    transition: color 100ms ease;
  
    @mixin hover {
      color: light-dark(var(--mantine-color-black), var(--mantine-color-white));
      text-decoration: none;
    }
  }