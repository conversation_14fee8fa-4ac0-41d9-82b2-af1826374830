import { useTranslation } from "next-i18next";
import { IParty } from "@/interfaces/IParty";
import {
  Avatar,
  Center,
  Group,
  LoadingOverlay,
  Pagination,
  ScrollArea,
  Stack,
  Table,
  Text,
} from "@mantine/core";
import Link from "next/link";
import { useMemo, useState } from "react";

export interface IElectionPartyResponse {
  wonPlaces: number;
  voteCount: number;
  runningPlaces: number;
  partyId: number;
  party: IParty;
}
interface PartiesTableProps {
  items: IElectionPartyResponse[];
  isLoading?: boolean;
}

import { TextInput } from "@mantine/core";
import { formatNumber } from "@/utils";

const PartiesTable: React.FC<PartiesTableProps> = (props) => {
  const ITEMS_PER_PAGE = 10;

  const [activePage, setActivePage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");

  const filteredItems = useMemo(() => {
    return props.items.filter((item) =>
      item.party.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery, props.items]);

  const paginatedItems = useMemo(() => {
    const startIndex = (activePage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredItems.slice(startIndex, endIndex);
  }, [activePage, filteredItems]);

  const rows = useMemo(
    () =>
      paginatedItems.map((item) => (
        <Table.Tr key={item.party.id}>
          <Table.Td>
            <Link
              className="no-underline text-inherit"
              href={`/parties/${item.party.id}`}
            >
              <Group gap="sm">
                <Avatar size={"md"} src={item.party.logo} />
                <div>
                  <Text fz="sm" fw={500}>
                    {item.party.localName}
                  </Text>
                </div>
              </Group>
            </Link>
          </Table.Td>

          <Table.Td>
            <Text fz="xs">{item.party.electionSymbol}</Text>
          </Table.Td>
          <Table.Td>
            <Text fz="xs">{formatNumber(item.runningPlaces)}</Text>
          </Table.Td>
          <Table.Td>
            <Text fz="xs">{formatNumber(item.wonPlaces)}</Text>
          </Table.Td>
          <Table.Td>
            <Text fz="xs">
              {formatNumber(item.runningPlaces - item.wonPlaces)}
            </Text>
          </Table.Td>
          <Table.Td>
            <Text fz="xs">{formatNumber(item.voteCount)}</Text>
          </Table.Td>
        </Table.Tr>
      )),
    [paginatedItems]
  );

  const handlePageChange = (page: number) => {
    setActivePage(page);
  };
  const { t } = useTranslation();

  return (
    <Stack gap="md" justify="center">
      <TextInput
        placeholder="Search by party name"
        value={searchQuery}
        onChange={(event) => setSearchQuery(event.currentTarget.value)}
      />
      <ScrollArea>
        <LoadingOverlay visible={props.isLoading} />
        <Table striped miw={800} verticalSpacing="sm">
          <Table.Thead>
            <Table.Tr>
              <Table.Th>{t("common:party")}</Table.Th>
              <Table.Th>Symbol</Table.Th>
              <Table.Th>{t("common:constituencies")}</Table.Th>
              <Table.Th>{t("common:wins")}</Table.Th>
              <Table.Th>{t("common:loss")}</Table.Th>
              <Table.Th>{t("common:total_votes")}</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
      <Center>
        <Pagination
          total={Math.ceil(filteredItems.length / ITEMS_PER_PAGE)}
          onChange={handlePageChange}
        />
      </Center>
    </Stack>
  );
};
export default PartiesTable;
