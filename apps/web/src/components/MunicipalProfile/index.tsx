import { useTranslation } from "next-i18next";
import { IGovernment } from "@/interfaces/IGovernment";
import { EntityInfo } from "../EntityInfo";
import { getImageUrlWithFallback } from "@/utils";
import { Badge, Group } from "@mantine/core";
import { IMunicipals } from "@/interfaces/IElectionSubResponse";

const MunicipalProfile = ({ data }: { data: IMunicipals }) => {
  const { t } = useTranslation();
  return (
    <EntityInfo
      title={data.name}
      avatar="https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
      cardProps={{ mih: "160px" }}
      resources="municipals"
      id={data.id + ""}
      noShadow
      name={data.localName || data.name}
      address={data.districts?.localName + ", " + data.states?.localName}
    />
  );
};
export default MunicipalProfile;
