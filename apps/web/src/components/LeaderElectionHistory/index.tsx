import React from "react";
import { Card, CardSection, Text, Badge, Group } from "@mantine/core";
import { Icon<PERSON>heck, IconX } from "@tabler/icons-react";
import { ILeader } from "@/interfaces/ILeader";
import Link from "next/link";
import { IElections } from "@/interfaces/IElectionSubResponse";
import DistanceFromNow from "../DistanceFromNow";

type ElectionEntry = {
  elections: IElections;
  area: string;
  states: { localName: string };
  districts: { localName: string };
  voteCount: number;
  isElected: boolean;
  elCode: string;
  candidacyTypeId: number;
};

type LeaderData = {
  leaders: ILeader;
  previousElections: ElectionEntry[];
};

type Props = {
  leader: LeaderData;
};

const LeaderElectionHistory: React.FC<Props> = ({ leader }) => {
  console.log(leader);
  return (
    <Card shadow="md" padding="lg" radius="md">
      {leader.previousElections.length === 0 ? (
        <Text size="sm" color="dimmed">
          No previous election data available.
        </Text>
      ) : (
        <div className="space-y-4 mt-2">
          {leader.previousElections.map((election, idx) => (
            <Link
              target="_blank"
              // @ts-expect-error
              href={`/elections/${election.elections.id}/sub/${election.elCode}/${election.candidacyType.id}`}
              className="no-underline text-inherit mt-10"
            >
              <Card key={idx} padding="md" shadow="xs" mt={10}>
                <Group justify="apart">
                  <div>
                    <Text size="sm" className="font-medium">
                      {election.elections.name}
                    </Text>
                    <Text size="sm" color="dimmed">
                      Area: {election.districts.localName}-{election.area},{" "}
                      {election.states.localName}
                    </Text>
                    <Text size="sm" color="dimmed">
                      Votes: {election.voteCount}
                    </Text>
                    <Text size="sm" color="dimmed">
                      <DistanceFromNow
                        startedAt={election.elections.year}
                        addDateAtEnd
                      />
                    </Text>
                  </div>
                  <Badge
                    color={election.isElected ? "green" : "red"}
                    variant="light"
                    leftSection={
                      election.isElected ? (
                        <IconCheck size={14} />
                      ) : (
                        <IconX size={14} />
                      )
                    }
                  >
                    {election.isElected ? "Elected" : "Not Elected"}
                  </Badge>
                </Group>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </Card>
  );
};

export default LeaderElectionHistory;
