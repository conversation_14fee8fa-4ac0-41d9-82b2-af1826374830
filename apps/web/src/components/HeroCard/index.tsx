import {
  Paper,
  Text,
  Title,
  Rating,
  AspectRatio,
  Stack,
  PaperProps,
  Overlay,
  BackgroundImage,
  Image,
  Box,
  Badge,
} from "@mantine/core";
import classes from "./style.module.css";
import { ReactNode } from "react";

export interface HeroCardProps {
  image: string | React.ReactNode;
  title: string | React.ReactNode;
  category?: string | React.ReactNode;
  more?: string | React.ReactNode;
  rating?: number;
  paperStyle?: PaperProps;
  renderTopLeftFloat?: () => React.ReactNode;
}

const BlurredBackgroundImage = ({
  children,
  image,
}: {
  children: ReactNode;
  image: string;
}) => {
  return (
    <>
      <BackgroundImage
        src={image}
        radius={"md"}
        className={"flex items-center justify-center h-[200px] md:h-[450px]"}
      >
        {children}
      </BackgroundImage>
      <Overlay backgroundOpacity={0.05} blur={10} className="z-0 rounded-md" />
    </>
  );
};

export function HeroCard({
  paperStyle,
  image,
  title,
  category,
  ...props
}: HeroCardProps) {
  return (
    <AspectRatio ratio={16 / 9} className={paperStyle?.className}>
      <BlurredBackgroundImage image={`${image}`} key={image as string}>
        <Stack>
          <picture className="relative z-50">
            <Image
              className="w-[300px] h-[250px]"
              radius={"md"}
              fallbackSrc={"/images/ecat-fallback.webp"}
              src={image}
              fit="contain"
              alt={`Image for ${image} plan.`}
            />
          </picture>
          <Box pos={"absolute"} left={5} top={5} className="z-50">
            {props.renderTopLeftFloat?.()}
          </Box>
          <Stack
            justify="end"
            h={"100%"}
            gap={5}
            align="center"
            pl={"lg"}
            p={"lg"}
            className="rounded-md  z-50 absolute md:relative left-0 bottom-0"
          >
            {props.rating ? (
              <Rating value={props.rating || 0} readOnly size={"xs"} />
            ) : null}
            <Title order={5} className={classes.title} c={"white"}>
              {title}
            </Title>

            {props.more || ""}
            {category ? (
              <Text className={classes.category} size="xs">
                {category || ""}
              </Text>
            ) : null}
          </Stack>
        </Stack>
      </BlurredBackgroundImage>
    </AspectRatio>
  );
  return (
    <AspectRatio ratio={16 / 9} className={paperStyle?.className}>
      <Paper
        shadow="md"
        p="xl"
        radius="md"
        bg={`url(${image})`}
        // className="bg-no-repeat bg-cover bg-center"
        bgp={"center"}
        bgr={"no-repeat"}
        {...paperStyle}
      ></Paper>
      <Overlay color="#000" radius="md" backgroundOpacity={0.25} zIndex={0.5} />
      <Stack
        justify="end"
        h={"100%"}
        gap={2}
        align="left"
        pl={"lg"}
        p={"lg"}
        className="rounded-md"
      >
        {props.rating ? (
          <Rating value={props.rating || 0} readOnly size={"xs"} />
        ) : null}
        <Title order={5} className={classes.title} c={"white"}>
          {title}
        </Title>
        {category ? (
          <Text className={classes.category} size="xs">
            {category || ""}
          </Text>
        ) : null}
        {props.more || ""}
      </Stack>
    </AspectRatio>
  );
}
