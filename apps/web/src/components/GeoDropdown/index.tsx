import { Grid, Group, Select, SimpleGrid, Stack } from "@mantine/core";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { useEffect, useState } from "react";
import { IParty } from "@/interfaces/IParty";
import { useGlobalState } from "@/store";
import { IUserAddress } from "@/interfaces/IUserAddress";
import { useTranslation } from "react-i18next";

type ResponseType = {
  stateId?: string | null;
  districtId?: string | null;
  municipalId?: string | null;
  wardId?: string | null;
  partyId: string | null;
  electionId?: string | null;
  candidacyTypeId?: string;
  area?: string;
};
interface IProps {
  enableParty?: boolean;
  enableElection?: boolean;
  onChange?: (params: ResponseType) => void;
  defaultValue?: IUserAddress;
  readonly?: boolean;
}

const areas = [
  { label: "1", value: "1" },
  { label: "1-1", value: "1-1" },
  { label: "1-2", value: "1-2" },
  { label: "2", value: "2" },
  { label: "2-1", value: "2-1" },
  { label: "2-2", value: "2-2" },
  { label: "3", value: "3" },
  { label: "3-1", value: "3-1" },
  { label: "3-2", value: "3-2" },
  { label: "4", value: "4" },
  { label: "4-1", value: "4-1" },
  { label: "4-2", value: "4-2" },
  { label: "5", value: "5" },
  { label: "5-1", value: "5-1" },
  { label: "5-2", value: "5-2" },
  { label: "6", value: "6" },
  { label: "6-1", value: "6-1" },
  { label: "6-2", value: "6-2" },
  { label: "7", value: "7" },
  { label: "7-1", value: "7-1" },
  { label: "7-2", value: "7-2" },
];

const GeoDropdown = ({ readonly, ...props }: IProps) => {
  const [election, setElection] = useState<string | null>(
    props.defaultValue?.electionId || ""
  );

  const [candidacyType, setCandidacyType] = useState<string | null>(
    props.defaultValue?.candidacyTypeId || ""
  );

  const [state, setState] = useState<string | null>(
    props.defaultValue?.stateId || ""
  );
  const [district, setDistrict] = useState<string | null>(
    props.defaultValue?.districtId || ""
  );
  const [ward, setWard] = useState<string | null>(
    props.defaultValue?.wardId || ""
  );
  const [municipals, setMunicipals] = useState<string | null>(
    props.defaultValue?.municipalityId || ""
  );
  const [area, setArea] = useState<string | null>("");
  const [party, setParty] = useState<string | null>("");

  useEffect(() => {
    setState(
      props.defaultValue?.stateId ? String(props.defaultValue.stateId) : ""
    );
    setDistrict(
      props.defaultValue?.districtId
        ? String(props.defaultValue.districtId)
        : ""
    );
    setWard(
      props.defaultValue?.wardId ? String(props.defaultValue.wardId) : ""
    );
    setMunicipals(
      props.defaultValue?.municipalityId
        ? String(props.defaultValue.municipalityId)
        : ""
    );
  }, [props.defaultValue]);

  const toggleLoading = useGlobalState((s) => s.toggleLoading);

  const statesQuery = useQuery("states", async () => {
    const { data } = await ApiService.resource.getAll("geo-states", {});
    return data;
  });
  const districtsQuery = useQuery(
    "districtsQuery" + state,
    async () => {
      const { data } = await ApiService.resource.getAll(
        `geo-states/${state}/districts`,
        {}
      );
      return data;
    },
    {
      enabled: !!state,
    }
  );
  //   const wardsQuery = useQuery("wardsQuery", () => {});
  const municipalsQuerys = useQuery(
    "municipalsQuerys" + district,
    async () => {
      const { data } = await ApiService.resource.getAll(
        `geo-districts/${district}/municipals`,
        {}
      );
      return data;
    },
    {
      enabled: !!district,
    }
  );
  //   const wardsQuery = useQuery("wardsQuery", () => {});
  const wardsQuery = useQuery(
    "wardsQuery" + district + municipals,
    async () => {
      const { data } = await ApiService.resource.getAll(
        `geo-municipals/${municipals}/wards`,
        {}
      );
      return data;
    },
    {
      enabled: !!municipals,
    }
  );

  const electionsQuery = useQuery(
    "electionsQuery",
    async () => {
      const { data } = await ApiService.resource.getAll(`elections`, {});
      return data;
    },
    {
      enabled: props.enableElection,
    }
  );

  const candidacyTypesQuery = useQuery("candidacyQuery", async () => {
    const { data } = await ApiService.resource.getAll(
      `elections/candidacy-types`,
      {}
    );
    return data;
  });

  //   const wardsQuery = useQuery("wardsQuery", () => {});
  const partiesQuery = useQuery("parties", async () => {
    const { data } = await ApiService.resource.getAll(`parties`, {
      limit: 3000,
    });
    return data;
  });

  useEffect(() => {
    props?.onChange?.({
      candidacyTypeId: candidacyType || undefined,
      area: area || undefined,
      electionId: election || undefined,
      districtId: district || undefined,
      municipalId: municipals || undefined,
      stateId: state || undefined,
      wardId: ward || undefined,
      partyId: party,
    });
  }, [state, district, municipals, party, election, ward, candidacyType, area]);

  useEffect(() => {
    const isLoading =
      districtsQuery.isLoading ||
      municipalsQuerys.isLoading ||
      wardsQuery.isLoading ||
      partiesQuery.isLoading ||
      statesQuery.isLoading;

    toggleLoading(isLoading);
  }, [
    districtsQuery.isLoading,
    municipalsQuerys.isLoading,
    wardsQuery.isLoading,
    partiesQuery.isLoading,
    statesQuery.isLoading,
  ]);
  const handleValueChange = (
    type:
      | "state"
      | "district"
      | "municipal"
      | "ward"
      | "election"
      | "candidacyType"
      | "area"
  ) => {
    return (value: string | null) => {
      switch (type) {
        case "candidacyType":
          setCandidacyType(value);
          break;
        case "area":
          setArea(value);
          break;
        case "election":
          setElection(value);
          break;
        case "state":
          setState(value);
          setDistrict(null);
          setWard(null);
          setMunicipals(null);
          break;
        case "district":
          setDistrict(value);
          setWard(null);
          setMunicipals(null);
          break;
        case "municipal":
          setMunicipals(value);
          setWard(null);
          break;
        case "ward":
          setWard(value);
          break;
        default:
          break;
      }
    };
  };
  const { t } = useTranslation();
  return (
    <SimpleGrid cols={{ xs: 1, sm: 3, md: 4, lg: 4 }} w={"100%"}>
      <Select
        readOnly={readonly}
        clearable
        searchable
        value={state}
        onChange={(value) => handleValueChange("state")(value)}
        label={t("common:state", {
          defaultValue: "State",
        })}
        // @ts-expect-error
        data={statesQuery.data?.map?.((item) => ({
          label: ` ${item.localName} ( ${item.name})`,
          value: item.id + "",
        }))}
        allowDeselect={false}
      />
      <Select
        readOnly={readonly}
        searchable
        clearable
        value={district}
        defaultValue={district + ""}
        onChange={(value) => handleValueChange("district")(value)}
        label={t("common:district", {
          defaultValue: "District",
        })}
        // @ts-expect-error
        data={districtsQuery.data?.districts?.map?.((item) => ({
          label: ` ${item.localName} ( ${item.name})`,
          value: item.id + "",
        }))}
        allowDeselect={false}
      />
      <Select
        readOnly={readonly}
        searchable
        clearable
        defaultValue={municipals + ""}
        value={municipals}
        onChange={(value) => handleValueChange("municipal")(value)}
        label={
          t("common:municipal", {
            defaultValue: "Municipal",
          }) || "Municipal"
        }
        // @ts-expect-error
        data={municipalsQuerys.data?.municipal?.map?.((item) => ({
          label: ` ${item.localName} ( ${item.name})`,
          value: item.id + "",
        }))}
        allowDeselect={false}
      />
      <Select
        w={"100%"}
        readOnly={readonly}
        searchable
        clearable
        value={ward}
        defaultValue={ward + ""}
        onChange={(value) => handleValueChange("ward")(value)}
        label={
          t("common:ward", {
            defaultValue: "Ward",
          }) || "Ward"
        }
        // @ts-expect-error
        data={wardsQuery.data?.wards?.map?.((item) => ({
          label: item.localName === "null" ? "N/A" : item.localName,
          value: item.localName + "",
        }))}
        allowDeselect={false}
      />
      {/* <Select
        w={"100%"}
        searchable
        clearable
        value={ward}
        defaultValue={ward + ""}
        onChange={(value) => handleValueChange("ward")(value)}
        label="Ward"
        // @ts-expect-error
        data={wardsQuery.data?.wards?.map?.((item) => ({
          label: item.localName === "null" ? "N/A" : item.localName,
          value: item.localName + "",
        }))}
        allowDeselect={false}
      /> */}

      <Select
        readOnly={readonly}
        clearable
        searchable
        value={candidacyType}
        onChange={(value) => handleValueChange("candidacyType")(value)}
        label={t("common:candidacy-types", {
          defaultValue: "Candidacy Types",
        })}
        // @ts-expect-error
        data={candidacyTypesQuery.data?.map?.((item) => ({
          label: ` ${item.localName} ( ${item.name})`,
          value: item.id + "",
        }))}
        allowDeselect={false}
      />

      <Select
        readOnly={readonly}
        clearable
        searchable
        value={area}
        onChange={(value) => handleValueChange("area")(value)}
        label={t("common:area", {
          defaultValue: "Area",
        })}
        data={areas}
        allowDeselect={false}
      />

      {props.enableParty && (
        <Select
          readOnly={readonly}
          w={"100%"}
          searchable
          clearable
          value={party}
          onChange={setParty}
          label={t("common:party", {
            defaultValue: "Party",
          })}
          data={partiesQuery.data?.items?.map?.((item: any) => ({
            label: ` ${item.localName} ( ${item.name})`,
            value: item.id + "",
          }))}
          allowDeselect={false}
        />
      )}
      {props.enableElection && (
        <Select
          readOnly={readonly}
          clearable
          searchable
          value={election}
          onChange={(value) => handleValueChange("election")(value)}
          label={t("common:elections", {
            defaultValue: "Elections",
          })}
          // @ts-expect-error
          data={electionsQuery.data?.map?.((item) => ({
            label: ` ${item.localName} ( ${item.name})`,
            value: item.id + "",
          }))}
          allowDeselect={false}
        />
      )}
    </SimpleGrid>
  );
};
export default GeoDropdown;
