import { IElectionResult } from "@/interfaces/IElections";
import { IElections } from "@/interfaces/IElectionSubResponse";
import {
  Avatar,
  Badge,
  Group,
  Indicator,
  Stack,
  Text,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { EntityInfo } from "../EntityInfo";
import {
  formatConstituencyAddress,
  formatDateToNepali,
  getImageUrlWithFallback,
} from "@/utils";
import LeaderProfile from "../LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import LeaderHeroProfile from "../LeaderHeroProfile";
import Link from "next/link";

const ElectionSubProfile = ({
  data,
  profileType,
  additionalInformationEnabled,
}: {
  data: IElectionResult;
  profileType?: "Leader" | "Party" | "Election";
  additionalInformationEnabled?: boolean;
}) => {
  if (!data) return null;
  const {
    districts,
    municipals,
    second,
    ward,
    leaders,
    area,
    elections,
    ...el
  } = data;
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();

  if (profileType === "Leader") {
    return (
      <LeaderHeroProfile
        additionalInformationEnabled={additionalInformationEnabled}
        data={leaders as ILeader}
        renderDescription={() => (
          <Stack gap={5}>
            <Text size="xs">
              {formatConstituencyAddress({
                municipal: municipals,
                district: districts,
                area: area,
                ward: ward,
              })}
            </Text>

            <Text size="xs">
              {data.candidacyType.localName}, {data.elections.name}
            </Text>
          </Stack>
        )}
      />
    );
  }
  if (!elections) return null;
  return (
    <Link
      href={`/elections/${elections.id}/sub/${data.elCode}/${data.candidacyTypeId}`}
      className="no-underline text-inherit"
    >
      <EntityInfo
        paperProps={{
          // className: "shadow-md md:shadow-none",
          h: "10rem",
          bg:
            colorScheme === "dark"
              ? theme.colors.dark[6]
              : theme.colors.gray[1],
        }}
        disableLink
        rate={data.rating?.average}
        titleTextAlign="start"
        name={leaders?.localName + " (" + data.candidacyType.localName + ")"}
        avatar={
          <Avatar.Group>
            <Indicator
              inline
              zIndex={1}
              size={16}
              offset={8}
              position="top-start"
              color="green"
              withBorder
              label={data.voteCount.toLocaleString()}
            >
              <Avatar
                size={"lg"}
                src={getImageUrlWithFallback(
                  leaders?.img,
                  //@ts-expect-error
                  leaders?.ecCandidateID
                )}
              />
            </Indicator>

            <Indicator
              zIndex={1}
              inline
              size={16}
              offset={8}
              position="top-end"
              color="gray"
              withBorder
              label={second?.voteCount?.toLocaleString()}
            >
              <Avatar
                size={"lg"}
                src={getImageUrlWithFallback(
                  second?.leaders?.img,
                  // @ts-expect-error
                  second?.leaders?.ecCandidateID
                )}
              />
            </Indicator>
          </Avatar.Group>
        }
        address={
          <>
            {formatConstituencyAddress({
              municipal: municipals,
              district: districts,
              area: area,
              ward: ward,
            })}
          </>
        }
        title={elections.name}
      />
    </Link>
  );
};
export default ElectionSubProfile;
