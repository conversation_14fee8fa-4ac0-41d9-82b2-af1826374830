import {
  IParliamentMemberInfo,
  IParliamentPartyInfo,
} from "@/interfaces/IParliamentOverview";
import { getImageUrlWithFallback } from "@/utils";
import { Avatar, Badge, Group, Stack, Text } from "@mantine/core";

const ParliamentParty = ({
  onSeeMoreMembersClick,
  parliamentInfo: {
    party,
    directMembersCount,
    proportionalMembersCount,
    totalMembersCount,
    members,
  },
}: {
  parliamentInfo: IParliamentPartyInfo;
  onSeeMoreMembersClick: (members: IParliamentMemberInfo[]) => void;
}) => {
  return (
    <div
      key={party.localName}
      className="flex  shadow bg-white items-center p-2 rounded-lg flex-col gap-1 justify-between"
    >
      <Stack gap={2} justify="center" align="center">
        <Avatar
          size={"sm"}
          src={getImageUrlWithFallback(party.logo, party.id + "")}
          radius={100}
        />
        <div className="font-semibold text-gray-800 text-center text-xs  md:text-md">
          {party.localName}
        </div>
      </Stack>
      <Group justify="center" mt={5} gap={3}>
        <Badge size="xs" radius={"xs"} color="green">
          Direct: {directMembersCount}
        </Badge>
        <Badge size="xs" radius={"xs"} color="lime">
          Proportional: {proportionalMembersCount}
        </Badge>
      </Group>
      <Text size="sm" className={"text-gray-800 "}>
        <b>{totalMembersCount}</b> Seats
      </Text>
      <Avatar.Group onClick={() => onSeeMoreMembersClick(members)}>
        {members.slice(0, 3).map((member) => (
          <Avatar
            alt={member.leaders.localName}
            size={"sm"}
            key={member.leaders.id}
            src={getImageUrlWithFallback(
              member.leaders.img,
              member.leaders.ecCandidateID + ""
            )}
            radius={100}
          />
        ))}
        {members.length > 3 && (
          <Avatar size={"sm"}>+{members.length - 3}</Avatar>
        )}
      </Avatar.Group>
    </div>
  );
};
export default ParliamentParty;
