import {
  Combobox,
  useCombobox,
  Input,
  InputBase,
  ScrollArea,
  ComboboxOption,
  Loader,
  InputProps,
  ComboboxStore,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import { ApiService } from "../../../api";
import { useQuery } from "react-query";
import { useRouter } from "next/router";
import { IconSearch } from "@tabler/icons-react";
import RenderResourceType from "@/containers/RenderResourceType";
import { EntityInfo } from "../EntityInfo";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getImageUrlWithFallback, getLinkOfResource } from "@/utils";

const SearchBox = (props: {
  focus?: boolean;
  inputProps?: InputProps;
  onSelect?: (item: any, store: ComboboxStore) => boolean;
  entities?: EntityTypeEnum[];
}) => {
  const { t } = useTranslation();
  const [search, setSearch] = useState("");
  const router = useRouter();

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const handleSearch = (value: string) => {
    if (value.length > 0 && value.length < 3) return;
    setSearch(value);
  };

  const debouncedCallback = useDebouncedCallback(handleSearch, 1000);

  const resourceQuery = useQuery(
    ["search", search],
    async () => {
      const response = await ApiService.resource.getAll(`search`, {
        search,
        entities: props.entities?.join?.(","),
      });
      return response?.data;
    },
    { enabled: search.length >= 3 }
  );

  const mapIdToContent = useMemo(() => {
    if (!resourceQuery.data) return {};
    return resourceQuery.data?.items?.reduce((acc, cur) => {
      //@ts-expect-error
      const key = `${cur.resourceType}-${cur.resource?.id}`;
      //@ts-expect-error
      acc[key] = cur;
      return acc;
    }, {});
  }, [resourceQuery.data]);

  const options = useMemo(() => {
    return Object.keys(mapIdToContent || {});
  }, [mapIdToContent]);

  const handleOptionSubmit = (val: string) => {
    //@ts-expect-error
    const item = mapIdToContent[val];
    if (item) {
      // You can route to detail or handle selection
      const link = getLinkOfResource(item.resourceType, item.resource);
      if (!link) return;

      if (props.onSelect) {
        props.onSelect?.(item.resource, combobox);
        if (inputRef.current) inputRef.current.value = "";
        setSearch("");
        combobox.closeDropdown();
        return;
      }
      console.log(item.resource, item.resourceType);
      router.push(link);
    }
  };

  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (!props.focus) return;
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, [props.focus]);

  return (
    <Combobox
      store={combobox}
      onOptionSubmit={handleOptionSubmit}
      withinPortal={false}
    >
      <Combobox.Target>
        <Input
          type="text"
          ref={inputRef}
          radius={"sm"}
          w={{ base: "100%", md: "100%" }}
          size="md"
          leftSection={<IconSearch />}
          placeholder={t("common:search", { defaultValue: "Search" })}
          onChange={(event) => {
            combobox.openDropdown();
            debouncedCallback(event.target.value);
          }}
          {...props.inputProps}
        />
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>
          <ScrollArea.Autosize mah={300}>
            {options.length > 0 ? (
              options.map((key) => {
                //@ts-expect-error
                const item = mapIdToContent[key];
                if (!item.resource) return null;
                return (
                  <Combobox.Option value={key} key={key}>
                    <EntityInfo
                      highlight={search}
                      disableLink
                      name={
                        item.resource.locaName ||
                        item.resource.name ||
                        item.resource.title
                      }
                      address={
                        item.resource.address ||
                        item.resource.description ||
                        item.resource.slogan ||
                        item.resource.motto ||
                        ""
                      }
                      avatar={
                        item.resourceType === EntityTypeEnum.Leader
                          ? getImageUrlWithFallback(
                              item.resource.img,
                              item.resource.ecCandidateID
                            )
                          : item.resource.avatar ||
                            item.resource.img | item.resource.image ||
                            item.resource.logo ||
                            "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
                      }
                    />
                    {/* <RenderResourceType
                      resource={item.resource}
                      resourceType={item.resourceType}
                    /> */}
                  </Combobox.Option>
                );
              })
            ) : (
              <Combobox.Empty>
                {resourceQuery.isLoading ? (
                  <Loader />
                ) : (
                  t("common:no_results", { defaultValue: "No results" })
                )}
              </Combobox.Empty>
            )}
          </ScrollArea.Autosize>
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default SearchBox;
