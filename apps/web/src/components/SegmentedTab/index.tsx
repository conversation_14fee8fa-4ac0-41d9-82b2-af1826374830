import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo, useState } from "react";
import {
  ActionIcon,
  Group,
  Menu,
  Paper,
  SegmentedControlItem,
  Tabs,
  TabsProps,
  UnstyledButton,
  useMantineTheme,
} from "@mantine/core";
import { NextRouter, useRouter } from "next/router";
import { IconDotsVertical } from "@tabler/icons-react";
import { useMediaQuery } from "@mantine/hooks";

function SegmentedTab(
  props: Pick<TabsProps, "variant" | "defaultValue"> & {
    data: Array<
      (SegmentedControlItem & { content: React.ReactNode }) | undefined
    >;
    resources: string;
    entityId: string;
    disableRouting?: boolean;
    onTabChange?: (tab: string, router: NextRouter) => void;
  }
) {
  const { t } = useTranslation();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<any>(
    props.defaultValue || router.query.tab
  );
  const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [controlsRefs, setControlsRefs] = useState<
    Record<string, HTMLButtonElement | null>
  >({});
  const [active, setActive] = useState(0);
  const setControlRef = (index: number) => (node: HTMLButtonElement) => {
    controlsRefs[index] = node;
    setControlsRefs(controlsRefs);
  };
  const handleTabChange = (index: any) => {
    setActiveTab(index);
    if (props.onTabChange) {
      props.onTabChange(index, router);
      return;
    }
    if (!props.disableRouting) {
      router.push(`/${props.resources}/${props.entityId}/${index}`, undefined, {
        scroll: false,
      });
    }
  };
  useEffect(() => {
    if (props.disableRouting) return;
    setActiveTab(router.query.tab);
  }, [router.query.tab]);

  const theme = useMantineTheme();
  const mobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);

  // todo: make this dynamic
  const threshold = mobile ? 2 : 6;
  const dataMemo = useMemo(() => {
    return props.data.filter((item) => item != undefined);
  }, [props.data]);
  return (
    <div>
      <Paper p={"md"} mt={10} shadow="xs">
        <Tabs
          keepMounted={false}
          onChange={handleTabChange}
          value={activeTab as string}
          variant={"default"}
        >
          <Tabs.List justify="space-between" className="flex flex-nowrap">
            <Group wrap="nowrap">
              {dataMemo.map((item, index) => {
                return (
                  <Tabs.Tab
                    key={index}
                    value={item.value}
                    display={index > threshold ? "none" : "flex"}
                  >
                    {t(`common:${item.label}`, {
                      defaultValue: item.label,
                    })}
                  </Tabs.Tab>
                );
              })}
            </Group>
            {dataMemo.length >= threshold && (
              <Menu>
                <Menu.Target>
                  <ActionIcon variant={"transparent"}>
                    <IconDotsVertical />
                  </ActionIcon>
                </Menu.Target>

                <Menu.Dropdown>
                  {dataMemo.slice(threshold + 1).map((item, index) => {
                    return (
                      <Menu.Item
                        key={index}
                        value={item.value}
                        onClick={() => handleTabChange(item.value)}
                      >
                        {t(`common:${item.label}`, {
                          defaultValue: item.label,
                        })}
                      </Menu.Item>
                    );
                  })}
                </Menu.Dropdown>
              </Menu>
            )}
          </Tabs.List>

          {dataMemo.map((item, index) => (
            <Tabs.Panel key={index} value={item.value} mt={10}>
              {item.content}
            </Tabs.Panel>
          ))}
        </Tabs>
      </Paper>
    </div>
  );
}

export default SegmentedTab;
