import { formatNumber } from "@/utils";
import { <PERSON><PERSON>, Card, Collapse } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";

const DistrictCard = ({ title, district, icon }: any) => {
  const [opened, { toggle }] = useDisclosure(false);
  const { t } = useTranslation();
  return (
    <Card className="shadow-xl mb-4 overflow-scroll" h={"100%"} mah={"30rem"}>
      <Card.Section className="p-6">
        <div className="flex items-center gap-2 mb-4">
          {icon}
          <h2 className="text-xl font-bold">
            {title}: {district.localName}
          </h2>
          <Button
            onClick={toggle}
            className="ml-auto text-sm font-bold"
            variant="subtle"
          >
            {opened ? t("common:hide", "Hide") : t("common:show", "Show")}
          </Button>
        </div>
        <Collapse in={opened}>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              {t("common:wins", { defaultValue: "Wins" })}:{" "}
              <span className="font-semibold">{district.wins}</span>
            </div>
            <div>
              {t("common:losses", { defaultValue: "Losses" })}:{" "}
              <span className="font-semibold">{district.losses}</span>
            </div>
            <div>
              {t("common:votes_received", { defaultValue: "Votes Received" })}:{" "}
              <span className="font-semibold">
                {formatNumber(district.votesRecieved)} (
                {district.voteReceivedPercentage.toFixed(2)}%)
              </span>
            </div>
            <div>
              {t("common:total_votes", { defaultValue: "Total Votes" })}:{" "}
              <span className="font-semibold">
                {formatNumber(district.allTotalVotes)}
              </span>
            </div>

            <div>
              {t("common:win_percent", { defaultValue: "Win Percent" })}%:{" "}
              <span className="font-semibold">
                {district.winPercentage.toFixed(2)}%
              </span>
            </div>
            <div>
              {t("common:loss_percent", { defaultValue: "Loss Percent" })}%:{" "}
              <span className="font-semibold">
                {district.lossPercentage.toFixed(2)}%
              </span>
            </div>

            <div>
              {t("common:state", { defaultValue: "State" })}:{" "}
              <span className="font-semibold">
                {district.topPerformer.states.localName}
              </span>
            </div>
            <div>
              {t("common:top_performer_area", {
                defaultValue: "Top Performer Area",
              })}
              :{" "}
              <span className="font-semibold">
                {district.topPerformer.area}
              </span>
            </div>
          </div>
          <div className="mt-4">
            <h3 className="font-semibold mb-2">
              {t("common:elections", "Elections")}
            </h3>
            <ul className="space-y-1">
              {district.elections.map((e: any, idx: any) => (
                <Link
                  href={`/elections/${e.electionId}/sub/${e.elCode}/${e.candidacyTypeId}`}
                  className="text-inherit no-underline hover:underline"
                >
                  <li key={idx} className="flex items-center gap-2">
                    {e.isElected ? (
                      // @ts-expect-error
                      <FaCheckCircle className="text-green-500" />
                    ) : (
                      // @ts-expect-error
                      <FaTimesCircle className="text-red-500" />
                    )}
                    <span>
                      {t("common:area", { defaultValue: "Area" })} {e.area} -{" "}
                      {formatNumber(e.voteCount)}{" "}
                      {t("common:votes", { defaultValue: "Votes" })}
                    </span>
                  </li>
                </Link>
              ))}
            </ul>
          </div>
        </Collapse>
      </Card.Section>
    </Card>
  );
};
export default DistrictCard;
