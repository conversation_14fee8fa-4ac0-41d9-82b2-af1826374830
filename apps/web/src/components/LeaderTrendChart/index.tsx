import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LabelList,
} from "recharts";
import { Box, Text, Avatar, Group } from "@mantine/core";
import { getImageUrlWithFallback } from "@/utils";
import { IconCheck, IconCross, IconX } from "@tabler/icons-react";

export type LeaderTrend = {
  electionYear: number;
  voteCount: number;
  voteDelta: number | null;
  voteTrend: "rise" | "fall" | "same" | null;
  seatWon: 0 | 1;
  partyName: string;
  partyId: number;
  partyLogo?: string;
  districtName: string;
  constituencyNumber: string;
};

export type LeaderTrendData = {
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  img?: string;
  trends: LeaderTrend[];
};

type Props = {
  data: LeaderTrendData[]; // multiple leaders
};

const getTrendArrow = (trend: "rise" | "fall" | "same" | null) => {
  if (trend === "rise") return "▲";
  if (trend === "fall") return "▼";
  if (trend === "same") return "–";
  return "";
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) return null;

  const sortedPayload = payload.sort((a: any, b: any) => b.value - a.value);
  return (
    <Box bg="white" p={10} className="text-black">
      <Text fw={600}>Election Year: {label}</Text>
      {sortedPayload.map((entry: any) => {
        const d = Object.values(entry.payload).find(
          (item) =>
            // @ts-expect-error
            item.leaderName === entry.name && item.voteCount === entry.value
        );
        return (
          <Group key={entry.dataKey} mt={4}>
            <Avatar
              // @ts-expect-error
              src={d?.partyLogo}
              size="sm"
              radius="xl"
              maw={30}
              mah={30}
            />
            <Text fw={500}>{entry.name}</Text>
            <Text size="sm" c="dimmed">
              Votes: {entry.value} {/* @ts-ignore */}
              {d.seatWon ? (
                <IconCheck size={12} color="green" />
              ) : (
                <IconX size={12} />
              )}
            </Text>
            {/* <Text size="sm">
              District: {d.districtName} ({d.constituencyNumber})
            </Text>
            <Text size="sm">Party: {d.partyName}</Text>
            <Text size="sm">Result: {d.seatWon === 1 ? "Won" : "Lost"}</Text>  */}
          </Group>
        );
      })}
    </Box>
  );
};

export default function LeaderTrendChart({ data }: Props) {
  const years = Array.from(
    new Set(data.flatMap((d) => d.trends.map((t) => t.electionYear)))
  ).sort();

  const chartData = years.map((year) => {
    const row: any = { electionYear: year };
    data.forEach((leader) => {
      const trend = leader.trends.find((t) => t.electionYear === year);
      if (trend) {
        row[leader.leaderId] = {
          ...trend,
          leaderName: leader.leaderName,
        };
      }
    });
    return row;
  });
  const renderDot = (logo?: string) => (props: any) => {
    const { cx, cy, index } = props;
    if (cx == null || cy == null) return null;

    const radius = 15;
    const clipId = `clip-circle-${index}-${cx}-${cy}`;
    const imgHref =
      logo === "NULL"
        ? "/logos/nepaltracks.png"
        : logo || "/logos/nepaltracks.png";

    return (
      <>
        <defs>
          <clipPath id={clipId}>
            <circle cx={cx} cy={cy + 15} r={radius} />
          </clipPath>
        </defs>
        <image
          href={imgHref}
          x={cx - radius}
          y={cy - radius + 15} // <- moved upward by 10px
          width={radius * 2}
          height={radius * 2}
          clipPath={`url(#${clipId})`}
        />
      </>
    );
  };

  return (
    <Box className="p-4 rounded-xl border shadow">
      <ResponsiveContainer width="100%" height={450}>
        <LineChart
          data={chartData}
          margin={{ top: 10, right: 20, bottom: 10, left: 10 }}
        >
          <XAxis dataKey="electionYear" />
          <YAxis tickFormatter={(v) => v.toLocaleString()} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />

          {data.map((leader) => (
            <Line
              key={leader.leaderId}
              type="monotone"
              dataKey={(d) => d[leader.leaderId]?.voteCount}
              name={leader.leaderName}
              stroke={`hsl(${(leader.leaderId * 47) % 360}, 70%, 50%)`}
              // @ts-expect-error
              dot={renderDot(
                // @ts-expect-error
                getImageUrlWithFallback(leader.img, leader.ecCandidateID)
              )}
              strokeWidth={2}
              activeDot={{ r: 6 }}
            >
              <LabelList
                dataKey={(d: any) => {
                  const t = d[leader.leaderId];
                  if (!t) return "";
                  // return truncateString(leader.leaderName, 6);
                  return `${getTrendArrow(t.voteTrend)} ${
                    t.seatWon ? "〇" : "×"
                  } ${t.voteCount} `;
                }}
                position="top"
                style={{ fontSize: 11 }}
              />
            </Line>
          ))}
        </LineChart>
      </ResponsiveContainer>
    </Box>
  );
}
