import { getImageUrlWithFallback, truncateString } from "@/utils";
import {
  Card,
  Title,
  Avatar,
  Stack,
  Text,
  SimpleGrid,
  Group,
  Badge,
  Accordion,
} from "@mantine/core";
import PartyLink from "../Links/PartyLink";
import { IElectionOverview } from "@/interfaces/IElectionOverview";
import LeaderLink from "../Links/LeaderLink";

type Props = {
  data: IElectionOverview["topLeadersPerElection"];
};

export function TopPerformPerElections({ data }: Props) {
  const grouped = data.reduce<Record<number, typeof data>>((acc, party) => {
    if (!acc[party.electionYear]) acc[party.electionYear] = [];
    acc[party.electionYear].push(party);
    return acc;
  }, {});
  const items = Object.entries(grouped).sort(
    (a, b) => Number(b[0]) - Number(a[0])
  );

  return (
    <Accordion
      multiple
      defaultValue={items.map(([year]) => year + "")}
      radius="md"
      variant="separated"
      styles={{ item: { marginBottom: 12 } }}
    >
      {items.map(([year, result]) => (
        <Accordion.Item value={year} key={year}>
          <Accordion.Control>
            <Group justify="space-between" p={"sm"} wrap="nowrap">
              <Stack gap={2}>
                <Title order={3}>
                  {result[0].electionName}, {year}
                </Title>
                <Text size="sm" c={"dimmed"} className="line-clamp-1">
                  {truncateString(
                    result.map((item) => item.leader.localName).join(", "),
                    100
                  )}
                </Text>
              </Stack>
              {result.length > 3 && (
                <Avatar.Group>
                  {result.slice(0, 3).map((item, index) => (
                    <Avatar
                      key={index}
                      src={getImageUrlWithFallback(
                        item.leader.img,
                        item.leader.ecCandidateID
                      )}
                    />
                  ))}
                  <Avatar>+{result.length - 3}</Avatar>
                </Avatar.Group>
              )}
            </Group>
          </Accordion.Control>
          <Accordion.Panel>
            <SimpleGrid
              cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
              spacing="lg"
              mt="md"
            >
              {result.map((item, index) => (
                <Card key={index} withBorder>
                  <Stack align="center" gap={4}>
                    <Avatar
                      src={getImageUrlWithFallback(
                        item.leader.img,
                        item.leader.ecCandidateID
                      )}
                      size="lg"
                      radius="xl"
                    />
                    <LeaderLink id={item.leader.id}>
                      <Text fw={600} className="line-clamp-1">
                        {item.leader.localName}
                      </Text>
                    </LeaderLink>
                    <Group gap="xs">
                      {item.debut ? <Badge color="purple">Debut</Badge> : null}
                      {item.won ? <Badge color="green">Won</Badge> : null}
                      <Badge color="blue">Votes: {item.totalVotes}</Badge>
                    </Group>
                  </Stack>
                </Card>
              ))}
            </SimpleGrid>
          </Accordion.Panel>
        </Accordion.Item>
        // <Card key={year} mt="xl" withBorder>
        //   <Title order={3}>Top New Parties – {year}</Title>
        //   <SimpleGrid
        //     cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
        //     spacing="lg"
        //     mt="md"
        //   >
        //     {parties.map((party) => (
        //       <Card key={party.partyId} withBorder>
        //         <Stack align="center" gap={4}>
        //           <Avatar
        //             src={party.logo}
        //             alt={party.name}
        //             size="lg"
        //             radius="xl"
        //           />
        //           <PartyLink id={party.partyId}>
        //             <Text fw={600} className="line-clamp-1">
        //               {party.localName || party.name}
        //             </Text>
        //           </PartyLink>
        //           <Group gap="xs">
        //             <Badge color="gray">Seats: {party.seats}</Badge>
        //             <Badge color="blue">Votes: {party.votes}</Badge>
        //           </Group>
        //         </Stack>
        //       </Card>
        //     ))}
        //   </SimpleGrid>
        // </Card>
      ))}
    </Accordion>
  );
}
