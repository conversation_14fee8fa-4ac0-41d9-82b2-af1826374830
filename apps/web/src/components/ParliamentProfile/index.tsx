import { useTranslation } from "next-i18next";
import { IParliament } from "@/interfaces/IParliament";
import { formatConstituencyAddress, formatDate } from "@/utils";
import {
  Avatar,
  Badge,
  Box,
  Card,
  Group,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import { EntityInfo } from "../EntityInfo";
import LeaderHeroProfile from "../LeaderHeroProfile";
import { ILeader } from "@/interfaces/ILeader";

const ParliamentProfile = ({
  data: item,
  profileType,
  additionalInformationEnabled,
  slideshowMode,
  renderDescription,
  renderRightSection,
  renderTitle,
  ...props
}: {
  data: IParliament;
  profileType?: "Leader" | "Party" | "Election";
  additionalInformationEnabled?: boolean;
  slideshowMode?: "hover" | "always";
  renderDescription?: (params: any) => React.ReactNode | JSX.Element;
  renderRightSection?: (params: any) => React.ReactNode | JSX.Element;
  id?: string;
  renderTitle?: (params: any) => React.ReactNode | JSX.Element;
}) => {
  const { t } = useTranslation();

  if (!item) return <>{t("common:not_available")}</>;

  return (
    <EntityInfo
      resources="parliaments"
      id={item.id + ""}
      name={item.name}
      avatar="/logos/parliament_of_nepal.png"
      noShadow
      address={item.description}
      rate={item.rating?.average}
    />
  );
};
export default ParliamentProfile;
