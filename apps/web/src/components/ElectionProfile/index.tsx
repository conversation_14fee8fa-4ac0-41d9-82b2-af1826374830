import { IElections } from "@/interfaces/IElectionSubResponse";
import { EntityInfo } from "../EntityInfo";
import { formatDate } from "@/utils";
import DistanceFromNow from "../DistanceFromNow";

const ElectionSubProfile = ({ data }: { data: IElections }) => {
  return (
    <EntityInfo
      resources="elections"
      id={data.id + ""}
      name={data.name}
      avatar={"/images/others/ec-nepal.png"}
      address={formatDate(data.year)}
      title={<DistanceFromNow startedAt={data.year} />}
    />
  );
};
export default ElectionSubProfile;
