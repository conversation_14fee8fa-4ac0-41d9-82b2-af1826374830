import { ILeader } from "@/interfaces/ILeader";
import { EntityInfo } from "../EntityInfo";
import { getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import { Badge, Group } from "@mantine/core";
import { UserInfoAction } from "../UserInfoAction";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";

const LeaderInfoProfile = ({
  data,
}: {
  data: ILeader;
  renderTopRightSection?: (params: { age: number }) => React.ReactNode;
  additionalInformationEnabled?: boolean;
  renderBeforeAvatar?: (params: { age: number }) => React.ReactNode;
  isCakeDay?: boolean;
  renderDescription?: (params: {
    age: number;
  }) => React.ReactNode | JSX.Element;
  contentType?: string[];
  context?: EntityTypeEnum;
}) => {
  return (
    <UserInfoAction
      img={getImageUrlWithFallback(data.img, data.ecCandidateID)}
      name={data.localName}
      description={data.address}
      rating={data.rating?.average}
      showRate
      renderBeforeAvatar={() => (
        <Badge color="gray" size="xs">
          {getAgeFromDate(data.birthDate)}
        </Badge>
      )}
      resourceId={data.id}
      resourceType="leaders"
    />
  );
};
export default LeaderInfoProfile;
