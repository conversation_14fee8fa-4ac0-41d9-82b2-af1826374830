import { getImageUrlWithFallback } from "@/utils";
import { Avatar, Badge, Group, Stack, Text } from "@mantine/core";
import LeaderLink from "../Links/LeaderLink";

interface IProps {
  title: any;
  image?: string;
  texts: {
    [key: string]: any;
  };
}
const SimpleCardItem = (props: IProps) => {
  return (
    <Stack align="center">
      <Avatar src={props.image} size="lg" radius="xl" />
      <Stack gap={2} align="center">
        <Text fw={600} className="line-clamp-1">
          {props.title}
        </Text>
        {Object.keys(props.texts).map((key) => (
          <Text size="sm">
            {key}: <b>{props.texts[key]}</b>
          </Text>
        ))}
      </Stack>
    </Stack>
  );
};
export default SimpleCardItem;
