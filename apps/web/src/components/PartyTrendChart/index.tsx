import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContaine<PERSON>,
  Label<PERSON>ist,
  Legend,
} from "recharts";
import { Group, Image, Box, Text } from "@mantine/core";
import { IElectionOverview } from "@/interfaces/IElectionOverview";

type PartyElectionData = IElectionOverview["partyTrendsSeries"][0];

const groupByParty = (data: PartyElectionData[]) => {
  return data.reduce((map, item) => {
    if (!map.has(item.partyId)) map.set(item.partyId, []);
    map.get(item.partyId)!.push(item);
    return map;
  }, new Map<number, PartyElectionData[]>());
};

const getTrendArrow = (
  data: PartyElectionData[],
  index: number,
  key: "voteCount" | "seatCount"
) => {
  const curr = data[index];
  const prev = data[index - 1];
  if (!prev) return "•";
  const delta = curr[key] - prev[key];
  return delta > 0 ? "▲" : delta < 0 ? "▼" : "–";
};

const renderDot = (logo?: string) => (props: any) => {
  const { cx, cy } = props;
  if (!cy) return null;
  return logo ? (
    <image
      href={
        logo === "NULL"
          ? "/logos/nepaltracks.png"
          : logo || "/logos/nepaltracks.png"
      }
      x={cx - 10}
      y={cy - 10}
      height={20}
      width={20}
    />
  ) : null;
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload?.length) return null;
  const sortedPayload = payload.sort((a: any, b: any) => b.value - a.value);
  return (
    <Box p={8} bg="white" style={{ border: "1px solid #eee" }}>
      <Text fw={500}>Year: {label}</Text>
      {sortedPayload.map((item: any, idx: number) => (
        <Group key={idx} gap={6} mt={4}>
          <Box w={12} h={12} bg={item.color} style={{ borderRadius: "50%" }} />
          <Text size="sm">
            {item.name}: {item.value.toFixed(2)}%
          </Text>
        </Group>
      ))}
    </Box>
  );
};

const CustomLegend = (props: any) => {
  const { payload } = props;

  const sortedPayload = payload.slice(0, 10);
  return (
    <Group gap="xs" wrap="wrap" mt="sm">
      {sortedPayload.map((entry: any, idx: number) => (
        <Group key={idx} gap={4}>
          <Box w={12} h={12} bg={entry.color} style={{ borderRadius: "50%" }} />
          <Text size="sm">{entry.value}</Text>
        </Group>
      ))}{" "}
    </Group>
  );
};

export const PartyLineChart = ({
  data,
  mode,
}: {
  data: IElectionOverview["partyTrendsSeries"];
  mode: "vote" | "seat";
}) => {
  const byParty = groupByParty(data);
  const years = Array.from(new Set(data.map((d) => d.electionYear))).sort();

  const chartData = years.map((year) => {
    const entry: Record<string, any> = { year };
    for (const [partyId, records] of byParty.entries()) {
      const point = records.find((r: any) => r.electionYear === year);
      entry[partyId] = point
        ? mode === "vote"
          ? point.votePercent
          : point.seatPercent
        : null;
      entry[`label-${partyId}`] = point
        ? `${mode === "vote" ? point.voteCount : point.seatCount}`
        : null;
    }
    return entry;
  });

  const legendPayload = Array.from(byParty.entries()).map(
    ([partyId, records]) => ({
      id: partyId,
      type: "line",
      value: records[0].name,
      color: records[0].color || "#999",
      logo: records[0].logo,
      voteCount: records[0].voteCount,
      seatCount: records[0].seatCount,
      votePercent: records[0].votePercent,
      seatPercent: records[0].seatPercent,
    })
  );

  return (
    <ResponsiveContainer width="100%" height={500}>
      <LineChart data={chartData}>
        <XAxis dataKey="year" />
        <YAxis domain={[0, 100]} tickFormatter={(t) => `${t}%`} />
        <Tooltip content={<CustomTooltip />} />
        {/* @ts-ignore */}
        <Legend content={<CustomLegend />} payload={legendPayload} />
        {[...byParty.entries()].map(([partyId, records]) => {
          const party = records[0];
          return (
            <Line
              key={partyId}
              type="monotone"
              dataKey={partyId.toString()}
              stroke={party.color || "#999"}
              //   @ts-expect-error */}
              dot={renderDot(party.logo || "/logos/nepaltracks.png")}
              strokeWidth={2}
              name={party.name}
            >
              <LabelList
                dataKey={`label-${partyId}`}
                position="top"
                // @ts-expect-error
                formatter={(_, __, idx) =>
                  getTrendArrow(
                    records,
                    idx,
                    mode === "vote" ? "voteCount" : "seatCount"
                  )
                }
              />
            </Line>
          );
        })}
      </LineChart>
    </ResponsiveContainer>
  );
};
