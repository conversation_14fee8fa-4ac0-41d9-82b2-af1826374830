import { IContent } from "@/interfaces/IContent";
import {
  ContentTypeColors,
  formatDate,
  getImageUrlWithFallback,
} from "@/utils";
import { Avatar, Paper, Text, Timeline, Title } from "@mantine/core";
import { formatDistance } from "date-fns";
import Link from "next/link";
import { useTranslation } from "react-i18next";

const ContentSeries = ({
  series,
  content,
  isChild,
}: {
  series: IContent[];
  isChild?: boolean;
  content: IContent;
}) => {
  const { t } = useTranslation();
  const minimumLength = isChild ? 0 : 1;
  return (
    <>
      {series?.length > minimumLength && !isChild ? (
        <Title order={5}>{t("common:series", "Series")}</Title>
      ) : null}
      {series?.length > minimumLength ? (
        <Timeline lineWidth={isChild ? 0 : 2} bulletSize={25} className="mt-4">
          {!isChild && (
            <Timeline.Item
              bullet={
                <Avatar
                  size={40}
                  radius="xl"
                  src={getImageUrlWithFallback(
                    // @ts-expect-error
                    content.resource?.img,
                    // @ts-expect-error
                    content.resource?.ecCandidateID
                  )}
                />
              }
              title={
                // @ts-expect-error
                content.resource?.localName +
                " (" +
                content.resource?.name +
                ")"
              }
            ></Timeline.Item>
          )}
          {series.map((seriesItem: any) => (
            <Timeline.Item
              key={seriesItem.id}
              title={
                <Link
                  href={`/contents/${seriesItem.resourceType}/${seriesItem.contentType}/${seriesItem.id}`}
                  className="no-underline text-inherit text-sm"
                >
                  {seriesItem.title}
                </Link>
              }
              bullet={
                <Avatar
                  variant="filled"
                  size={25}
                  radius="xl"
                  color={ContentTypeColors[seriesItem.contentType]}
                >
                  {seriesItem.icon ? <seriesItem.icon /> : null}
                </Avatar>
              }
            >
              {seriesItem?.img}
              <Text c="dimmed" size="sm" className="line-clamp-1">
                {seriesItem.content}
              </Text>
              <Text size="xs" mt={4}>
                {formatDistance(new Date(seriesItem.eventDate), new Date())} ago
                {" - "}
                {formatDate(seriesItem.eventDate)}
              </Text>
              {seriesItem.childContents?.length ? (
                <Paper ml={"md"} p={"md"}>
                  <ContentSeries
                    content={seriesItem}
                    series={(seriesItem.childContents || []).filter(
                      (item: IContent) =>
                        !series.map((e: any) => e.id).includes(item.id)
                    )}
                    isChild
                  />
                </Paper>
              ) : null}
            </Timeline.Item>
          ))}
        </Timeline>
      ) : null}
    </>
  );
};

export default ContentSeries;
