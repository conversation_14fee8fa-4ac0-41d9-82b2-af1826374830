import { SocialIcon } from "react-social-icons";

export default function SocialLinks({
  links,
}: {
  links: Record<string, string | undefined>;
}) {
  const supported = Object.entries(links).filter(([_, url]) => !!url);

  if (supported.length === 0) return null;

  return (
    <div className="flex gap-3 flex-wrap mt-2">
      {supported.map(([platform, url]) => (
        <SocialIcon
          key={platform}
          url={url}
          style={{ height: 24, width: 24 }}
          bgColor="#fff"
          fgColor="#000"
          target="_blank"
          rel="noopener noreferrer"
        />
      ))}
    </div>
  );
}
