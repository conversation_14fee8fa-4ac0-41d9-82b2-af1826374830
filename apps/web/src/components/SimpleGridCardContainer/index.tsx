import {
  Card,
  Title,
  SimpleGrid,
  Group,
  Avatar,
  Stack,
  Text,
  Badge,
  Button,
} from "@mantine/core";
import { useState } from "react";
import { getImageUrlWithFallback } from "@/utils";
import LeaderLink from "../Links/LeaderLink";

type ConsistentWinner = {
  leaderId: number;
  name: string;
  winCount: number;
  lossCount: number;
  totalContests: number;
  winRatio: number;
  leader: {
    id: number;
    localName: string;
    ecCandidateID: string;
    img: string;
  };
};

interface IProps {
  title: string;
  data: any[];
  renderCardContent: (item: any) => React.ReactNode | JSX.Element;
}
export function SimpleGridCardContainer({ data, ...props }: IProps) {
  const [expanded, setExpanded] = useState(false);
  const visible = expanded ? data : data.slice(0, 8);

  return (
    <Card withBorder mt="xl">
      <Title order={3}>{props.title}</Title>

      <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing="lg" mt="md">
        {visible.map((result, index) => (
          <Card key={index} withBorder shadow="sm">
            {props.renderCardContent(result)}
          </Card>
        ))}
      </SimpleGrid>

      {data.length > 8 && (
        <Group justify="center" mt="md">
          <Button
            size="xs"
            variant="light"
            onClick={() => setExpanded((e) => !e)}
          >
            {expanded ? "Show Less" : "Show More"}
          </Button>
        </Group>
      )}
    </Card>
  );
}
