import { Button } from "@mantine/core";
import { IconBrandReddit } from "@tabler/icons-react";
import React from "react";
import { useTranslation } from "next-i18next";

const RedditSubmitButton = ({
  title,
  url,
  contentType,
  onlyRedditIcon,
}: {
  title: string;
  url: string;
  contentType: string;
  onlyRedditIcon?: boolean;
}) => {
  const { t } = useTranslation();
  const handleClick = () => {
    const subreddit = "nepaltracks";
    const encodedTitle = encodeURIComponent(title);
    const encodedUrl = encodeURIComponent(url);

    const redditUrl = `https://www.reddit.com/r/nepaltracks/submit?title=${encodedTitle}`;
    window.open(redditUrl, "_blank"); // opens in new tab
  };

  if (onlyRedditIcon) {
    return (
      <IconBrandReddit
        size={16}
        onClick={handleClick}
        className="cursor-pointer"
      />
    );
  }
  return (
    <Button
      variant="transparent"
      onClick={handleClick}
      leftSection={<IconBrandReddit />}
    >
      {t("common:create_new_post", {
        type: t("common:" + contentType, contentType),
        defaultValue: `Create new ${contentType}`,
      })}{" "}
      r/nepaltracks
    </Button>
  );
};

export default RedditSubmitButton;
