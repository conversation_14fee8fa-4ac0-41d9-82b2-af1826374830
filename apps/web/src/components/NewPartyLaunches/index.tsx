import { getImageUrlWithFallback, truncateString } from "@/utils";
import {
  Card,
  Title,
  Avatar,
  Stack,
  Text,
  SimpleGrid,
  Group,
  Badge,
  Accordion,
} from "@mantine/core";
import PartyLink from "../Links/PartyLink";
import { IElectionOverview } from "@/interfaces/IElectionOverview";

type Props = {
  data: IElectionOverview["newPartyLaunches"];
};

export function NewPartyLaunches({ data }: Props) {
  const grouped = data.reduce<Record<number, typeof data>>((acc, party) => {
    if (!acc[party.firstYear]) acc[party.firstYear] = [];
    acc[party.firstYear].push(party);
    return acc;
  }, {});
  const items = Object.entries(grouped).sort(
    (a, b) => Number(b[0]) - Number(a[0])
  );

  return (
    <Accordion
      multiple
      defaultValue={items.map(([year]) => year + "")}
      radius="md"
      variant="separated"
      styles={{ item: { marginBottom: 12 } }}
    >
      {items.map(([year, parties]) => (
        <Accordion.Item value={year} key={year}>
          <Accordion.Control>
            <Group justify="space-between" p={"sm"} wrap="nowrap">
              <Stack gap={2}>
                <Title order={3}>
                  {parties[0].electionName}, {year}
                </Title>
                <Text size="sm" c={"dimmed"} className="line-clamp-1">
                  {truncateString(
                    parties.map((party) => party.name).join(", "),
                    100
                  )}
                </Text>
              </Stack>
              {parties.length > 3 && (
                <Avatar.Group>
                  {parties.slice(0, 3).map((party) => (
                    <Avatar key={party.partyId} src={party.logo} />
                  ))}
                  <Avatar>+{parties.length - 3}</Avatar>
                </Avatar.Group>
              )}
            </Group>
          </Accordion.Control>
          <Accordion.Panel>
            <SimpleGrid
              cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
              spacing="lg"
              mt="md"
            >
              {parties.map((party) => (
                <Card key={party.partyId} withBorder>
                  <Stack align="center" gap={4}>
                    <Avatar
                      src={party.logo}
                      alt={party.name}
                      size="lg"
                      radius="xl"
                    />
                    <PartyLink id={party.partyId}>
                      <Text fw={600} className="line-clamp-1">
                        {party.localName || party.name}
                      </Text>
                    </PartyLink>
                    <Group gap="xs">
                      <Badge color="gray">Seats: {party.seats}</Badge>
                      <Badge color="blue">Votes: {party.votes}</Badge>
                    </Group>
                  </Stack>
                </Card>
              ))}
            </SimpleGrid>
          </Accordion.Panel>
        </Accordion.Item>
        // <Card key={year} mt="xl" withBorder>
        //   <Title order={3}>Top New Parties – {year}</Title>
        //   <SimpleGrid
        //     cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
        //     spacing="lg"
        //     mt="md"
        //   >
        //     {parties.map((party) => (
        //       <Card key={party.partyId} withBorder>
        //         <Stack align="center" gap={4}>
        //           <Avatar
        //             src={party.logo}
        //             alt={party.name}
        //             size="lg"
        //             radius="xl"
        //           />
        //           <PartyLink id={party.partyId}>
        //             <Text fw={600} className="line-clamp-1">
        //               {party.localName || party.name}
        //             </Text>
        //           </PartyLink>
        //           <Group gap="xs">
        //             <Badge color="gray">Seats: {party.seats}</Badge>
        //             <Badge color="blue">Votes: {party.votes}</Badge>
        //           </Group>
        //         </Stack>
        //       </Card>
        //     ))}
        //   </SimpleGrid>
        // </Card>
      ))}
    </Accordion>
  );
}
