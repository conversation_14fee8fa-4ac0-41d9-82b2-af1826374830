import {
  Card,
  Title,
  SimpleGrid,
  Group,
  Avatar,
  Stack,
  Text,
  Badge,
  Button,
} from "@mantine/core";
import { useState } from "react";
import { getImageUrlWithFallback } from "@/utils";
import LeaderLink from "../Links/LeaderLink";

type ConsistentWinner = {
  leaderId: number;
  name: string;
  winCount: number;
  lossCount: number;
  totalContests: number;
  winRatio: number;
  leader: {
    id: number;
    localName: string;
    ecCandidateID: string;
    img: string;
  };
};

export function ConsistentWinnersCard({ data }: { data: ConsistentWinner[] }) {
  const [expanded, setExpanded] = useState(false);
  const sorted = [...data].sort((a, b) => b.winRatio - a.winRatio);
  const visible = expanded ? sorted : sorted.slice(0, 8);

  return (
    <Card withBorder mt="xl">
      <Title order={3}>Consistent Winners</Title>

      <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing="lg" mt="md">
        {visible.map((result) => (
          <Card key={result.leaderId} withBorder shadow="sm">
            <Group align="flex-start">
              <Avatar
                src={getImageUrlWithFallback(
                  result.leader.img,
                  result.leader.ecCandidateID
                )}
                size="lg"
                radius="xl"
              />
              <Stack gap={2}>
                <LeaderLink id={result.leader.id} subPath="elections">
                  <Text fw={600} className="line-clamp-1">
                    {result.leader.localName || result.name}
                  </Text>
                </LeaderLink>
                <Text size="sm">
                  Wins: <b>{result.winCount}</b>
                </Text>
                <Text size="sm">
                  Losses: <b>{result.lossCount}</b>
                </Text>
                <Text size="sm">
                  Contests: <b>{result.totalContests}</b>
                </Text>
                <Badge
                  color={result.winRatio >= 50 ? "green" : "red"}
                  size="sm"
                  radius="sm"
                  variant="light"
                >
                  Win Rate: {result.winRatio}%
                </Badge>
              </Stack>
            </Group>
          </Card>
        ))}
      </SimpleGrid>

      {data.length > 8 && (
        <Group justify="center" mt="md">
          <Button
            size="xs"
            variant="light"
            onClick={() => setExpanded((e) => !e)}
          >
            {expanded ? "Show Less" : "Show More"}
          </Button>
        </Group>
      )}
    </Card>
  );
}
