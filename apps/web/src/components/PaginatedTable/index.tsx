import {
  Pagination,
  PaginationProps,
  Skeleton,
  Stack,
  Table,
  Title,
} from "@mantine/core";
import { t } from "i18next";
import { useTranslation } from "react-i18next";

export interface PaginatedTableProps {
  isLoading?: boolean;
  pagination?: PaginationProps;
  columns: {
    id: string;
    label: string | JSX.Element;
    renderCell?: (value: any, data: unknown) => JSX.Element;
  }[];
  data: any[];
}
const PaginatedTable = (props: PaginatedTableProps) => {
  const { t } = useTranslation();
  return (
    <Stack align="center">
      {/* {props.pagination && (
        <Pagination
          {...props.pagination}
          value={props.pagination.value || 1}
          total={props.pagination.total / props.data.length}
          mt="sm"
        />
      )}{" "} */}
      <Title order={5}>{props?.pagination?.total} Result(s)</Title>
      <Table striped verticalSpacing="sm" w={"100%"} mih={500}>
        <Table.Thead>
          <Table.Tr>
            {props.columns.map((col) => {
              return (
                <Table.Th key={col.id}>
                  {t("common:" + col.label, { defaultValue: col.label })}
                </Table.Th>
              );
            })}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {props.isLoading &&
            Array(10)
              .fill(true)
              .map((item, index) => {
                return (
                  <Table.Tr key={index}>
                    {props.columns.map((map, index) => {
                      return (
                        <Table.Td key={index}>
                          <Skeleton h={30} w={70} />
                        </Table.Td>
                      );
                    })}
                  </Table.Tr>
                );
              })}
          {props.data.map((row, key) => {
            return (
              <Table.Tr key={key}>
                {props.columns.map((col, index) => {
                  return (
                    <Table.Td key={"col" + index} maw={400}>
                      {col?.renderCell?.(col?.id && row[col?.id], row) ||
                        row[col?.id || ""]}
                    </Table.Td>
                  );
                })}
              </Table.Tr>
            );
          })}
        </Table.Tbody>{" "}
      </Table>
      {props.pagination && (
        <Pagination
          {...props.pagination}
          total={props.pagination.total / props.data.length}
          value={props.pagination.value || 1}
          mt="sm"
        />
      )}
    </Stack>
  );
};
export default PaginatedTable;
