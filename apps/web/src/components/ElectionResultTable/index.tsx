import { useTranslation } from "next-i18next";
import { IElectionResult } from "@/interfaces/IElections";
import { getAgeFromDate } from "@/utils";
import {
  Avatar,
  Badge,
  Group,
  ScrollArea,
  Stack,
  Table,
  Text,
} from "@mantine/core";
import { IconTrendingDown, IconTrendingUp } from "@tabler/icons-react";
import Link from "next/link";

interface IProps {
  data: IElectionResult[];
  stats: {
    [key: string]: string;
  };
}
export const ElectionResultTable = (props: IProps) => {
  const { t } = useTranslation();
  return (
    <Table striped verticalSpacing="sm" w={"100%"}>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>{t("common:name")}</Table.Th>
          <Table.Th>{t("common:votes")}</Table.Th>
          <Table.Th>{t("common:win_margin")}</Table.Th>
          <Table.Th>{t("common:loss_margin")}</Table.Th>
          <Table.Th>{t("common:party")}</Table.Th>
          {/* <Table.Th>{t("common:review")}</Table.Th> */}
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {props.data?.map(
          ({ parties, isElected, voteCount, leaders: item }, index) => (
            <Table.Tr
              key={item?.id}
              style={
                isElected
                  ? {
                      // background: "rgb(136 205 105 / 81%)",
                      // color: "white"
                    }
                  : {}
              }
            >
              <Table.Td>
                <Link
                  href={`/leaders/${item?.id}`}
                  className="no-underline text-inherit"
                >
                  <Group gap="sm" wrap="nowrap">
                    <Avatar
                      size={"md"}
                      src={
                        item?.img ||
                        `/images/leaders/${item?.ecCandidateID}.jpg`
                      }
                    />

                    <Stack gap={-10} align="center">
                      {isElected && (
                        <Badge
                          variant={t("common:gradient")}
                          gradient={{
                            from: "teal",
                            to: "lime",
                            deg: 105,
                          }}
                        >
                          {" "}
                          Elected
                        </Badge>
                      )}
                      <Text fz="sm" fw={500}>
                        {item?.localName}
                      </Text>
                    </Stack>

                    <Badge color="gray">
                      {item?.birthDate && getAgeFromDate(item?.birthDate)}
                    </Badge>
                  </Group>
                </Link>
              </Table.Td>
              <Table.Td>{voteCount.toLocaleString()}</Table.Td>
              <Table.Td>
                <Group align="center" wrap="nowrap">
                  <Text>+ </Text>
                  <Text ta={"center"}>
                    {voteCount - (props.data?.[index + 1]?.voteCount || 0)}
                  </Text>
                  <IconTrendingUp
                    color={t("common:green")}
                    size="1.4rem"
                    stroke={1.5}
                  />
                </Group>
              </Table.Td>
              <Table.Td>
                <Group align="center" wrap="nowrap">
                  {isElected ? (
                    "-"
                  ) : (
                    <>
                      <Text>- </Text>
                      <Text>
                        {(props.data?.[0]?.voteCount || 0) - voteCount}
                      </Text>
                      <IconTrendingDown
                        color="red"
                        size="1.4rem"
                        stroke={1.5}
                      />
                    </>
                  )}
                </Group>
              </Table.Td>
              <Table.Td>
                <Link
                  href={`/parties/${parties?.id}`}
                  className="no-underline text-inherit"
                >
                  <Group gap="sm" wrap="nowrap">
                    <Avatar size={"xs"} src={parties?.logo} />
                    <Text fz="sm" fw={500}>
                      {parties?.code}
                    </Text>
                  </Group>
                </Link>
              </Table.Td>
              {/* 
              <Table.Td>
                {props.stats?.percentages[item?.id || 0]}
                <Group justify="apart" wrap="nowrap">
                  <Text fz="xs" c="teal" fw={700}>
                    {props.stats?.percentages[item?.id || 0]}%
                  </Text>
                  <Text fz="xs" c="red" fw={700}>
                    {100 - +props.stats?.percentages[item?.id || 0]}%
                  </Text>
                </Group>
              </Table.Td> */}
            </Table.Tr>
          )
        )}
      </Table.Tbody>
    </Table>
  );
};
