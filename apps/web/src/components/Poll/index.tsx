"use client";

import { useState, useEffect } from "react";
import {
  Card,
  Text,
  Title,
  Button,
  Progress,
  Radio,
  Checkbox,
  Badge,
  Group,
  Stack,
  rem,
  Avatar,
  Box,
} from "@mantine/core";
import { IconClock, IconUsers, IconCheck } from "@tabler/icons-react";

interface PollOption {
  id: string;
  text: string;
  votes: number;
  avatar?: string;
  avatarAlt?: string;
}

interface PollProps {
  id: string;
  title: string;
  description?: string;
  options: PollOption[];
  mode: "single" | "multiple";
  expiresAt?: Date;
  totalVotes?: number;
  hasVoted?: boolean;
  userVotes?: string[];
}

export default function Poll({
  id,
  title,
  description,
  options: initialOptions,
  mode = "single",
  expiresAt,
  totalVotes = 0,
  hasVoted = false,
  userVotes = [],
}: PollProps) {
  const [options, setOptions] = useState(initialOptions);
  const [selectedOptions, setSelectedOptions] = useState<string[]>(userVotes);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasUserVoted, setHasUserVoted] = useState(hasVoted);
  const [currentTotalVotes, setCurrentTotalVotes] = useState(totalVotes);
  const [timeLeft, setTimeLeft] = useState<string>("");

  const isExpired = expiresAt ? new Date() > expiresAt : false;
  const showResults = hasUserVoted || isExpired;

  // Calculate time remaining
  useEffect(() => {
    if (!expiresAt) return;

    const updateTimeLeft = () => {
      const now = new Date();
      const diff = expiresAt.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft("Expired");
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeLeft(`${days}d ${hours}h left`);
      } else if (hours > 0) {
        setTimeLeft(`${hours}h ${minutes}m left`);
      } else {
        setTimeLeft(`${minutes}m left`);
      }
    };

    updateTimeLeft();
    const interval = setInterval(updateTimeLeft, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [expiresAt]);

  const handleSingleSelect = (optionId: string) => {
    if (showResults) return;
    setSelectedOptions([optionId]);
  };

  const handleMultipleSelect = (optionId: string, checked: boolean) => {
    if (showResults) return;

    if (checked) {
      setSelectedOptions((prev) => [...prev, optionId]);
    } else {
      setSelectedOptions((prev) => prev.filter((id) => id !== optionId));
    }
  };

  const handleSubmit = async () => {
    if (selectedOptions.length === 0 || showResults) return;

    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Update vote counts
    const updatedOptions = options.map((option) => ({
      ...option,
      votes: selectedOptions.includes(option.id)
        ? option.votes + 1
        : option.votes,
    }));

    setOptions(updatedOptions);
    setCurrentTotalVotes((prev) => prev + 1);
    setHasUserVoted(true);
    setIsSubmitting(false);
  };

  const getPercentage = (votes: number) => {
    if (currentTotalVotes === 0) return 0;
    return Math.round((votes / currentTotalVotes) * 100);
  };

  const getMaxVotes = () => Math.max(...options.map((option) => option.votes));

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{ maxWidth: rem(600) }}
    >
      <Stack gap="md">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Stack gap="xs" style={{ flex: 1 }}>
            <Title order={3} size="h3">
              {title}
            </Title>
            {description && (
              <Text size="sm" c="dimmed">
                {description}
              </Text>
            )}
          </Stack>
          <Group gap="xs">
            {expiresAt && (
              <Badge
                color={isExpired ? "red" : "blue"}
                variant="light"
                leftSection={
                  <IconClock style={{ width: rem(12), height: rem(12) }} />
                }
              >
                {timeLeft}
              </Badge>
            )}
            <Badge
              color="gray"
              variant="outline"
              leftSection={
                <IconUsers style={{ width: rem(12), height: rem(12) }} />
              }
            >
              {currentTotalVotes} votes
            </Badge>
          </Group>
        </Group>

        {/* Poll Options */}
        <Stack gap="sm">
          {mode === "single" ? (
            <Radio.Group
              value={selectedOptions[0] || ""}
              onChange={handleSingleSelect}
            >
              <Stack gap="xs">
                {options.map((option) => (
                  <Box
                    key={option.id}
                    p="xs"
                    style={{
                      borderRadius: "var(--mantine-radius-sm)",
                      cursor: showResults ? "default" : "pointer",
                      backgroundColor:
                        selectedOptions.includes(option.id) && !showResults
                          ? "var(--mantine-color-blue-0)"
                          : "transparent",
                      border:
                        selectedOptions.includes(option.id) && !showResults
                          ? "1px solid var(--mantine-color-blue-3)"
                          : "1px solid transparent",
                    }}
                    onClick={() =>
                      !showResults && handleSingleSelect(option.id)
                    }
                  >
                    <Stack gap="xs">
                      <Group justify="space-between" align="center">
                        <Group gap="sm" align="center" style={{ flex: 1 }}>
                          <Radio value={option.id} disabled={showResults} />
                          {option.avatar && (
                            <Avatar
                              src={option.avatar}
                              alt={option.avatarAlt || option.text}
                              size="sm"
                              radius="sm"
                            />
                          )}
                          <Text size="sm" style={{ flex: 1 }}>
                            {option.text}
                          </Text>
                        </Group>
                        {showResults && (
                          <Group gap="xs" align="center">
                            <Text size="sm" fw={500}>
                              {option.votes} ({getPercentage(option.votes)}%)
                            </Text>
                            {option.votes === getMaxVotes() &&
                              option.votes > 0 && (
                                <IconCheck
                                  style={{ width: rem(16), height: rem(16) }}
                                  color="var(--mantine-color-green-6)"
                                />
                              )}
                          </Group>
                        )}
                      </Group>
                      {showResults && (
                        <Progress
                          value={getPercentage(option.votes)}
                          size="xs"
                          radius="sm"
                          color="blue"
                        />
                      )}
                    </Stack>
                  </Box>
                ))}
              </Stack>
            </Radio.Group>
          ) : (
            <Stack gap="xs">
              {options.map((option) => (
                <Box
                  key={option.id}
                  p="xs"
                  style={{
                    borderRadius: "var(--mantine-radius-sm)",
                    cursor: showResults ? "default" : "pointer",
                    backgroundColor:
                      selectedOptions.includes(option.id) && !showResults
                        ? "var(--mantine-color-blue-0)"
                        : "transparent",
                    border:
                      selectedOptions.includes(option.id) && !showResults
                        ? "1px solid var(--mantine-color-blue-3)"
                        : "1px solid transparent",
                  }}
                  onClick={() =>
                    !showResults &&
                    handleMultipleSelect(
                      option.id,
                      !selectedOptions.includes(option.id)
                    )
                  }
                >
                  <Stack gap="xs">
                    <Group justify="space-between" align="center">
                      <Group gap="sm" align="center" style={{ flex: 1 }}>
                        <Checkbox
                          checked={selectedOptions.includes(option.id)}
                          onChange={(event) =>
                            handleMultipleSelect(
                              option.id,
                              event.currentTarget.checked
                            )
                          }
                          disabled={showResults}
                        />
                        {option.avatar && (
                          <Avatar
                            src={option.avatar}
                            alt={option.avatarAlt || option.text}
                            size="sm"
                            radius="sm"
                          />
                        )}
                        <Text size="sm" style={{ flex: 1 }}>
                          {option.text}
                        </Text>
                      </Group>
                      {showResults && (
                        <Group gap="xs" align="center">
                          <Text size="sm" fw={500}>
                            {option.votes} ({getPercentage(option.votes)}%)
                          </Text>
                          {option.votes === getMaxVotes() &&
                            option.votes > 0 && (
                              <IconCheck
                                style={{ width: rem(16), height: rem(16) }}
                                color="var(--mantine-color-green-6)"
                              />
                            )}
                        </Group>
                      )}
                    </Group>
                    {showResults && (
                      <Progress
                        value={getPercentage(option.votes)}
                        size="xs"
                        radius="sm"
                        color="blue"
                      />
                    )}
                  </Stack>
                </Box>
              ))}
            </Stack>
          )}
        </Stack>

        {/* Submit Button */}
        {!showResults && (
          <Button
            onClick={handleSubmit}
            disabled={selectedOptions.length === 0 || isSubmitting}
            loading={isSubmitting}
            fullWidth
            size="md"
          >
            {isSubmitting ? "Submitting..." : "Submit Vote"}
          </Button>
        )}

        {/* Status Messages */}
        {hasUserVoted && !isExpired && (
          <Group justify="center" gap="xs">
            <IconCheck
              style={{ width: rem(16), height: rem(16) }}
              color="var(--mantine-color-green-6)"
            />
            <Text size="sm" c="dimmed" ta="center">
              Thank you for voting! Results will be final when the poll expires.
            </Text>
          </Group>
        )}

        {isExpired && (
          <Text size="sm" c="dimmed" ta="center">
            This poll has ended. Final results are shown above.
          </Text>
        )}
      </Stack>
    </Card>
  );
}
