import DefaultLink from "./DefaultLink";

const ElectionDetailLink = (props: {
  electionId: string | number;
  elCode: string | number;
  candidacyTypeId: string | number;
  children: React.ReactNode;
}) => {
  return (
    <DefaultLink
      href={`/elections/${props.electionId}/sub/${props.elCode}/${props.candidacyTypeId}`}
    >
      {props.children}
    </DefaultLink>
  );
};
export default ElectionDetailLink;
