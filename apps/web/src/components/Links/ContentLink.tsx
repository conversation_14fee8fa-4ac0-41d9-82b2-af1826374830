import DefaultLink from "./DefaultLink";

const ContentLink = (props: {
  resourceType: string;
  contentType: string;
  id: string | number;
  children: React.ReactNode;
}) => {
  return (
    <DefaultLink
      href={`/contents/${props.resourceType.toLowerCase()}/${props.contentType.toLowerCase()}/${
        props.id
      }`}
    >
      {props.children}
    </DefaultLink>
  );
};
export default ContentLink;
