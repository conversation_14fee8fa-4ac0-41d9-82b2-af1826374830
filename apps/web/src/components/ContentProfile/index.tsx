import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  getHostFromUrl,
  getImageUrlWithFallback,
  truncateString,
} from "@/utils";
import { EntityInfo } from "../EntityInfo";
import { Avatar, Badge, Group } from "@mantine/core";
import { startCase } from "lodash";
import { IContent } from "@/interfaces/IContent";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { getLeaderImages } from "@/hooks/useLeaderImages";

const ContentProfile = ({ data: item }: { data: IContent }) => {
  const { t } = useTranslation();
  const Icon = (ContentTypeIconMap[
    item.contentType as ContentTypeIconMapType
  ] || ContentTypeIconMap.DEFAULT) as any;
  const color =
    // @ts-ignore
    ContentTypeColors[item.contentType as ContentTypeIconMapType] || "gray";

  return (
    <Link
      href={`/contents/${item.resourceType.toLowerCase()}/${item.contentType.toLowerCase()}/${
        item.id
      }`}
      className="no-underline text-inherit"
    >
      <EntityInfo
        paperProps={{ h: "15rem", w: "350px" }}
        avatar={
          item.cmsLink ? (
            item.resource && item.resourceType === "LEADER" ? (
              <Avatar
                src={getLeaderImages(item.resource as any).defaultImage}
                alt={item.title}
                size={"lg"}
              />
            ) : (
              <Avatar
                src={getHostFromUrl(item.cmsLink) + "/favicon.ico"}
                alt={item.title}
                size={"lg"}
              />
            )
          ) : (
            <Icon size={30} color={color} />
          )
        }
        id={item.id + ""}
        resources={`/contents/${item.resourceType.toLowerCase()}/${item.contentType.toLowerCase()}/`}
        name={item.title}
        address={
          truncateString(
            item?.content || "",
            item.contentType === "NEWS" ? 100 : 200
          ) || "No content"
        }
        titleTextAlign="start"
        rate={item.rating?.average}
        title={
          <Group>
            <Badge size="sm" color={color} leftSection={<Icon size={12} />}>
              {t(`common:${item.contentType}`)}
            </Badge>{" "}
            {/* <Badge size="sm" color={color} variant="outline">
              {t(`common:${item.contentStatus}`, {
                defaultValue: startCase(item.contentStatus),
              })}
            </Badge> */}
          </Group>
        }
      />
    </Link>
  );
};

export default ContentProfile;
