import { useTranslation } from "next-i18next";
import { IGovernment } from "@/interfaces/IGovernment";
import { EntityInfo } from "../EntityInfo";
import { getImageUrlWithFallback } from "@/utils";
import { Badge, Group } from "@mantine/core";
import { IMunicipals } from "@/interfaces/IElectionSubResponse";

const WardProfile = ({ data }: { data: IMunicipals }) => {
  const { t } = useTranslation();
  return (
    <EntityInfo
      cardProps={{ mih: "160px" }}
      resources="wards"
      id={data.id + ""}
      name={"Ward " + data.localName || data.name}
      // title={
      //   <Group>
      //     {data.government_type} Government
      //     {!data.endAt && (
      //       <Badge color={"green"}>{t("common:current")}</Badge>
      //     )}
      //   </Group>
      // }
    />
  );
};
export default WardProfile;
