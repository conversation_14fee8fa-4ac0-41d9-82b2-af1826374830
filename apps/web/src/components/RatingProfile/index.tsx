import RenderResourceType from "@/containers/RenderResourceType";
import { IRating } from "@/interfaces/IRating";
import {
  Avatar,
  Box,
  Card,
  Center,
  Group,
  Paper,
  Rating,
  Stack,
  Text,
  useMantineColorScheme,
} from "@mantine/core";
import { formatDistance } from "date-fns";
import Link from "next/link";

const RatingProfile = ({ data }: { data: IRating }) => {
  if (!data.user || !data.resource) return null;
  const { colorScheme } = useMantineColorScheme();

  return (
    <Paper p={"xs"} h={"100%"}>
      <Stack h={"100%"} align="start">
        <Link
          href={`/reviews/${data.hash}`}
          className="no-underline text-inherit w-full"
        >
          <Stack
            p={"xs"}
            className={`${
              colorScheme === "dark" ? "bg-gray-800" : "bg-gray-100"
            } rounded-lg w-full`}
          >
            <Group wrap="nowrap" justify="space-between">
              <Group wrap="nowrap" gap={5}>
                <Avatar src={data.user?.profileImage} size={"sm"} />
                <Text size={"xs"}>
                  {data.user?.firstName} {data.user?.lastName}
                </Text>
              </Group>
              <Text size={"xs"} c={"dimmed"} className="italic">
                {formatDistance(new Date(data.createdAt), new Date())} ago
              </Text>
            </Group>
            <Stack gap={1}>
              <Rating value={data.value} readOnly size={"xs"} />
              <Text size={"xs"} className="line-clamp-1">
                {data.comment}
              </Text>
            </Stack>
          </Stack>
        </Link>
        <Card p={"xs"} h={"100%"} w={"100%"}>
          <Center>
            <RenderResourceType
              //@ts-expect-error
              resourceType={data.rateOnType}
              resource={data.resource}
            />
          </Center>
        </Card>
      </Stack>
    </Paper>
  );
};

export default RatingProfile;
