import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import LeaderHeroProfile from "../LeaderHeroProfile";
import { ILeader } from "@/interfaces/ILeader";
import { Badge } from "@mantine/core";
import { toLower } from "lodash";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { useTranslation } from "react-i18next";

const TopContentLeader = ({
  resourceUrl,
  contentType,
  partyId,
  context,
}: {
  resourceUrl?: string;
  contentType: string[];
  context?: EntityTypeEnum;
  partyId?: string;
}) => {
  const nextContentType = contentType.join(",");
  const { t } = useTranslation();

  return (
    <AsyncFeatureSlider
      resourceUrl={
        resourceUrl ||
        `parties/${partyId}/leaders/top?contentType=${nextContentType}&limit=10&page=1`
      }
      entityId={partyId || ""}
      renderItems={(item: ILeader) => {
        if (!item) return null;
        return (
          <LeaderHeroProfile
            context={context}
            contentType={contentType}
            additionalInformationEnabled
            renderBeforeAvatar={() =>
              //@ts-expect-error
              +item.scandal_count > 1 ? (
                <Badge radius={"sm"} variant={"filled"}>
                  {/* @ts-ignore */}
                  {item.scandal_count}+
                </Badge>
              ) : null
            }
            data={item}
          />
        );
      }}
      //@ts-expect-error
      transformResponse={(data) => data?.items || []}
      title={t("common:top_contents_leaders", {
        contents: contentType?.map((item) => t(`common:${item}`, item)),
        defaultValue: `Top leaders in {{contents}} `,
      })}
      entityType="party"
    />
  );
};

export default TopContentLeader;
