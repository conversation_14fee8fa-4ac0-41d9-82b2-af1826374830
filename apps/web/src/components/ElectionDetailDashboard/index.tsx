import { useEffect, useState } from "react";
import axios from "axios";
import {
  Container,
  Title,
  Loader,
  Tabs,
  Grid,
  Card,
  Text,
  Table,
  ScrollArea,
  Group,
  Avatar,
  Stack,
  SimpleGrid,
  Button,
} from "@mantine/core";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Chart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  LineChart,
  Line,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { getImageUrlWithFallback } from "@/utils";
import Link from "next/link";
import { groupBy } from "lodash";
import PartyLink from "../Links/PartyLink";
import LeaderLink from "../Links/LeaderLink";

export default function ElectionDashboard({
  electionId,
}: {
  electionId: number;
}) {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useQuery([], async () => {
    const response = await ApiService.resource.getAll(
      `elections/${electionId}/analytics`,
      {}
    );
    setData(response.data);
    setLoading(false);
  });

  if (loading) return <Loader />;

  return (
    <Stack>
      <Title order={2}>{data.summary.localName}</Title>
      <SimpleGrid cols={{ md: 4, xs: 1, sm: 2 }}>
        <StatCard title="Total Votes" value={data.summary.totalVotes} />
        <StatCard title="Candidates" value={data.summary.totalCandidates} />
        <StatCard title="Elected" value={data.summary.totalElected} />
      </SimpleGrid>
      <Tabs mt="xl" defaultValue="party-votes">
        <Tabs.List>
          <Tabs.Tab value="party-votes">Party Vote Share</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="party-votes">
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={data.partyAnalytics}
                dataKey="voteCount"
                nameKey="partyName"
                outerRadius={120}
                fill="red"
                label
              >
                {data.partyAnalytics.map((_: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={_.colorCode || "grey"} />
                ))}
              </Pie>

              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Tabs.Panel>
      </Tabs>
      <TopCandidates data={data.topCandidates} />
      <ClosestContests data={data.closestContests} />
      <GenderStats data={data.genderStats} />
      <CandidacyBreakdown data={data.candidacyBreakdown} />
      <WastedVotes data={data.wastedVotes} />
      <FirstTimeWinnersCard data={data.firstTimeWinners} />
      <WinnerFaces
        title="First-Time Winners"
        leaders={data.newAndRepeatWinners.firstTimers}
      />
      <WinnerFaces
        title="Repeat Winners"
        leaders={data.newAndRepeatWinners.repeatWinners}
      />{" "}
      <PartyPerformanceCategories data={data.comparePartyPerformance} />
      {data.partyComparison?.length && (
        <PartyComparisonChart data={data.partyComparison} />
      )}
    </Stack>
  );
}

function StatCard({ title, value }: { title: string; value: number }) {
  return (
    <Card shadow="md" padding="lg" radius="md" withBorder>
      <Text size="sm" color="dimmed">
        {title}
      </Text>
      <Text fw={700} size="xl">
        {value.toLocaleString()}
      </Text>
    </Card>
  );
}

function TopCandidates({ data }: { data: any }) {
  const parties = groupBy(data, "parties.localName");
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Top Candidates</Title>
      <Group mt={"md"}>
        {Object.keys(parties).map((item) => (
          <Group gap={"xs"}>
            <Avatar src={parties[item][0].parties.logo} size={"sm"} />
            <Text>
              {item} ({parties[item].length})
            </Text>
          </Group>
        ))}
      </Group>{" "}
      <Grid mt="sm">
        {data.map((c: any) => (
          <Grid.Col span={{ md: 4, sm: 2, xs: 2 }} key={c.id}>
            <Card withBorder>
              <Group>
                <Avatar
                  src={getImageUrlWithFallback(
                    c.leaders.img,
                    c.leaders.ecCandidateID
                  )}
                  size={"lg"}
                />
                <Stack gap={1}>
                  <Link
                    href={`/leaders/${c.leaders.id}`}
                    className=" text-inherit"
                  >
                    <Text fw={600}>{c.leaders?.localName}</Text>
                  </Link>
                  <Text size="sm">{c.districts?.localName}</Text>
                  <Text size="sm">{c.parties?.localName}</Text>
                  <Text fw={700}>Votes: {c.voteCount.toLocaleString()}</Text>
                </Stack>
              </Group>
            </Card>
          </Grid.Col>
        ))}
      </Grid>
    </Card>
  );
}

function ClosestContests({ data }: { data: any }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Closest Contests</Title>
      {data.map((contest: any, i: number) => (
        <Card key={i} mt="xs" withBorder>
          <Link
            className="text-inherit no-underline"
            target="_blank"
            href={`/elections/${contest.winner.electionId}/sub/${contest.winner.elCode}/${contest.winner.candidacyTypeId}/home`}
          >
            <Text>{contest.district}</Text>
            <Text size="sm">
              {contest.winner.leaders.localName} vs{" "}
              {contest.runnerUp.leaders.localName}
            </Text>
            <Text size="sm" color="dimmed">
              Margin: {contest.margin.toLocaleString()} votes
            </Text>
          </Link>
        </Card>
      ))}
    </Card>
  );
}

function GenderStats({ data }: { data: any }) {
  const chartData = [
    { name: "Male", value: data.male, color: "#8884d8" },
    { name: "Female", value: data.female, color: "#82ca9d" },
    { name: "Other", value: data.other, color: "#ffc658" },
  ];

  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Candidate Gender Distribution</Title>
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={chartData}
            dataKey="value"
            nameKey="name"
            outerRadius={100}
            label
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </Card>
  );
}

function CandidacyBreakdown({ data }: { data: any }) {
  const chartData = Object.entries(data).map(([type, val]) => {
    const typedVal = val as { total: number; elected: number };
    return {
      type,
      total: typedVal.total,
      elected: typedVal.elected,
    };
  });

  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Candidacy Type Breakdown</Title>
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={chartData}>
          <XAxis dataKey="type" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="total" fill="#8884d8" name="Total Candidates" />
          <Bar dataKey="elected" fill="#82ca9d" name="Elected" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
}

function WastedVotes({ data }: { data: any }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Wasted Votes Analysis</Title>
      <Text>Total Votes: {data.totalVotes.toLocaleString()}</Text>
      <Text>Votes to Elected: {data.winningVotes.toLocaleString()}</Text>
      <Text>Wasted Votes: {data.wastedVotes.toLocaleString()}</Text>
      <Text color="red">Wasted Percentage: {data.wastedPercentage}%</Text>
    </Card>
  );
}

function FirstTimeWinnersCard({ data }: { data: any }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>First-time vs Repeat Winners</Title>
      <Text>Total Elected: {data.totalElected}</Text>
      <Text>First-time Winners: {data.firstTimeWinners}</Text>
      <Text>Repeat Winners: {data.repeatWinners}</Text>
    </Card>
  );
}

function PartyComparisonChart({ data }: { data: any }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Party Seat & Vote Change (from Previous Election)</Title>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data.delta}>
          <XAxis dataKey="partyName" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="voteChange" fill="#8884d8" name="Vote Change" />
          <Bar dataKey="seatChange" fill="#82ca9d" name="Seat Change" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
}

function DistrictSummaryTable({ data }: { data: any }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>District Summaries</Title>
      <ScrollArea>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>District</Table.Th>
              <Table.Th>Total Votes</Table.Th>
              <Table.Th>Elected Leader</Table.Th>
              <Table.Th>Party</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {data.map((d: any, idx: number) => (
              <Table.Tr key={idx}>
                <Table.Td>{d.localName}</Table.Td>
                <Table.Td>{d.totalVotes.toLocaleString()}</Table.Td>
                <Table.Td>{d.electedLeader || "-"}</Table.Td>
                <Table.Td>
                  {d.candidates.find((c: any) => c.isElected)?.party || "-"}
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </ScrollArea>
    </Card>
  );
}

function WinnerFaces({ title, leaders }: { title: string; leaders: any[] }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>{title}</Title>
      <SimpleGrid mt="md" cols={{ md: 5, sm: 3, xs: 2 }}>
        {leaders.map((leader, idx) => (
          <Group key={idx} gap={5} align="center" wrap="nowrap">
            <Avatar src={leader.img || undefined} size={"md"} radius="xl" />
            <LeaderLink id={leader.id} className="">
              <Stack
                className="text-sm font-medium text-center"
                gap={0}
                justify="start"
                align="start"
              >
                {leader.localName}
                {leader.repeatCount > 0 && (
                  <Text size="xs" color="dimmed">
                    {leader.repeatCount} repeat wins
                  </Text>
                )}
                <Group gap={2} align="start" wrap="nowrap" justify="start">
                  {leader.previousYears.length > 0 && (
                    <Text size="xs" color="dimmed" ta={"start"}>
                      {leader.previousYears.join(", ")}
                    </Text>
                  )}
                </Group>
              </Stack>
            </LeaderLink>
          </Group>
        ))}
      </SimpleGrid>
    </Card>
  );
}

function TopPartiesPerformance({ data }: { data: any[] }) {
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Top Performing Parties</Title>
      <Grid>
        {data.map((party, index) => (
          <Grid.Col span={4} key={index}>
            <Card withBorder>
              <Group>
                <Avatar src={party.party.logo} size="lg" radius="xl" />
                <Stack>
                  <Text fw={600}>{party.party.name}</Text>
                  <Text size="sm">Seats: {party.wonPlaces}</Text>
                  <Text size="sm">
                    Votes: {party.voteCount.toLocaleString()}
                  </Text>
                  {party.delta && (
                    <Text
                      size="sm"
                      color={party.delta.seatChange >= 0 ? "green" : "red"}
                    >
                      {party.delta.seatChange >= 0 ? "+" : ""}
                      {party.delta.seatChange} seats
                    </Text>
                  )}
                  {party.isNew && (
                    <Text size="xs" color="blue">
                      New Party
                    </Text>
                  )}
                </Stack>
              </Group>
            </Card>
          </Grid.Col>
        ))}
      </Grid>
    </Card>
  );
}

function SeatDistributionChart({ data }: { data: any[] }) {
  const totalSeats = data.reduce((acc, cur) => acc + cur.wonPlaces, 0);
  return (
    <Card mt="xl" withBorder>
      <Title order={4}>Seat Distribution</Title>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} layout="vertical">
          <XAxis type="number" />
          <YAxis dataKey="party.name" type="category" width={150} />
          <Tooltip />
          <Legend />
          <Bar dataKey="wonPlaces">
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.party.partyColorCode || "grey"}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      <Group mt="md" justify="space-between">
        <Text>0</Text>
        <Text>Majority: 301</Text>
        <Text>{totalSeats}</Text>
      </Group>
    </Card>
  );
}

function PartyPerformanceCategories({ data }: { data: any }) {
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});

  const renderCategory = (title: string, parties: any[]) => {
    const isExpanded = expanded[title] || false;
    const shownParties = isExpanded ? parties : parties.slice(0, 8);

    if (!parties.length) return null;

    return (
      <Card mt="xl" withBorder>
        <Title order={4}>{title}</Title>
        <SimpleGrid cols={{ md: 4, sm: 2, xs: 1 }} spacing="lg" mt="md">
          {shownParties.map((party, index) => (
            <Card key={`${title}-${index}`} withBorder>
              <Stack justify="center" align="center">
                <Avatar src={party.party.logo} size="lg" radius="xl" />
                <Stack gap={1} align="center">
                  <PartyLink id={party.party.id}>
                    <Text fw={600} className="line-clamp-1">
                      {party.party.name}
                    </Text>
                  </PartyLink>

                  <Text size="sm">
                    Seats: {party.wonPlaces}{" "}
                    <Text span size="xs" color="dimmed">
                      (Prev: {party.previousSeats} •{" "}
                      {party.previousSeatPercent?.toFixed(1)}%)
                    </Text>
                  </Text>

                  <Text size="sm">
                    Votes: {party.voteCount?.toLocaleString()}{" "}
                    <Text span size="xs" color="dimmed">
                      (Prev: {party.previousVotes?.toLocaleString()} •{" "}
                      {party.previousVotePercent?.toFixed(1)}%)
                    </Text>
                  </Text>

                  {/* Seat diff */}
                  {(party.seatDiff !== undefined && (
                    <Text
                      size="sm"
                      color={party.seatDiff >= 0 ? "green" : "red"}
                    >
                      {party.seatDiff >= 0 ? "+" : ""}
                      {party.seatDiff} seats (
                      {party.seatDiffPercent?.toFixed(1)}%)
                    </Text>
                  )) ||
                    null}

                  {/* Vote diff */}
                  {(party.voteDiff !== undefined && (
                    <Text
                      size="sm"
                      color={party.voteDiff >= 0 ? "green" : "red"}
                    >
                      {party.voteDiff >= 0 ? "+" : ""}
                      {party.voteDiff.toLocaleString()} votes (
                      {party.voteDiffPercent?.toFixed(1)}%)
                    </Text>
                  )) ||
                    null}

                  {/* Vote share */}
                  {(party.votePercent && (
                    <Text size="xs" color="dimmed">
                      Current Vote Share: {party.votePercent.toFixed(2)}%
                    </Text>
                  )) ||
                    null}

                  {/* Seat share */}
                  {(party.seatPercent && (
                    <Text size="xs" color="dimmed">
                      Current Seat Share: {party.seatPercent.toFixed(2)}%
                    </Text>
                  )) ||
                    null}
                </Stack>
              </Stack>
            </Card>
          ))}
        </SimpleGrid>

        {parties.length > 8 && (
          <Group justify="center" mt="md">
            <Button
              size="xs"
              variant="light"
              onClick={() =>
                setExpanded((prev) => ({
                  ...prev,
                  [title]: !isExpanded,
                }))
              }
            >
              {isExpanded ? "Show Less" : "Show More"}
            </Button>
          </Group>
        )}
      </Card>
    );
  };

  return (
    <>
      {renderCategory("New Parties", data.newParties)}
      {renderCategory("Emerging Parties", data.emergingParties)}
      {renderCategory("Improving Parties", data.improvingParties)}
      {renderCategory("Declining Parties", data.decliningParties)}
    </>
  );
}

export {
  TopPartiesPerformance,
  SeatDistributionChart,
  PartyPerformanceCategories,
};
