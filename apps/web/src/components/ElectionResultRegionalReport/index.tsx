import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import DistrictCard from "../DistrictCard";

const districts = {
  top: [
    {
      name: "citvn",
      localName: "चितवन",
      wins: 2,
      voteReceivedPercentage: 37.57,
      voteLossPercentage: 261758.42,
      allTotalVotes: 261796,
      votesRecieved: 98361,
      losses: 1,
      winPercentage: 66.67,
      lossPercentage: 33.33,
      topPerformer: {
        area: "2",
        isElected: true,
        voteCount: 49300,
        districtId: 29,
        stateId: 3,
        states: { name: "baagmtii prdesh", localName: "बागमती प्रदेश" },
        districts: { name: "citvn", localName: "चितवन" },
      },
      elections: [
        { area: "2", isElected: true, voteCount: 49300 },
        { area: "1", isElected: true, voteCount: 34218 },
        { area: "3", isElected: false, voteCount: 14843 },
      ],
    },
  ],
  bottom: [
    {
      name: "Man<PERSON>",
      localName: "मनाङ्ग",
      wins: 0,
      voteReceivedPercentage: 0.1,
      voteLossPercentage: 4826.89,
      allTotalVotes: 4827,
      votesRecieved: 5,
      losses: 1,
      winPercentage: 0,
      lossPercentage: 100,
      topPerformer: {
        area: "1",
        isElected: false,
        voteCount: 5,
        districtId: 31,
        stateId: 4,
        states: { name: "gnnddkii prdesh", localName: "गण्डकी प्रदेश" },
        districts: { name: "Manang", localName: "मनाङ्ग" },
      },
      elections: [{ area: "1", isElected: false, voteCount: 5 }],
    },
  ],
};

const DistrictPerformance = (props: { districts: typeof districts }) => {
  const { t } = useTranslation();
  return (
    <Group>
      <h1 className="text-2xl font-bold mb-4">
        {t("common:district_performance", "District Performance")}
      </h1>
      {props.districts.top?.map((district, idx) => (
        <DistrictCard
          direction="up"
          key={`top-${idx}`}
          title={t("common:top_district", "Top District")}
          district={district}
        />
      ))}
      {props.districts.bottom?.map((district, idx) => (
        <DistrictCard
          direction="down"
          key={`bottom-${idx}`}
          title={t("common:bottom_district", "Bottom District")}
          district={district}
        />
      ))}
    </Group>
  );
};

export default DistrictPerformance;
