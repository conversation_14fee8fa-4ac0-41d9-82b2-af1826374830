import { Card, Progress, Text } from "@mantine/core";

const GovernmentStats = () => {
  const data = [
    { label: "Mobile", count: "204,001", part: 59, color: "#47d6ab" },
    { label: "Desktop", count: "121,017", part: 35, color: "#03141a" },
    { label: "Tablet", count: "31,118", part: 6, color: "#4fcdf7" },
  ];

  return (
    <Card withBorder radius="md" padding="xl" bg="var(--mantine-color-body)">
      <Text fz="xs" tt="uppercase" fw={700} c="dimmed">
        Promise achieved
      </Text>
      <Text fz="lg" fw={500}>
        $5.431 / $10.000
      </Text>
      <Progress value={54.31} mt="md" size="lg" radius="xl" />
    </Card>
  );
};
export default GovernmentStats;
