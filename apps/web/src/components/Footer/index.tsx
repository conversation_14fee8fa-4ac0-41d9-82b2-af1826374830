import {
  IconBrandFacebook,
  IconBrandInstagram,
  IconBrandTwitter,
  IconBrandYoutube,
} from "@tabler/icons-react";
import {
  ActionIcon,
  Anchor,
  Box,
  Group,
  useMantineColorScheme,
} from "@mantine/core";
import classes from "./style.module.css";
import Link from "next/link";

const links = [
  { link: "mailto:<EMAIL>", label: "Contact" },
  { link: "/privacy-policy", label: "Privacy" },
  { link: "/terms", label: "Terms" },
];

export function FooterCentered() {
  const items = links.map((link) => (
    <Anchor c="dimmed" key={link.label} href={link.link} lh={1} size="sm">
      {link.label}
    </Anchor>
  ));
  const { colorScheme } = useMantineColorScheme();

  return (
    <div className={classes.footer}>
      <div className={classes.inner}>
        <Box visibleFrom="md">
          <img
            src={
              colorScheme === "dark"
                ? "/logos/nepaltracks-dark.png"
                : "/logos/nepaltracks.png"
            }
            alt="logo"
            width={200}
            height={25}
          />
        </Box>
        <Box hiddenFrom="md">
          <img src={"/favicon.ico"} alt="logo" width={24} height={24} />
        </Box>
        <Group className={classes.links}>{items}</Group>

        <Group gap="xs" justify="flex-end" wrap="nowrap">
          <Link href="https://www.x.com/nepaltracks" target="_blank">
            <ActionIcon size="lg" variant="default" radius="xl">
              <IconBrandTwitter size={18} stroke={1.5} />
            </ActionIcon>
          </Link>
          <Link href="https://www.facebook.com/nepaltracks" target="_blank">
            <ActionIcon size="lg" variant="default" radius="xl">
              <IconBrandFacebook size={18} stroke={1.5} />
            </ActionIcon>
          </Link>
        </Group>
      </div>
    </div>
  );
}
export default FooterCentered;
