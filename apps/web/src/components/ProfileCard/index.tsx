import { GenderType } from "@/interfaces/ILeader";
import { formatDate, getAgeFromDate } from "@/utils";
import {
  Avatar,
  Badge,
  Box,
  Divider,
  Flex,
  Group,
  Image,
  Paper,
  Rating,
  RatingProps,
  Stack,
  Text,
  Title,
  Tooltip,
  rem,
} from "@mantine/core";
import { IconGenderFemale, IconGenderMale } from "@tabler/icons-react";
import Link from "next/link";
import SocialLinks from "../SocialLinks";

interface IProps {
  rating: number;
  name?: string;
  img?: string;
  onChange?: RatingProps["onChange"];
  gender?: GenderType;
  birthDate?: string;
  id?: number;
  renderInformation?: () => React.ReactNode;
  fbPage?: string;
  twitterPage?: string;
  youtubePage?: string;
}
const ProfileCard = (props: IProps) => {
  return (
    <Paper mih={"10rem"} p={16} shadow="md" className=" sticky top-16">
      <Stack p={10} align="center" justify="center" gap={5}>
        <Avatar radius="md" w={250} h={250} src={props.img} />

        <Group gap={10}>
          <Title order={3}>{props.name}</Title>
          <Badge color="gray" size="md">
            {props.birthDate ? getAgeFromDate(props.birthDate) : "N/A"}
          </Badge>
        </Group>
        <Group gap={5}>
          <Text size="sm" fw={500}>
            {props.birthDate ? formatDate(props.birthDate) : null}
          </Text>
          <Divider orientation="vertical" />
          <Badge color={"cyan"} size="md">
            <Box mt={5}>
              {props.gender ? (
                // @ts-ignore
                props.gender === "MALE" ? (
                  <IconGenderMale
                    aria-label={props.gender}
                    stroke={2}
                    size={"1rem"}
                  />
                ) : (
                  // @ts-ignore
                  <IconGenderFemale
                    aria-label={props.gender}
                    color="pink"
                    size={"1rem"}
                    stroke={2}
                  />
                )
              ) : null}
            </Box>
          </Badge>
        </Group>
        <SocialLinks
          links={{
            facebook: props.fbPage,
            twitter: props.twitterPage,
            youtube: props.youtubePage,
          }}
        />
        <Divider />
      </Stack>
      {/* <Divider /> */}
      <Flex mt={10} direction={"column"}>
        {props.renderInformation?.()}
      </Flex>
    </Paper>
  );
};
export default ProfileCard;
