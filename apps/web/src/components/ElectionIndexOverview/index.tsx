import { IElectionOverview } from "@/interfaces/IElectionOverview";
import {
  Card,
  Title,
  Table,
  SimpleGrid,
  Text,
  Group,
  Badge,
  Avatar,
  Stack,
  Tabs,
} from "@mantine/core";
import {
  Bar<PERSON>hart,
  LineChart,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Bar,
  Line,
  ResponsiveContainer,
} from "recharts";
import LeaderInfoProfile from "../LeaderInfoProfile";
import { getImageUrlWithFallback } from "@/utils";
import LeaderLink from "../Links/LeaderLink";
import { ConsistentWinnersCard } from "../ConsistentWinnersCard";
import { NewPartyLaunches } from "../NewPartyLaunches";
import { FadingPartiesTimeline } from "../FadingPartiesLineChart";
import { FeaturedSlider } from "../FeatutredSlider";
import PartyProfile from "../PartyProfile";
import LeaderHeroProfile from "../LeaderHeroProfile";
import { VoteCountBox } from "../VoteCountBox";
import { SimpleGridCardContainer } from "../SimpleGridCardContainer";
import SimpleCardItem from "../SimpleCardItem";
import { TopPerformPerElections } from "../TopPerformPerElections";
import { TopPartiesPerElection } from "../TopPartiesPerElections";
import { SectionTitle } from "../SectionTitle";
import { PartyLineChart } from "../PartyTrendChart";
import LeaderTrendChart from "../LeaderTrendChart";
import Link from "next/link";
import ElectionLink from "../Links/ElectionLink";

export function ElectionsOverview({ data }: { data: IElectionOverview }) {
  const renderTable = (
    title: string,
    headers: string[],
    rows: JSX.Element[]
  ) => (
    <Card withBorder mt="xl">
      <Title order={3}>{title}</Title>
      <Table>
        <Table.Thead>
          <Table.Tr>
            {headers.map((h, i) => (
              <Table.Th key={i}>{h}</Table.Th>
            ))}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    </Card>
  );

  return (
    <div className="space-y-12">
      {/* <Card withBorder mt="xl">
        <Title order={3}>Top 10 Party Trends (Votes vs Seats)</Title>
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={data.partyTrends.slice(0, 10)}>
            <XAxis dataKey="partyName" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="votes" fill="#8884d8" />
            <Bar dataKey="seats" fill="#82ca9d" />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card withBorder mt="xl">
        <Title order={3}>Vote vs Seat Trends Over Time</Title>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data.voteSeatTrends}>
            <XAxis dataKey="year" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="votes"
              stroke="#8884d8"
              name="Votes"
            />
            <Line
              type="monotone"
              dataKey="seats"
              stroke="#82ca9d"
              name="Seats"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card> */}
      <FeaturedSlider
        title="Big Debut Winners"
        data={data?.bigDebutWinners || []}
        renderItems={(item) => {
          return (
            <LeaderHeroProfile
              data={item.leader}
              renderTopRightSection={() => <Badge>{item.debutYear}</Badge>}
              renderDescription={() => {
                return (
                  <Group>
                    <VoteCountBox count={item.debutVotes} />
                  </Group>
                );
              }}
            />
          );
        }}
      />

      <FeaturedSlider
        title="Poor Performer"
        data={data?.repeatLosers || []}
        renderItems={(item) => {
          return (
            <LeaderHeroProfile
              data={item.leader}
              renderDescription={() => {
                return (
                  <Text size="sm" c={"dimmed"}>
                    Lost {item.losses} out of {item.total} elections
                  </Text>
                );
              }}
            />
          );
        }}
      />
      <ConsistentWinnersCard data={data.consistentWinners} />

      <SimpleGridCardContainer
        data={data.leaderSwitches}
        title="Leader Switches"
        renderCardContent={(item: (typeof data.leaderSwitches)[0]) => (
          <SimpleCardItem
            texts={{
              Switches: <b>{item.switches} times</b>,
            }}
            title={item.leader.localName}
            image={getImageUrlWithFallback(
              item.leader.img,
              item.leader.ecCandidateID
            )}
          />
        )}
      />

      <SimpleGridCardContainer
        data={data.biggestWinMargins}
        title="Biggest Win Margins"
        renderCardContent={(item: (typeof data.biggestWinMargins)[0]) => (
          <SimpleCardItem
            texts={{
              Margin: <VoteCountBox count={item.margin} />,
              Yeat: item.year,
              District: item.district,
              RunnerUp: item.runnerUp,
            }}
            title={item.winner}
            image={getImageUrlWithFallback(
              item.leader.img,
              item.leader.ecCandidateID
            )}
          />
        )}
      />

      <SimpleGridCardContainer
        data={data.closeContests}
        title="Close Contests"
        renderCardContent={(item: (typeof data.closeContests)[0]) => (
          <SimpleCardItem
            texts={{
              Margin: <VoteCountBox count={item.margin} />,
              Yeat: item.year,
              District: item.district,
              RunnerUp: item.runnerUp,
            }}
            title={item.winner}
            image={getImageUrlWithFallback(
              item.leader.img,
              item.leader.ecCandidateID
            )}
          />
        )}
      />

      <FeaturedSlider
        title="Consistent Parties"
        data={data?.consistentParties || []}
        renderItems={(item) => {
          return (
            <PartyProfile
              renderTitle={() => "ssm"}
              data={item.party}
              renderInformation={() => {
                return (
                  <Text size="xs" c={"dimmed"}>
                    {item.winYears.join(",")}
                  </Text>
                );
              }}
              renderDescription={() => (
                <>{item.consistencyRatio.toFixed(2)}% ratio</>
              )}
            />
          );
        }}
      />
      <FeaturedSlider
        title="Struggling Parties"
        data={data?.strugglingParties || []}
        renderItems={(item) => {
          return (
            <PartyProfile
              data={item.party}
              renderInformation={() => {
                return <Group>{item.wins}</Group>;
              }}
              renderDescription={() => <>{item.wins} Wins</>}
            />
          );
        }}
      />
      <SectionTitle>
        Leader Trends over Time{" "}
        <Link href="/elections?tab=trends" className="text-sm">
          See more
        </Link>
      </SectionTitle>
      {/* @ts-expect-error */}
      <LeaderTrendChart data={data.leaderTrends || []} />
      <SectionTitle>Party Trends Votes</SectionTitle>
      <Tabs defaultValue="vote">
        <Tabs.List>
          <Tabs.Tab value="vote">Vote Trends in %</Tabs.Tab>
          <Tabs.Tab value="vote1">Vote Trends </Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="vote">
          <PartyLineChart data={data.partyTrendsSeries} mode="vote" />
        </Tabs.Panel>
        <Tabs.Panel value="vote1">
          <PartyTrendsLineChart data={data.partyTrends} />
        </Tabs.Panel>
      </Tabs>

      <SectionTitle>Party Trends Seats</SectionTitle>
      <Tabs defaultValue="seat">
        <Tabs.List>
          <Tabs.Tab value="seat">Vote Trends in %</Tabs.Tab>
          <Tabs.Tab value="seat1">Vote Trends </Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="seat">
          <PartyLineChart data={data.partyTrendsSeries} mode="seat" />
        </Tabs.Panel>
        <Tabs.Panel value="seat1">
          <PartyTrendsLineChart data={data.partyTrends} valueKey="seats" />
        </Tabs.Panel>
      </Tabs>

      {/* <SectionTitle>Party Trends Votes</SectionTitle>
      <PartyLineChart data={data.partyTrendsSeries} mode="vote" />
      <SectionTitle>Party Trends Seats</SectionTitle>
      <PartyLineChart data={data.partyTrendsSeries} mode="seat" />

      <PartyTrendsLineChart data={data.partyTrends} valueKey="seats" />
      <PartyTrendsLineChart data={data.partyTrends} /> */}
      <Stack gap={1}>
        <Card>
          <SectionTitle>Top Performers Leaders</SectionTitle>
          <TopPerformPerElections data={data.topLeadersPerElection || []} />
        </Card>
      </Stack>
      <Stack gap={1}>
        <Card>
          <SectionTitle>Top Performing Parties</SectionTitle>
          <TopPartiesPerElection data={data.topPartiesPerElection || []} />
        </Card>
      </Stack>
      <Stack gap={1}>
        <Card>
          <SectionTitle>Top Performing New Parties</SectionTitle>
          <NewPartyLaunches data={data.newPartyLaunches || []} />
        </Card>
      </Stack>

      {/* <FadingPartiesTimeline
        partyTrendsSeries={data.partyTrendsSeries}
        fadingPartyIds={data.fadingParties.map((p) => p.partyId)}
      />  */}

      {renderTable(
        "Fading Parties",
        ["Name", "Last Year", "Recent Seats"],
        data.fadingParties.map((p) => (
          <Table.Tr key={p.partyId}>
            <Table.Td>{p.name}</Table.Td>
            <Table.Td>{p.lastYear}</Table.Td>
            <Table.Td>{p.recentSeats}</Table.Td>
          </Table.Tr>
        ))
      )}

      {/* <Card withBorder mt="xl">
        <Title order={3}>Party Trends Series</Title>
        <ResponsiveContainer width="100%" height={350}>
          <LineChart data={data.partyTrendsSeries}>
            <XAxis dataKey="electionYear" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="votePercent"
              stroke="#8884d8"
              name="Vote %"
            />
            <Line
              type="monotone"
              dataKey="seatPercent"
              stroke="#82ca9d"
              name="Seat %"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card> */}

      {/* <Card withBorder mt="xl">
        <Title order={3}>Leader Trends</Title>
        <SimpleGrid cols={3} spacing="lg">
          {data.leaderTrends.map((l) => (
            <Card key={l.leaderId} withBorder>
              <Text fw={600}>{l.name}</Text>
              <ResponsiveContainer width="100%" height={150}>
                <LineChart data={l.results}>
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Line dataKey="won" stroke="#22c55e" />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          ))}
        </SimpleGrid>
      </Card> */}
    </div>
  );
}

export function PartyTrendsLineChart({ data, valueKey = "votes" }: any) {
  // Group data by party
  const groupedByParty = Object.values(
    data.reduce((acc: Record<number, any[]>, row: any) => {
      if (!acc[row.partyId]) acc[row.partyId] = [];
      acc[row.partyId].push(row);
      return acc;
    }, {})
  );

  // Extract unique years
  const electionYears = Array.from(
    new Set(data.map((d: any) => new Date(d.electionYear).getFullYear()))
    // @ts-expect-error
  ).sort((a, b) => a - b);

  // Build party series
  const partyLines = groupedByParty
    // @ts-expect-error
    .filter((group) => group.length > 1)
    .map((group) => {
      // @ts-expect-error
      const party = group[0];
      const series = electionYears.map((year) => {
        // @ts-expect-error
        const found = group.find(
          // @ts-expect-error
          (r) => new Date(r.electionYear).getFullYear() === year
        );
        return {
          year,
          [party.partyName]: found ? found[valueKey] : 0,
        };
      });
      return { party, series };
    });

  // Merge data for recharts
  const mergedData = electionYears.map((year) => {
    const merged: any = { year };
    for (const { series } of partyLines) {
      const point = series.find((r) => r.year === year);
      if (point) Object.assign(merged, point);
    }
    return merged;
  });

  return (
    <Card withBorder mt="xl">
      <Title order={4} mb="md">
        Party {valueKey === "votes" ? "Vote" : "Seat"} Trends Over Time
      </Title>

      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={mergedData}>
          <XAxis dataKey="year" />
          <YAxis />
          <Tooltip />
          <Legend />
          {partyLines.map(({ party }) => (
            <Line
              key={party.partyId}
              type="monotone"
              dataKey={party.partyName}
              stroke={party.color || "#888"}
              strokeWidth={2}
              dot={{
                stroke: party.color || "#888",
                strokeWidth: 2,
                r: 4,
              }}
              activeDot={{
                r: 6,
                stroke: party.color || "#888",
                strokeWidth: 3,
                fill: "#fff",
              }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>

      <Group wrap="wrap" mt="md" gap="xs">
        {partyLines.map(({ party }) => (
          <Badge
            key={party.partyId}
            size="lg"
            color={party.color || "gray"}
            variant="light"
            leftSection={
              party.logo ? (
                <Avatar src={party.logo} size="xs" radius="xl" />
              ) : (
                <span
                  style={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    backgroundColor: party.color || "#888",
                    display: "inline-block",
                  }}
                />
              )
            }
          >
            {party.localName || party.partyName}
          </Badge>
        ))}
      </Group>
    </Card>
  );
}
