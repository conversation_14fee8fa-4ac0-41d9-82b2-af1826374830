import { IconCheck } from "@tabler/icons-react";
import {
  Box,
  Button,
  Container,
  Group,
  Image,
  List,
  Overlay,
  Rating,
  Text,
  ThemeIcon,
  Title,
} from "@mantine/core";
import classes from "./style.module.css";
import { ILeader } from "@/interfaces/ILeader";
import { getImageUrlWithFallback } from "@/utils";
import Link from "next/link";

export function HeroBanner({ item }: { item: ILeader }) {
  return (
    <Box className={classes.hero + "  "}>
      <Overlay
        gradient="linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, .65) 40%)"
        opacity={1}
        zIndex={0}
        className="bg-cover bg-center scale-110 blur-md"
      />

      <Container size="md">
        <div className={classes.inner}>
          <div className={classes.content} style={{ zIndex: 1 }}>
            <Title className={classes.title}>{item.localName}</Title>
            <Rating value={item.rating?.average || 0} readOnly />

            <List
              mt={30}
              spacing="sm"
              size="sm"
              icon={
                <ThemeIcon size={20} radius="xl">
                  <IconCheck size={12} stroke={1.5} />
                </ThemeIcon>
              }
            >
              <List.Item>{item.summaryNP}</List.Item>
            </List>

            <Group mt={30}>
              <Link href={`/leaders/${item.id}`}>
                <Button radius="xl" size="md" className={classes.control}>
                  Read more
                </Button>
              </Link>
            </Group>
          </div>
          <Image
            style={{ zIndex: 1 }}
            radius={"md"}
            src={getImageUrlWithFallback(item.img, item.ecCandidateID)}
            className={classes.image}
          />
        </div>
      </Container>
    </Box>
  );
}
