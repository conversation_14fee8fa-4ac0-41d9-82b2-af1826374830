import { Card, Title, Stack, Group, Text, ScrollArea } from "@mantine/core";
import { IconArrowNarrowRight } from "@tabler/icons-react";
import { IElectionOverview } from "@/interfaces/IElectionOverview";

interface Props {
  partyTrendsSeries: IElectionOverview["partyTrendsSeries"];
  fadingPartyIds: number[];
}

export function FadingPartiesTimeline({
  partyTrendsSeries,
  fadingPartyIds,
}: Props) {
  const filtered = partyTrendsSeries.filter((d) =>
    fadingPartyIds.includes(d.partyId)
  );

  const groupedByParty = filtered.reduce<Record<number, typeof filtered>>(
    (acc, row) => {
      if (!acc[row.partyId]) acc[row.partyId] = [];
      acc[row.partyId].push(row);
      return acc;
    },
    {}
  );

  const partySeries = Object.values(groupedByParty).map((series) => {
    const sorted = [...series].sort((a, b) => a.electionYear - b.electionYear);
    const party = sorted[0];
    return {
      partyId: party.partyId,
      name: party.name,
      localName: party.localName,
      color: party.color,
      trends: sorted.map((s) => ({
        electionYear: s.electionYear,
        seatCount: s.seatCount,
      })),
    };
  });

  return (
    <Card withBorder mt="xl">
      <Title order={3}>Fading Parties Overview</Title>
      <Stack mt="md" gap="lg">
        {partySeries.map((party) => (
          <Card key={party.partyId} withBorder shadow="xs">
            <Group align="stretch" wrap="nowrap" justify="space-between">
              {/* Party Info */}
              <Group gap="xs" w={200}>
                <span
                  style={{
                    display: "inline-block",
                    width: 12,
                    height: 12,
                    borderRadius: "50%",
                    backgroundColor: party.color || "#999",
                  }}
                />
                <Text fw={600} lineClamp={2}>
                  {party.localName || party.name}
                </Text>
              </Group>

              {/* Timeline */}
              <ScrollArea scrollbars="x" offsetScrollbars w="100%">
                <Group gap="xl" wrap="nowrap">
                  {party.trends.map((point, idx) => {
                    const next = party.trends[idx + 1];
                    const isDecline = next && point.seatCount > next.seatCount;

                    return (
                      <Group gap="xs" key={point.electionYear}>
                        <Stack gap={0} align="center">
                          <Text size="sm" fw={500}>
                            {point.electionYear}
                          </Text>
                          <Text
                            size="sm"
                            fw={600}
                            color={isDecline ? "red" : "black"}
                          >
                            {point.seatCount}
                          </Text>
                        </Stack>

                        {next && (
                          <IconArrowNarrowRight
                            size={20}
                            color={isDecline ? "red" : "gray"}
                          />
                        )}
                      </Group>
                    );
                  })}
                </Group>
              </ScrollArea>
            </Group>
          </Card>
        ))}
      </Stack>
    </Card>
  );
}
