import { getImageUrlWithFallback } from "@/utils";
import { HeroCard } from "../HeroCard";
import { IParty } from "@/interfaces/IParty";
import Link from "next/link";

const PartyHeroCard = ({ party: item }: { party: IParty }) => {
  return (
    <HeroCard
      image={getImageUrlWithFallback(item.logo, item.id + "")}
      title={
        <Link
          href={`/parties/${item.id}`}
          style={{ color: "white", textDecoration: "none" }}
        >
          {item.localName}
        </Link>
      }
      more=" "
    />
  );
};

export default PartyHeroCard;
