import { formatNumber } from "@/utils";
import { Badge, BadgeProps, Text } from "@mantine/core";
import { IconChartBar } from "@tabler/icons-react";

const VoteCountBox = (props: {
  count: number;
  variant?: BadgeProps["variant"];
}) => {
  return (
    <Badge
      size="xs"
      variant={props.variant || "white"}
      leftSection={<IconChartBar size={"1rem"} />}
    >
      <Text fw={"bold"}>{formatNumber(props.count)}</Text>
    </Badge>
  );
};

export { VoteCountBox };
