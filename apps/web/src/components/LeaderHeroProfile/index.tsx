"use client";

import { ILeader } from "@/interfaces/ILeader";
import { getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import Link from "next/link";
import {
  Card,
  Image,
  Text,
  Badge,
  Group,
  Rating,
  Stack,
  Box,
  Avatar,
  useMantineTheme,
  ScrollArea,
  Skeleton,
  useMantineColorScheme,
  Popover,
  Button,
  Indicator,
} from "@mantine/core";
import { IconCake } from "@tabler/icons-react";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { useMemo, useRef, useState } from "react";
import { useMediaQuery } from "@mantine/hooks";
import { SearchResultListService } from "@/containers/SearchResultListService";
import useLeaderImages from "@/hooks/useLeaderImages";
import { useDelayedHover } from "@/hooks/useDelayedHover";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";

const LeaderHeroProfile = ({
  data,
  additionalInformationEnabled,
  renderDescription,
  renderTopRightSection,
  renderBeforeAvatar,
  isCakeDay,
  contentType,
  context,
}: {
  renderTopRightSection?: (params: { age: number }) => React.ReactNode;
  additionalInformationEnabled?: boolean;
  renderBeforeAvatar?: (params: { age: number }) => React.ReactNode;
  data?: ILeader;
  isCakeDay?: boolean;
  renderDescription?: (params: {
    age: number;
  }) => React.ReactNode | JSX.Element;
  contentType?: string[];
  context?: EntityTypeEnum;
}) => {
  if (!data) return null;
  const theme = useMantineTheme();
  const mobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);
  const { colorScheme } = useMantineColorScheme();
  const { hoveredLeaders, markHovered } = useLeaderHover();

  const age = getAgeFromDate(data.birthDate);
  const params = { age, data };

  const { defaultImage, randomImage } = useLeaderImages(data);
  const [canLoadSearchResult, setCanLoadSearchResult] = useState(false);
  const { isHovering, canTrigger, hasHovered, onMouseEnter, onMouseLeave } =
    useDelayedHover({
      enabled: !mobile && additionalInformationEnabled && !canLoadSearchResult,
    });
  const shouldRenderContent = canLoadSearchResult || canTrigger;
  const isHovered = hoveredLeaders[data.id + ""];
  const renderSearchResultList = useMemo(() => {
    return (
      <Box
        bg={colorScheme === "dark" ? theme.colors.dark[6] : "white"}
        p={"sm"}
        className="p-4rounded-md animate__animated animated_fadeIn min-h-60  border w-[300px]"
      >
        <SearchResultListService
          onDataLoad={() => {
            setCanLoadSearchResult(true);
            markHovered(data.id + "");
          }}
          query={data.localName}
          resourceId={data.id}
          resourceType="LEADER"
          contentType={
            contentType || [
              "NEWS",
              "SCANDAL",
              "CONTROVERSIES",
              "ACHIEVEMENTS",
              "MILESTONES",
              "ANNOUNCEMENTS",
              "PROMISES",
            ]
          }
          context={context}
          renderNoResults={() => (
            <>
              <Text className="text-md font-semibold mb-2">
                {data.localName} ({data.name})
              </Text>{" "}
              <Text className="text-sm mb-1">Address: {data.address}</Text>
              <Text className="text-sm  mb-1">Gender: {data.gender}</Text>
              <Text className="text-sm  mb-1">
                Rating: {data.rating?.average ?? "N/A"}
              </Text>
              <Text className="text-sm ">{data.description || ""}</Text>
            </>
          )}
        />
      </Box>
    );
  }, [data]);
  return (
    <Box
      className="flex group transition-all duration-700 ease-in-out items-start animate__animated animate__fadeIn"
      h={"100%"}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {/* Card Section */}
      <Card
        radius="md"
        withBorder
        className="w-full md:w-72 md:shrink-0 relative transition-all"
        h={"100%"}
      >
        <Link
          href={`/leaders/${data.id}`}
          className="no-underline text-inherit"
        >
          <Card.Section className="relative group overflow-hidden">
            <div className="transition-transform duration-300 ease-in-out group-hover:scale-105">
              <Image
                src={getImageUrlWithFallback(randomImage, data.ecCandidateID)}
                fallbackSrc={
                  data.gender === "MALE"
                    ? age < 40
                      ? "/images/ecat-fallback-2.webp"
                      : "/images/ecat-fallback.webp"
                    : "/images/ecat-fallback-female.webp"
                }
                height={160}
                alt={data.localName}
              />
            </div>

            <div className="absolute bottom-2 right-2">
              {renderBeforeAvatar?.(params)}
            </div>

            <div className="absolute top-2 right-2">
              {renderTopRightSection?.(params)}
            </div>
          </Card.Section>
        </Link>
        <Group
          className="absolute top-2 left-2 overflow-hidden "
          wrap="nowrap"
          gap={5}
        >
          {data.images &&
            (!mobile ? (
              <Indicator
                withBorder
                inline
                size={10}
                offset={5}
                disabled={isHovered}
              >
                <Avatar
                  variant="filled"
                  size="md"
                  radius="xl"
                  src={getImageUrlWithFallback(
                    defaultImage,
                    data.ecCandidateID
                  )}
                  style={{ border: "2px solid white", boxSizing: "border-box" }}
                />
              </Indicator>
            ) : (
              <Popover position="bottom" withArrow>
                <Popover.Target>
                  <Indicator
                    withBorder
                    inline
                    size={10}
                    offset={5}
                    disabled={isHovered}
                  >
                    <Avatar
                      variant="filled"
                      size="md"
                      radius="xl"
                      src={getImageUrlWithFallback(
                        defaultImage,
                        data.ecCandidateID
                      )}
                      style={{
                        border: "2px solid white",
                        boxSizing: "border-box",
                      }}
                    />
                  </Indicator>
                </Popover.Target>
                <Popover.Dropdown>
                  <ScrollArea h={"17rem"} type="auto">
                    {renderSearchResultList}
                  </ScrollArea>
                </Popover.Dropdown>
              </Popover>
            ))}
        </Group>

        <Stack align="start" mt="lg" mb="xs" gap={0}>
          <Link
            href={`/leaders/${data.id}`}
            className="text-inherit no-underline"
          >
            <Group
              className={`${isCakeDay ? "animate-pulse" : ""}`}
              justify="start"
              align="center"
              gap={5}
            >
              {isCakeDay && <IconCake size={14} color="pink" />}
              <Text fw={500} className="text-md md:text-md">
                {data.localName}
              </Text>
              <Badge color="gray" size="xs">
                {age}
              </Badge>
            </Group>
          </Link>
          {/* @ts-expect-error */}
          {(data.rating !== undefined || data.average_rating) && (
            <Rating
              // @ts-expect-error
              value={data?.rating?.average || +data.average_rating}
              readOnly
              size="xs"
            />
          )}
          {renderDescription ? (
            renderDescription?.(params)
          ) : (
            <Text className="text-xs md:text-xs line-clamp-1" c="dimmed">
              {data.address}
            </Text>
          )}
        </Stack>
      </Card>

      {/* Right Panel */}
      {!mobile && additionalInformationEnabled && (
        <div
          className={`
          max-w-0 group-hover:max-w-[500px] opacity-0 group-hover:opacity-100 
          overflow-hidden ml-0 group-hover:ml-4 transition-all duration-700 ease-in-out
          min-h-60 max-h-60 overflow-y-hidden w-[300px]
        `}
        >
          {/* {!canTrigger && (
            <Skeleton height={300} width={"100%"} className=" " />
          )}{" "} */}
          {shouldRenderContent ? (
            <ScrollArea h={"17rem"} type="auto">
              {renderSearchResultList}
            </ScrollArea>
          ) : (
            <Skeleton
              height={200}
              width={"100%"}
              className="animate__animated animated_fadeIn"
            />
          )}
        </div>
      )}
    </Box>
  );
};

export default LeaderHeroProfile;
