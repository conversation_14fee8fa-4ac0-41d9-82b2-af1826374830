/* eslint-disable max-classes-per-file */
import { Avatar } from "@mantine/core";
import React, { PureComponent } from "react";
import { Treemap, ResponsiveContainer } from "recharts";

// const data = [
//   {
//     name: "RS<PERSON>",
//     children: [
//       { name: "<PERSON><PERSON>", size: 10 },
//       { name: "Axis", size: 10 },
//       { name: "AxisGridLine", size: 10 },
//       { name: "AxisLabel", size: 10 },
//       { name: "CartesianAxes", size: 10 },
//     ],
//   },
//   {
//     name: "UML",
//     children: [
//       { name: "AnchorControl", size: 10 },
//       { name: "ClickControl", size: 10 },
//       { name: "Control", size: 10 },
//       { name: "ControlList", size: 10 },
//       { name: "DragControl", size: 10 },
//       { name: "ExpandControl", size: 10 },
//       { name: "HoverControl", size: 10 },
//       { name: "IControl", size: 10 },
//       { name: "PanZoomControl", size: 10 },
//       { name: "SelectionControl", size: 10 },
//       { name: "TooltipControl", size: 10 },
//     ],
//   },
// ];

const COLORS = [
  "#8889DD",
  "#9597E4",
  "#8DC77B",
  "#A5D297",
  "#E2CF45",
  "#F8C12D",
];

interface CustomizedContentProps {
  seatData: { name: string; seats: number; color?: string; img?: string }[];
  root?: any;
  depth: number;
  x: number;
  y: number;
  width: number;
  height: number;
  index: number;
  payload?: any;
  colors?: string[];
  rank?: number;
  name: string;
}

class CustomizedContent extends PureComponent<CustomizedContentProps> {
  render() {
    const {
      seatData,
      root,
      depth,
      x,
      y,
      width,
      height,
      index,
      payload,
      colors,
      rank,
      name,
    } = this.props;

    return (
      <g>
        <rect
          x={x}
          y={y}
          width={width}
          height={height}
          style={{
            fill:
              seatData.find((item: any) => item.name === name)?.color ||
              "#ffffff00",
            stroke: "#fff",
            strokeWidth: 2 / (depth + 1e-10),
            strokeOpacity: 1 / (depth + 1e-10),
          }}
        />

        {depth === 1 && (
          <>
            <text
              x={x + width / 2 - width / 2 / 2}
              y={y + height / 2}
              fill="#fff"
              fontSize={16}
              fillOpacity={0.9}
            >
              {name} ({seatData.find((item: any) => item.name === name)?.seats}){" "}
            </text>
            <image
              href={
                seatData.find((item: any) => item.name === name)?.img ||
                "https://nepaltracks.com/logos/nepaltracks.png"
              }
              x={x + width / 2 - width / 2 / 2}
              y={y + height / 2 + 10}
              width={20}
              height={20}
            />{" "}
          </>
        )}
      </g>
    );
  }
}

const SeatDistribution = (props: { seatData: any }) => {
  //@ts-expect-error
  const data = props.seatData.map((item) => {
    return {
      name: item.name,
      //   children: Array(item.seats)
      //     .fill(true)
      //     .map(() => ({ size: 10 })),
      children: [{ size: item.seats }],
    };
  });

  return (
    <ResponsiveContainer width="100%" height="100%">
      <Treemap
        data={data}
        //@ts-expect-error
        content={<CustomizedContent seatData={props.seatData} />}
        dataKey="size"
        aspectRatio={4 / 3}
        stroke="#fff"
        // fill="#8884d8"
      />
    </ResponsiveContainer>
  );
};
export default SeatDistribution;
