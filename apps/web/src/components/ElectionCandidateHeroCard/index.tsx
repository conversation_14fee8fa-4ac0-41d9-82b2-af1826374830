import {
  Avatar,
  BackgroundImage,
  Badge,
  Box,
  Group,
  Overlay,
  Stack,
} from "@mantine/core";
import { IconBox, IconCheck } from "@tabler/icons-react";
import Link from "next/link";
import { HeroCard } from "../HeroCard";
import PartyBadge from "../PartyBadge";
import { ReactNode } from "react";
import { VoteCountBox } from "../VoteCountBox";
import { IElectionResult } from "@/interfaces/IElections";

interface IProps {
  count: number;
  candidateId?: number;
  name: string;
  isElected: boolean;
  remarks?: string;
  image?: string;
  partyName?: string;
  partyImage?: string;
  partyId?: number;
  percantage: number;
  previousElections?: IElectionResult[];
  renderTopLeftFloat?: () => React.ReactNode;
}

const ElectionCandidateHeroCard = (props: IProps) => {
  return (
    <Box>
      <HeroCard
        paperStyle={{ className: "h-[200px] md:h-[450px]" }}
        category={
          <Group gap={2}>
            <VoteCountBox count={props.count} />
            {props.isElected ? (
              <Badge
                size="md"
                color={props.isElected ? "teal" : "blue"}
                radius="xl"
                leftSection={
                  props.isElected ? (
                    <IconCheck size={12} strokeWidth={5} color={"white"} />
                  ) : null
                }
              >
                {props.remarks}
              </Badge>
            ) : null}
          </Group>
        }
        image={props.image}
        more={
          <PartyBadge
            partyId={props.partyId}
            partyImage={props.partyImage}
            partyName={props.partyName}
          />
        }
        title={
          <Link
            className="no-underline text-inherit"
            href={"/leaders/" + props.candidateId}
          >
            <Badge variant="filled" size="lg">
              {props.name}
            </Badge>
          </Link>
        }
        renderTopLeftFloat={props.renderTopLeftFloat}
      />
    </Box>
  );
};
export default ElectionCandidateHeroCard;
