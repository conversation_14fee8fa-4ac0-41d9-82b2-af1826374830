import { useTranslation } from "next-i18next";
import { truncateString } from "@/utils";
import {
  Avatar,
  Text,
  Group,
  Card,
  Tooltip,
  Rating,
  Anchor,
  CardProps,
  Paper,
  PaperProps,
  Highlight,
} from "@mantine/core";
import {
  IconStars,
  IconGenderMale,
  IconGenderFemale,
  IconHome,
} from "@tabler/icons-react";
import Link from "next/link";

export interface EntityInfoProps {
  disableLink?: boolean;
  linkComponent?: any;
  partyId?: string;
  id?: string;
  avatar?: string | React.ReactNode;
  name: string | React.ReactNode;
  title?: React.ReactNode;
  phone?: string;
  email?: string;
  gender?: "male" | "female";
  resources?: string;
  rate?: number;
  address?: string | React.ReactNode;
  renderInformation?: () => React.ReactNode;
  renderRightSection?: () => React.ReactNode;
  renderAfterContent?: () => React.ReactNode;
  cardProps?: PaperProps;
  paperProps?: CardProps;
  titleTextAlign?: string;
  noShadow?: boolean;
  highlight?: string;
  avatarProps?: any;
}

export function EntityInfo({
  avatar,
  name,
  title,
  titleTextAlign,
  noShadow,
  address,
  ...props
}: EntityInfoProps) {
  return (
    <Card
      className={`${noShadow ? "" : "shadow-md"}`}
      p={"md"}
      h={"100%"}
      miw={{ base: "100%", md: "300px" }}
      mih={{ base: "100%", md: "100%" }}
      mah={{ base: "100%", md: "100%" }}
      {...props.paperProps}
    >
      <Group justify="space-between" align="center" h={"100%"}>
        <Group align="center" wrap="nowrap" h={"100%"}>
          {avatar && typeof avatar === "string" && (
            <Avatar
              src={avatar}
              size={"lg"}
              radius="md"
              {...props.avatarProps}
            />
          )}
          {avatar && typeof avatar === "object" && avatar}
          <div>
            {title &&
              (props.disableLink ? (
                <Text
                  fz="xs"
                  tt="uppercase"
                  fw={700}
                  c="dimmed"
                  className="line-clamp-1"
                >
                  {/* @ts-ignore */}
                  <Highlight highlight={props.highlight || ""}>
                    {title}
                  </Highlight>
                </Text>
              ) : (
                <Link
                  href={
                    props.disableLink
                      ? "javascript:void(0)"
                      : `/${props.resources || "leaders"}/` +
                        (props.id || props.partyId)
                  }
                  className="no-underline text-inherit"
                >
                  <Text
                    fz="xs"
                    tt="uppercase"
                    fw={700}
                    c="dimmed"
                    className="line-clamp-1"
                  >
                    {/* @ts-ignore */}
                    <Highlight highlight={props.highlight || ""}>
                      {title}
                    </Highlight>
                  </Text>
                </Link>
              ))}

            <Group gap={2} align="center">
              {props.disableLink ? (
                <Text
                  fz="md"
                  fw={500}
                  //@ts-expect-error
                  ta={titleTextAlign ? titleTextAlign : "center"}
                >
                  {/* @ts-ignore */}
                  <Highlight highlight={props.highlight || ""}>
                    {name}
                  </Highlight>
                </Text>
              ) : (
                <Link
                  className="no-underline text-inherit"
                  href={
                    props.disableLink
                      ? "javascript:void(0)"
                      : `/${props.resources}/` + props.id
                  }
                >
                  <Text
                    fz="md"
                    fw={500}
                    //@ts-expect-error
                    ta={titleTextAlign ? titleTextAlign : "center"}
                  >
                    {/* @ts-ignore */}
                    <Highlight highlight={props.highlight || ""}>
                      {name}
                    </Highlight>
                  </Text>
                </Link>
              )}

              {props.gender && (
                <Tooltip label={props.gender} aria-label={props.gender}>
                  {props.gender === "male" ? (
                    <IconGenderMale
                      aria-label={props.gender}
                      stroke={2}
                      size="1rem"
                    />
                  ) : (
                    <IconGenderFemale stroke={2} size="1rem" />
                  )}
                </Tooltip>
              )}
            </Group>

            {props.rate !== undefined && (
              <Group wrap="nowrap" gap={10} mt={3}>
                <Link
                  href={
                    props.disableLink
                      ? "javascript:void(0)"
                      : `/${props.resources}/` + props.id + "/ratings"
                  }
                  className="no-underline text-inherit"
                >
                  <Rating size="xs" value={props.rate} readOnly />
                </Link>
              </Group>
            )}
            {address && (
              <Group wrap="nowrap" gap={10} mt={5}>
                <Text fz="xs" c="dimmed" className="max-w-xl">
                  {/* @ts-ignore */}
                  {address?.replace?.("NULL", "")}{" "}
                </Text>
              </Group>
            )}
            {props.renderInformation?.()}
          </div>
        </Group>

        {props.renderRightSection?.()}
      </Group>
      {props.renderAfterContent?.()}
    </Card>
  );
}
