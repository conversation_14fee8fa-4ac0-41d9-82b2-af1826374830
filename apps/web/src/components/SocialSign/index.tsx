import { But<PERSON>, <PERSON><PERSON> } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { FaTwitter, FaGoogle } from "react-icons/fa";

export function SocialSignin() {
  const host = process.env.NEXT_PUBLIC_API_HOST;
  const { t } = useTranslation();

  return (
    <Stack>
      <Button
        onClick={() =>
          (window.location.href = `${host}/api/v1/auth/social/twitter`)
        }
        // @ts-expect-error
        leftSection={<FaTwitter size={18} />}
        className="flex items-center justify-center gap-3 w-full px-4 py-2  text-white font-semibold rounded-md transition bg-[#272B30] hover:bg-[#050505]"
      >
        <span>
          {t("common:continue_with_x", {
            X: "X",
            defaultValue: "Continue with X",
          })}
        </span>
      </Button>

      <Button
        onClick={() =>
          (window.location.href = `${host}/api/v1/auth/social/google`)
        }
        leftSection={
          <img src="/logos/google.png" width={18} height={18} alt="google" />
        }
        className="flex items-center justify-center gap-3 w-full px-4 py-2 bg-white text-gray-700 hover:text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-200  transition"
      >
        {/* @ts-ignore */}
        <span>
          {t("common:continue_with_google", {
            defaultValue: "Continue with Google",
          })}
        </span>
      </Button>
    </Stack>
  );
}
