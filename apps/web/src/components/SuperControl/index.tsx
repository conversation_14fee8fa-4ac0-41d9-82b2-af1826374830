import { useProfile } from "@/store";
import { ActionIcon, Group } from "@mantine/core";
import {
  IconBuilding,
  IconEdit,
  IconPlus,
  IconTrash,
} from "@tabler/icons-react";
import RedditSubmitButton from "../RedditPostLink";

const SuperControl = ({
  entityType,
  entityId,
  newData,
  noEdit,
  enableRedditPost,
  onlyRedditIcon,
}: {
  onlyRedditIcon?: boolean;
  noEdit?: boolean;
  enableRedditPost?: boolean;
  entityType: string;
  entityId: string;
  newData?: any;
}) => {
  const host = process.env.NEXT_PUBLIC_API_HOST;
  const profile = useProfile();
  const objectToUrlParams = (obj: any) => {
    return Object.keys(obj || {})
      .map((key) => `${key}=${obj[key]}`)
      .join("&");
  };
  return (
    <Group>
      {profile?.profile?.role === "Admin" && (
        <>
          {!noEdit && (
            <ActionIcon
              size={"xs"}
              variant="transparent"
              onClick={() => {
                window.open(
                  `${host}/api/v1/xyz-mukhiya/resources/${entityType}/records/${entityId}/edit`,
                  "_blank"
                );
              }}
            >
              <IconEdit />
            </ActionIcon>
          )}

          <ActionIcon
            size={"xs"}
            variant="transparent"
            onClick={() => {
              window.open(
                `${host}/api/v1/xyz-mukhiya/resources/${entityType}/actions/new?${objectToUrlParams(
                  newData
                )}`,
                "_blank"
              );
            }}
          >
            <IconPlus />
          </ActionIcon>
          <ActionIcon
            size={"xs"}
            variant="transparent"
            onClick={() => {
              window.open(
                `${host}/api/v1/xyz-mukhiya/resources/${entityType}/records/${entityId}/delete`,
                "_blank"
              );
            }}
          >
            <IconTrash />
          </ActionIcon>

          <ActionIcon
            size={"xs"}
            variant="transparent"
            onClick={() => {
              window.open(
                `${host}/api/v1/xyz-mukhiya/resources/${entityType}/actions/new?${objectToUrlParams(
                  newData
                )}?leaders=${newData.leadersId}`,
                "_blank"
              );
            }}
          >
            <IconBuilding />
          </ActionIcon>
        </>
      )}

      {enableRedditPost && (
        <RedditSubmitButton
          onlyRedditIcon={onlyRedditIcon}
          contentType={newData?.contentType}
          title={`[${newData.contentType}-${(
            newData?.resourceType || "LEADER"
          ).toUpperCase()}-${entityId || "0"}] Write your title here `}
          url={newData?.url}
        />
      )}
    </Group>
  );
};
export default SuperControl;
