import {
  ResponsiveContainer,
  Composed<PERSON>hart,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  CartesianGrid,
  Bar,
  Line,
  Cell,
  Label,
} from "recharts";
import { Card, Title } from "@mantine/core";

export function LeaderElectionHistoryChart({
  elections,
  entityName,
}: {
  entityName?: string;
  elections: any[];
}) {
  const chartData = [...elections]
    .sort(
      (a, b) =>
        new Date(a.elections.year).getTime() -
        new Date(b.elections.year).getTime()
    )
    .map((e) => ({
      year: new Date(e.elections.year).getFullYear(),
      votes: e.voteCount,
      isElected: e.isElected,
      party: e.parties?.name,
      color: e.parties?.partyColorCode || "#8884d8",
    }));

  return (
    <Card mt="xl" withBorder>
      <Title order={4}>{entityName} Election History </Title>
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart
          data={chartData}
          margin={{ top: 10, right: 30, left: 0, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year">
            <Label value="Year" offset={-10} position="insideBottom" />
          </XAxis>
          <YAxis
            label={{ value: "Votes", angle: -90, position: "insideLeft" }}
          />
          <Tooltip
            formatter={(value: number, name: string) =>
              name === "votes" ? `${value.toLocaleString()} votes` : value
            }
            labelFormatter={(label) => `Year: ${label}`}
          />
          <Legend />
          <Bar dataKey="votes" name="Votes" fill="#94a3b8">
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.isElected ? entry.color : "#d1d5db"} // gray if lost
              />
            ))}
          </Bar>
          <Line
            type="monotone"
            dataKey="votes"
            name="Vote Trend"
            stroke="#1e40af"
            strokeWidth={2}
            dot={({ cx, cy, payload }) => (
              <circle
                cx={cx}
                cy={cy}
                r={6}
                fill={payload.isElected ? "#16a34a" : "#ef4444"}
                stroke="#fff"
                strokeWidth={2}
              />
            )}
            activeDot={{ r: 8 }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </Card>
  );
}
