import { EntityInfo } from "../EntityInfo";
import { cleanUrl, getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import { IMedia } from "@/interfaces/IMedia";

type Params = {
  age: number;
};
interface IProps {
  data: IMedia;
  renderDescription?: (params: Params) => React.ReactNode | JSX.Element;
  renderRightSection?: (params: Params) => React.ReactNode | JSX.Element;
  renderTitle?: (params: Params) => React.ReactNode | JSX.Element;
  renderInformation?: (params: Params) => React.ReactNode | JSX.Element;
}
const MediaProfile = (props: IProps) => {
  const age = props.data.years || getAgeFromDate(props.data.startDate);
  const params = { age };
  return (
    <EntityInfo
      paperProps={{ h: "130px", miw: "300px" }}
      resources={"medias"}
      id={props.data.id + ""}
      name={props.data.localName || props.data.name}
      avatar={getImageUrlWithFallback(props.data.logo, props.data.id + "")}
      title={props.renderDescription?.(params)}
      address={
        props.renderInformation?.(params) || cleanUrl(props.data.website || "")
      }
      rate={props.data.rating?.average}
      renderRightSection={() => props.renderRightSection?.(params)}
    />
  );
};

export default MediaProfile;
