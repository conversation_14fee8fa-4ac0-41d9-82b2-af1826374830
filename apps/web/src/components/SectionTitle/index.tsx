import { Title } from "@mantine/core";

interface SectionTitleProps {
  children: React.ReactNode;
  size?: 1 | 2 | 3 | 4 | 5 | 6; // mantine Title order 1-6
  mt?: number | string;
  mb?: number;
}

export function SectionTitle({
  children,
  size = 4,
  mt = "md", // can be a string or number
  mb = 6,
}: SectionTitleProps) {
  return (
    <Title order={size} mt={mt} mb={mb}>
      {children}
    </Title>
  );
}
