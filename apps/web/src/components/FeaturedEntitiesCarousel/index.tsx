import { useTranslation } from "next-i18next";
import { Carousel } from "@mantine/carousel";
import { useMediaQuery } from "@mantine/hooks";
import { useMantineTheme } from "@mantine/core";

export function FeaturedEntitiesCarousel<T>(props: {
  renderCarouselItem: (item: T) => React.ReactNode;
  items: T[];
}) {
  const theme = useMantineTheme();
  const mobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);

  return (
    <Carousel
      height={mobile ? 200 : 450}
      slideSize={{ base: "100%", sm: "50%", md: "25%" }}
      slideGap={{ base: 0, sm: "md" }}
      loop
      align={"start"}
      slidesToScroll={mobile ? 1 : 2}
    >
      {props.items?.map((item, index) => (
        <Carousel.Slide key={index}>
          {props.renderCarouselItem(item)}
        </Carousel.Slide>
      ))}
    </Carousel>
  );
}
