import Link from "next/link";

const getMappedLinks = (name: string, data: any = {}) => {
  const { id } = data || {};

  const resource = name.toLowerCase();
  if (resource === "governments" || resource === "government")
    return `/governments/${id}/home`;
  if (resource === "leaders" || resource === "leader")
    return `/leaders/${id}/home`;
  if (resource === "parties" || resource === "party")
    return `/parties/${id}/home`;
  if (resource === "departments" || resource === "department")
    return `/departments/${id}`;
  if (resource === "wards" || resource === "ward") return `/wards/${id}/home`;
  if (resource === "municipals" || resource === "municipal")
    return `/municipals/${id}/home`;
  if (resource === "parliaments" || resource === "parliament")
    return `/parliaments/${id}/home`;
  if (resource === "elections" || resource === "election")
    return `/elections/${id}/home`;

  if (resource === "contents" || resource === "content")
    return `/contents/${data.resourceType}/${data.contentType}/${data.id}/home`;
};
const DynamicLinkBuilderForResource = ({
  resourceType,
  data,
  children,
}: {
  children: any;
  resourceType: any;
  data: any;
}) => {
  const href = getMappedLinks(resourceType, data);

  return (
    <Link className="no-underline text-inherit" href={href || "#"}>
      {children}
    </Link>
  );
};
export default DynamicLinkBuilderForResource;
