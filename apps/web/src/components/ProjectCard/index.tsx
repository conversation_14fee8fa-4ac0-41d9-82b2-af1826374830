import { formatN<PERSON>ber, replaceNumberWithAnka } from "@/utils";
import {
  Card,
  Image,
  Badge,
  Text,
  Group,
  Progress,
  Tooltip,
  Button,
  Rating,
} from "@mantine/core";
import { IconExternalLink } from "@tabler/icons-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

interface ProjectCardProps {
  project: {
    id: number;
    name: string;
    localName?: string;
    description?: string;
    type: "Mega" | "Infrastructure" | "Small";
    status: "Ongoing" | "Completed" | "Planned" | "Cancelled";
    sector?: string;
    address?: string;
    startDate: string;
    endDate?: string;
    plannedStartDate?: string;
    plannedEndDate?: string;
    lastInspectedAt?: string;
    budget?: number;
    currency?: string;
    beneficiaries?: number;
    contractor?: string;
    fundingSource?: string;
    expectedOutcome?: string;
    challenges?: string;
    milestones?: any;
    image?: string;
    mediaGallery?: any;
    progress?: number;
    link?: string;
    rating?: {
      average: number;
      count: number;
    };
  };
}

export default function ProjectCard({ project }: ProjectCardProps) {
  const { t } = useTranslation();

  return (
    <Card
      withBorder
      radius="md"
      padding="lg"
      className="w-full mx-auto shadow-xs transition-all"
    >
      <Group align="start" gap="lg" className="flex-col md:flex-row">
        {project.image && (
          <Image
            src={project.image}
            alt={project.name}
            width={140}
            height={140}
            radius="md"
            className="object-cover mr-4"
          />
        )}

        <div className="flex-1">
          <Group justify="apart" className="mb-2">
            <Text size="lg" fw={600}>
              {project.name}
            </Text>
            <Badge
              color={
                project.status === "Completed"
                  ? "green"
                  : project.status === "Ongoing"
                  ? "blue"
                  : "gray"
              }
            >
              {project.status}
            </Badge>
          </Group>
          {project.localName && (
            <Text size="sm" color="dimmed" className="italic">
              ({project.localName})
            </Text>
          )}{" "}
          {project.rating?.average && (
            <Rating value={project.rating?.average} readOnly size="xs" />
          )}
          <Text size="sm" className="mt-1 mb-2">
            {project.description || "No description available."}
          </Text>
          <Group gap="xs" className="mb-2" mt={5}>
            <Badge color="teal" variant="light">
              {project.type}
            </Badge>
            {project.sector && (
              <Badge color="grape" variant="light">
                {project.sector}
              </Badge>
            )}
            {project.address && (
              <Badge color="indigo" variant="light">
                {project.address}
              </Badge>
            )}
          </Group>
          {project.budget && (
            <Text size="xs" color="dimmed">
              {t("common:budget", "Budget")}: {formatNumber(project.budget)}{" "}
              {project.currency} {project.currency === "NPR" ? "रु" : "$"}
            </Text>
          )}
          <Text size="xs" color="dimmed">
            {t("common:started", "Started")}:{" "}
            {format(new Date(project.startDate), "yyyy-MM-dd")}
            {project.endDate &&
              ` | {t("common:ended", "Ended")}: ${format(
                new Date(project.endDate),
                "yyyy-MM-dd"
              )}`}
          </Text>
          {project.contractor && (
            <Text size="xs" color="dimmed">
              {t("common:contractor", "Contractor")}: {project.contractor}
            </Text>
          )}
          {project.fundingSource && (
            <Text size="xs" color="dimmed">
              {t("common:funding_source", "Funding Source")}:{" "}
              {project.fundingSource}
            </Text>
          )}
          {typeof project.progress === "number" && (
            <div className="mt-3">
              <Tooltip
                label={`${t("common:progress", "Progress")}: ${
                  project.progress
                }%`}
              >
                <Progress
                  value={project.progress}
                  color="blue"
                  size="sm"
                  radius="xl"
                />
              </Tooltip>
            </div>
          )}
          {project.link && (
            <Button
              component="a"
              href={project.link}
              target="_blank"
              rel="noopener noreferrer"
              size="xs"
              rightSection={<IconExternalLink size={14} />}
              className="mt-4"
              variant="light"
            >
              {t("common:learn_more", "Learn more")}
            </Button>
          )}
        </div>
      </Group>
    </Card>
  );
}
