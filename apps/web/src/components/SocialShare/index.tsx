import { Group, Tooltip } from "@mantine/core";
import { useMantineColorScheme } from "@mantine/core";

import {
  EmailIcon,
  EmailShareButton,
  FacebookIcon,
  FacebookShareButton,
  TelegramIcon,
  TelegramShareButton,
  PinterestIcon,
  PinterestShareButton,
  RedditIcon,
  RedditShareButton,
  TwitterIcon,
  TwitterShareButton,
  ViberIcon,
  ViberShareButton,
  LineIcon,
  LineShareButton,
  PocketIcon,
  PocketShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  XIcon,
} from "react-share";

const medias: Array<{
  name: string;
  Button: any;
  Icon: any;
}> = [
  { name: "Facebook", Button: FacebookShareButton, Icon: FacebookIcon },
  { name: "Twitter", Button: TwitterShareButton, Icon: XIcon },
  { name: "Reddit", Button: RedditShareButton, Icon: RedditIcon },
  { name: "Viber", Button: ViberShareButton, Icon: ViberIcon },
  { name: "WhatsApp", Button: WhatsappShareButton, Icon: WhatsappIcon },
];

const SocialShare = ({ url }: { url: string }) => {
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <Group align="center" justify="center" gap="xs" className="flex-wrap">
      {medias.map(({ name, Button, Icon }, index) => (
        <Tooltip label={`Share on ${name}`} key={index}>
          <div
            className={`
              transition-transform duration-200
              hover:scale-110
              rounded-full
              p-1
              hover:bg-gray-100
              dark:hover:bg-white/10
              dark:hover:shadow-lg
              hover:shadow
            `}
          >
            {/* @ts-ignore */}
            <Button url={url}>
              {/* @ts-ignore */}
              <Icon size={24} round />
            </Button>
          </div>
        </Tooltip>
      ))}
    </Group>
  );
};

export default SocialShare;
