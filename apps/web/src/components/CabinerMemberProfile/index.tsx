import { Badge, Group, Stack, Text } from "@mantine/core";
import LeaderHeroProfile from "../LeaderHeroProfile";
import TenureBadge from "../TenureBadge";
import { startCase, toLower } from "lodash";
import { ICabinetMember } from "@/interfaces/ICabinetMember";
import Link from "next/link";
import { formatDate, getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import SuperControl from "../SuperControl";
import { useTranslation } from "react-i18next";
import { UserInfoAction } from "../UserInfoAction"; // import your new variant
import DistanceFromNow from "../DistanceFromNow";

const CabinetMemberProfile = ({
  data: { role, leaders: head, leaderName: ln, partyName, ...position },
  variant = "hero", // "hero" | "compact"
  disableRanking,
}: {
  data: ICabinetMember;
  variant?: "hero" | "compact";
  disableRanking?: boolean;
}) => {
  console.log(head);
  const { t } = useTranslation();
  const rank = position.rank ?? position.department?.rank;
  const leaderName = head?.name || head?.localName || ln;

  const partyLabel =
    head?.party_leaders?.map((item) => item.party.name).join(", ") || partyName;

  const description = partyLabel ? `${leaderName} (${partyLabel})` : leaderName;
  const age = head && head.birthDate ? getAgeFromDate(head.birthDate) : 0;

  const renderPastCabinet = () =>
    head?.cabinet_members?.length ? (
      <Stack gap={1}>
        <br />
        <Text size="xs" fw={700} ta={"start"}>
          {t("common:also_previously_served_in", {
            defaultValue: "Also Previously Served In",
          })}
        </Text>
        <ul className="mt-2 max-h-12 overflow-y-auto">
          {head.cabinet_members.map((item) => (
            <li key={item.id}>
              <Link
                href={`/departments/${item.department?.id}`}
                className="no-underline text-inherit "
              >
                <Text fw={550} c="dimmed" size="xs" ta={"start"}>
                  {item.department?.name} - {formatDate(item.startedAt)}
                </Text>
              </Link>
            </li>
          ))}
        </ul>
      </Stack>
    ) : null;

  const renderPresentCabinet = () =>
    head?.additionalCabinets?.length ? (
      <Stack gap={1}>
        <br />
        <Text size="xs" fw={700} ta={"start"}>
          {t("common:also_served_as", {
            defaultValue: "Also served as",
          })}
        </Text>
        <ul className="mt-2 max-h-12 overflow-y-auto">
          {head.additionalCabinets.map((item) => (
            <li key={item.id}>
              <Link
                href={`/departments/${item.department?.id}`}
                className="no-underline text-inherit "
              >
                <Text fw={550} c="dimmed" size="xs" ta={"start"}>
                  {item.department?.name} - {formatDate(item.startedAt)}
                </Text>
              </Link>
            </li>
          ))}
        </ul>
      </Stack>
    ) : null;

  const sharedDescription = (
    <Stack gap={1} mt="xs" align={"center"} w={"100%"} justify={"center"}>
      <SuperControl
        entityType="cabinet_members"
        entityId={String(position.id)}
        newData={{
          leaders: head?.id,
          departmentId: position.department?.id,
          governmentId: position.governmentId,
        }}
      />
      {position.department?.name && (
        <Text fw={550} c="dimmed" size="sm" ta={"center"}>
          {position.department.name}
        </Text>
      )}
      <TenureBadge startedAt={position.startedAt} endAt={position.endAt} />
      <DistanceFromNow startedAt={position.startedAt} />
      {renderPastCabinet()}
      {renderPresentCabinet()}
    </Stack>
  );

  if (variant === "compact") {
    return (
      <UserInfoAction
        data={head}
        img={
          position.img ||
          getImageUrlWithFallback(head?.img, head?.ecCandidateID || "")
        }
        name={
          <Group justify="center" align="center" gap={5}>
            {leaderName}
            {age && (
              <Badge color="gray" size="xs">
                {age}
              </Badge>
            )}
          </Group>
        }
        resourceId={head?.id}
        resourceType="leaders"
        renderBeforeAvatar={() =>
          !disableRanking && role ? (
            <Badge>
              {t(`common:${toLower(role)}`, startCase(String(role)))}
            </Badge>
          ) : null
        }
        renderDescription={() => sharedDescription}
      />
    );
  }

  // Default to full profile
  return (
    <LeaderHeroProfile
      additionalInformationEnabled
      data={head}
      renderTopRightSection={
        !disableRanking && rank
          ? () => (
              <Badge color="gray" variant="outline" size="md">
                #{rank}
              </Badge>
            )
          : undefined
      }
      renderBeforeAvatar={
        role
          ? () => (
              <Badge>
                {t(`common:${toLower(role)}`, startCase(String(role)))}
              </Badge>
            )
          : undefined
      }
      renderDescription={() => sharedDescription}
    />
  );
};

export default CabinetMemberProfile;
