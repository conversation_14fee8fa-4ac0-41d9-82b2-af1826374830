import { getImageUrlWithFallback, truncateString } from "@/utils";
import {
  Card,
  Title,
  Avatar,
  Stack,
  Text,
  SimpleGrid,
  Group,
  Badge,
  Accordion,
} from "@mantine/core";
import PartyLink from "../Links/PartyLink";
import { IElectionOverview } from "@/interfaces/IElectionOverview";
import { CabinetDistributionByParty } from "@/containers/Sections/CabinetDistributionByParty";

type Props = {
  data: IElectionOverview["topPartiesPerElection"];
};

export function TopPartiesPerElection({ data }: Props) {
  const grouped = data.reduce<Record<number, typeof data>>((acc, party) => {
    if (!acc[party.electionYear]) acc[party.electionYear] = [];
    acc[party.electionYear].push(party);
    return acc;
  }, {});
  const items = Object.entries(grouped).sort(
    (a, b) => Number(b[0]) - Number(a[0])
  );

  return (
    <Accordion
      multiple
      defaultValue={items.map(([year]) => year + "")}
      radius="md"
      variant="separated"
      styles={{ item: { marginBottom: 12 } }}
    >
      {items.map(([year, parties]) => {
        const totalVotes = parties.reduce(
          (acc, cur) => acc + cur.totalVotes,
          0
        );

        return (
          <Accordion.Item value={year} key={year} variant="separated">
            <Accordion.Control>
              <Group justify="space-between" p="sm" wrap="nowrap">
                <Stack gap={2}>
                  <Title order={4}>
                    Top performing parties in {parties[0].electionName}, {year}
                  </Title>
                  <Text size="sm" c="dimmed" className="line-clamp-1">
                    {truncateString(
                      parties.map((party) => party.name).join(", "),
                      100
                    )}
                  </Text>
                </Stack>
                {parties.length > 3 && (
                  <Avatar.Group>
                    {parties.slice(0, 3).map((party) => (
                      <Avatar
                        key={party.partyId}
                        src={party.logo}
                        radius="xl"
                      />
                    ))}
                    <Avatar radius="xl">+{parties.length - 3}</Avatar>
                  </Avatar.Group>
                )}
              </Group>
            </Accordion.Control>

            <Accordion.Panel>
              <Stack>
                <SimpleGrid
                  cols={{ base: 1, sm: 2, md: 3, lg: 4 }}
                  spacing="lg"
                  mt="md"
                >
                  {parties.map((party) => (
                    <Card key={party.partyId} withBorder radius="sm" p="md">
                      <Stack align="center" gap={6}>
                        <Avatar
                          src={party.logo}
                          alt={party.name}
                          size="lg"
                          radius="xl"
                        />
                        <PartyLink id={party.partyId}>
                          <Text fw={600} size="sm" className="line-clamp-1">
                            {party.localName || party.name}
                          </Text>
                        </PartyLink>
                        <Group gap="xs" wrap="wrap">
                          {party.debut && <Badge color="purple">Debut</Badge>}
                          <Badge color="gray">Seats: {party.totalSeats}</Badge>
                          <Badge color="blue">Votes: {party.totalVotes}</Badge>
                        </Group>
                      </Stack>
                    </Card>
                  ))}
                </SimpleGrid>
                <CabinetDistributionByParty
                  //@ts-expect-error
                  data={parties.map((item) => ({
                    count: item.totalSeats,
                    label: item.party.localName,
                    part: ((item.totalVotes / totalVotes) * 100).toFixed(2),
                    color: item.party.partyColorCode || "",
                    img: item.party.logo,
                  }))}
                />
              </Stack>
            </Accordion.Panel>
          </Accordion.Item>
        );
      })}
    </Accordion>
  );
}
