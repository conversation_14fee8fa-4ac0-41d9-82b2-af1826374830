// utils/aggregatePartyStats.ts

export type ElectionEntry = {
    voteCount: number;
    isElected: boolean;
    parties: {
        id: number;
        name: string;
        localName: string;
        electionSymbol: string;
        logo: string;
    };
    candidacyType: {
        name: string;
        localName: string;
    };
};

export type PartyStats = {
    partyId: number;
    partyName: string;
    localName: string;
    logo: string;
    electionSymbol: string;
    totalVotes: number;
    electedCount: number;
    positions: Record<string, number>; // { "WARD_MEMBER": 2, "WARD_CHAIRPERSON": 1 }
};

export const aggregatePartyStats = (data: ElectionEntry[]): PartyStats[] => {
    const statsMap = new Map<number, PartyStats>();

    for (const entry of data) {
        const p = entry.parties;

        if (!statsMap.has(p.id)) {
            statsMap.set(p.id, {
                partyId: p.id,
                partyName: p.name,
                localName: p.localName,
                logo: p.logo,
                electionSymbol: p.electionSymbol,
                totalVotes: 0,
                electedCount: 0,
                positions: {},
            });
        }

        const current = statsMap.get(p.id)!;
        current.totalVotes += entry.voteCount;
        if (entry.isElected) current.electedCount++;

        const position = entry.candidacyType?.name || "Representatives";
        current.positions[position] = (current.positions[position] || 0) + 1;
    }

    return Array.from(statsMap.values());
};
