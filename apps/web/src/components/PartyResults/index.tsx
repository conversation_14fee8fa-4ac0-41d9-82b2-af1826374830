// components/PartyResults.tsx

import { Card, Text, Title, Group, Image, Avatar } from "@mantine/core";
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
} from "recharts";
import { aggregatePartyStats, ElectionEntry } from "./aggregatePartyStats";
import { startCase } from "lodash";
import { formatNumber } from "@/utils";

type Props = {
  data: ElectionEntry[];
};

const COLORS = ["#228be6", "#fab005", "#e03131", "#12b886", "#845ef7"];

export const PartyResults = ({ data }: Props) => {
  const stats = aggregatePartyStats(data);

  const pieData = stats.map((p) => ({
    name: p.localName,
    value: p.totalVotes,
  }));

  const barData = stats.map((p) => ({
    name: p.localName,
    totalVotes: p.totalVotes,
    elected: p.electedCount,
  }));

  return (
    <Card withBorder shadow="md" radius="md" p="md">
      <Title order={3}>पार्टी अनुसार नतिजा विश्लेषण (Party-wise Results)</Title>

      <Group justify="center" my="md">
        <PieChart width={300} height={300}>
          <Pie
            data={pieData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={100}
          >
            {pieData.map((_, idx) => (
              <Cell key={idx} fill={COLORS[idx % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>

        <BarChart width={500} height={300} data={barData}>
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Bar dataKey="totalVotes" fill="#4dabf7" />
          <Bar dataKey="elected" fill="#82ca9d" />
        </BarChart>
      </Group>

      {stats.map((party) => (
        <Card key={party.partyId} withBorder mt="md" shadow="sm" radius="md">
          <Group justify="space-between">
            <Group>
              {party.logo && <Avatar src={party.logo} alt={party.partyName} />}
              <div>
                <Text w={600}>{party.localName}</Text>
                <Text size="xs" color="dimmed">
                  {party.partyName}
                </Text>
              </div>
            </Group>
            <div>
              <Text>Total Votes: {formatNumber(party.totalVotes)}</Text>
              <Text>Elected Leaders: {formatNumber(party.electedCount)}</Text>
              {Object.entries(party.positions).map(([pos, count]) => (
                <Text size="xs" key={pos}>
                  {startCase(pos.replace("_", " "))}: {count}
                </Text>
              ))}
            </div>
          </Group>
        </Card>
      ))}
    </Card>
  );
};
