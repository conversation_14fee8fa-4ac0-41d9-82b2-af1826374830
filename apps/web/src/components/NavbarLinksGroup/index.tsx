import { useMemo, useState } from "react";
import { IconChevronRight } from "@tabler/icons-react";
import {
  Box,
  Collapse,
  Group,
  Text,
  ThemeIcon,
  Tooltip,
  UnstyledButton,
} from "@mantine/core";
import classes from "./style.module.css";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { useRouter } from "next/router";

interface LinksGroupProps {
  icon: React.FC<any>;
  label: string;
  link: string;
  initiallyOpened?: boolean;
  links?: { label: string; link: string }[];
  isTopPage?: boolean;
  isHamOpened?: boolean;
  pathname?: string;
}

export function LinksGroup({
  icon: Icon,
  label,
  link,
  initiallyOpened,
  links,
  isTopPage,
  isHamOpened,
  pathname,
}: LinksGroupProps) {
  const { t } = useTranslation();
  const hasLinks = Array.isArray(links);
  const [opened, setOpened] = useState(initiallyOpened || false);
  const items = (hasLinks ? links : []).map((link) => (
    <Text<"a">
      component={"a"}
      className={classes.link}
      href={link.link}
      key={link.label}
      onClick={(event) => event.preventDefault()}
    >
      {link.label}
    </Text>
  ));

  const isActive = useMemo(() => {
    if (pathname !== "/" && link === "/") return false;
    return pathname?.includes(link);
  }, [pathname]);

  return (
    <Link href={link || "/"} className={" no-underline text-inherit "}>
      <UnstyledButton
        onClick={() => setOpened((o) => !o)}
        className={classes.control}
      >
        <Group justify="space-between" gap={0}>
          <Box style={{ display: "flex", alignItems: "center" }}>
            <Tooltip withArrow label={t(label)} position="right">
              <ThemeIcon variant={isActive ? "light" : "transparent"} size={30}>
                <Icon size={18} />
              </ThemeIcon>
            </Tooltip>
            {isTopPage && (
              <Box
                ml="md"
                className={classes.mainLink}
                hidden={
                  !((isTopPage && !isHamOpened) || (!isTopPage && isHamOpened))
                }
              >
                {t(label)}
              </Box>
            )}
          </Box>
          {hasLinks && (
            <IconChevronRight
              className={classes.chevron}
              stroke={1.5}
              size={16}
              style={{ transform: opened ? "rotate(-90deg)" : "none" }}
            />
          )}
        </Group>
      </UnstyledButton>
      {hasLinks ? <Collapse in={opened}>{items}</Collapse> : null}
    </Link>
  );
}
