import { formatDate } from "@/utils";
import { Badge, Group, Text } from "@mantine/core";
import { formatDistance } from "date-fns";
import { useTranslation } from "react-i18next";

interface TenureBadgeProps {
  startedAt?: string | Date | null;
  withTooltip?: boolean;
  addDateAtEnd?: boolean;
}

export default function DistanceFromNow({
  startedAt,
  addDateAtEnd,
}: TenureBadgeProps) {
  const { t } = useTranslation();
  if (!startedAt) {
    return null;
  }

  const start = new Date(startedAt); // Assuming startedAt is a valid date string

  const content = (
    <Group gap={6} wrap="nowrap" align="center">
      <Text fw={700} size="xs" c="dimmed">
        {startedAt ? formatDistance(start, new Date()) + " ago" : ""}
        {addDateAtEnd ? ", " + formatDate(start) : null}
      </Text>
    </Group>
  );

  return (
    <Badge variant="transparent" radius="sm" px="xs" py={4} fw={500} size="sm">
      {content}
    </Badge>
  );
}
