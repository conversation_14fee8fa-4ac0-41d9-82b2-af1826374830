import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsTab } from "@mantine/core";
import { Card } from "@mantine/core";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";
import { truncateString } from "@/utils";

const COLORS = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff7f50",
  "#a4de6c",
  "#d0ed57",
];

const cabinetByPartyData = [
  { party: "Nepali Congress", ministers: 10 },
  { party: "CPN-UML", ministers: 12 },
  { party: "Maoist Centre", ministers: 6 },
];

const departmentChurnData = [
  { department: "Finance", changes: 8 },
  { department: "Health", changes: 5 },
  { department: "Education", changes: 3 },
];

const leaderRiseData = [
  { year: "2010", roles: 1 },
  { year: "2012", roles: 2 },
  { year: "2014", roles: 3 },
  { year: "2016", roles: 5 },
  { year: "2020", roles: 6 },
];

export default function PoliticalDashboard() {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Nepal Government Dashboard</h1>
      <Tabs defaultValue="cabinetByParty">
        <TabsList>
          <TabsTab value="cabinetByParty">Cabinet By Party</TabsTab>
          <TabsTab value="departmentChurn">Department Turnover</TabsTab>
          <TabsTab value="leaderRise">Leader Rise</TabsTab>
          <TabsTab value="partyPie">Party Share Pie</TabsTab>
        </TabsList>

        <TabsPanel value="cabinetByParty">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">
              Cabinet Composition by Party
            </h2>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={cabinetByPartyData}>
                <XAxis dataKey="party" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="ministers">
                  {cabinetByPartyData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="departmentChurn">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">Department Turnover</h2>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={departmentChurnData} layout="vertical">
                <XAxis type="number" />
                <YAxis dataKey="department" type="category" />
                <Tooltip />
                <Bar dataKey="changes" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="leaderRise">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">
              Leader Rise Over Time
            </h2>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={leaderRiseData}>
                <XAxis dataKey="year" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="roles" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="partyPie">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">
              Cabinet Share by Party (Pie)
            </h2>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={cabinetByPartyData}
                  dataKey="ministers"
                  nameKey="party"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {cabinetByPartyData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>
      </Tabs>
    </div>
  );
}

export type Root = {
  id: number;
  name: string;
  description: string;
  img: any;
  startedAt: string;
  endAt: string;
  createdAt: string;
  updatedAt: string;
  headId: number;
  government_type: string;
  municipalId: any;
  stateId: any;
  parliamentId: any;
  localName: any;
  head: {
    id: number;
    name: string;
    localName: string;
    description: string;
    img: string;
    address: string;
    contact: string;
    startedAt: any;
    endAt: any;
    createdAt: string;
    updatedAt: string;
    gender: string;
    birthDate: string;
    deathDate: any;
    emailAddress: any;
    metadata: string;
    qualification: string;
    experience: string;
    otherDetails: string;
    nameOfInst: string;
    ecCandidateID: string;
    images: any;
    projectsId: any;
    fbPage: any;
    twitterPage: any;
    instagramPage: any;
    youtubePage: any;
    website: any;
  };
  elections: Array<any>;
  cabinet_members: Array<{
    id: number;
    governmentId: number;
    partyId?: number;
    leaderId: number;
    departmentId: number;
    role: string;
    rank: number;
    startedAt: string;
    endAt: string;
    isResigned: boolean;
    isActing: boolean;
    portfolioTitle: string;
    remarks: any;
    appointedBy: any;
    appointmentMethod: any;
    officialLink?: string;
    createdAt: string;
    updatedAt: string;
    leaderName?: string;
    partyName?: string;
    leaders: {
      id: number;
      name: string;
      localName: string;
      description?: string;
      img?: string;
      address?: string;
      contact?: string;
      startedAt: any;
      endAt: any;
      createdAt: string;
      updatedAt: string;
      gender: string;
      birthDate?: string;
      deathDate: any;
      emailAddress: any;
      metadata: any;
      qualification?: string;
      experience?: string;
      otherDetails?: string;
      nameOfInst?: string;
      ecCandidateID: string;
      images: any;
      projectsId: any;
      fbPage: any;
      twitterPage: any;
      instagramPage: any;
      youtubePage: any;
      website: any;
      leaders_images: Array<any>;
    };
    department: {
      id: number;
      createdAt: string;
      updatedAt: string;
      name: string;
      code: string;
      description: string;
      localName?: string;
      logo: any;
      coverImage: any;
      website?: string;
      fbPage: any;
      twitterPage: any;
      youtubePage: any;
      metadata: any;
      rank?: number;
    };
  }>;
  governmentOverview: {
    government: {
      id: number;
      name: string;
      description: string;
      img: any;
      startedAt: string;
      endAt: string;
      createdAt: string;
      updatedAt: string;
      headId: number;
      government_type: string;
      municipalId: any;
      stateId: any;
      parliamentId: any;
      localName: any;
    };
    stats: {};
    coaltion: Array<{
      party: {
        id: number;
        name: string;
        description?: string;
        startDate?: string;
        code?: string;
        motto?: string;
        endDate?: string;
        logo?: string;
        coverImage?: string;
        localName: string;
        partyColorCode?: string;
        electionSymbol?: string;
        createdAt?: string;
        updatedAt?: string;
        fbPage: any;
        twitterPage: any;
        instagramPage: any;
        youtubePage: any;
        website: any;
      };
      members: Array<{
        leader: {
          id: number;
          name: string;
          localName: string;
          description?: string;
          img?: string;
          address?: string;
          contact?: string;
          startedAt: any;
          endAt: any;
          createdAt: string;
          updatedAt: string;
          gender: string;
          birthDate?: string;
          deathDate: any;
          emailAddress: any;
          metadata: any;
          qualification?: string;
          experience?: string;
          otherDetails?: string;
          nameOfInst?: string;
          ecCandidateID: string;
          images: any;
          projectsId: any;
          fbPage: any;
          twitterPage: any;
          instagramPage: any;
          youtubePage: any;
          website: any;
          leaders_images: Array<any>;
          party_leaders: Array<{
            id: number;
            leaderId: number;
            partyId: number;
            startDate: string;
            endDate: any;
            createdAt: string;
            updatedAt: string;
            position: string;
            role: string;
            code: string;
            party: {
              id: number;
              name: string;
              description?: string;
              startDate: string;
              code: string;
              motto?: string;
              endDate: any;
              logo?: string;
              coverImage?: string;
              localName: string;
              partyColorCode?: string;
              electionSymbol?: string;
              createdAt: string;
              updatedAt: string;
              fbPage: any;
              twitterPage: any;
              instagramPage: any;
              youtubePage: any;
              website: any;
            };
          }>;
        };
        cabinet_member: {
          id: number;
          governmentId: number;
          partyId?: number;
          leaderId: number;
          departmentId: number;
          role: string;
          rank: number;
          startedAt: string;
          endAt: string;
          isResigned: boolean;
          isActing: boolean;
          portfolioTitle: string;
          remarks: any;
          appointedBy: any;
          appointmentMethod: any;
          officialLink?: string;
          createdAt: string;
          updatedAt: string;
          leaderName: string;
          partyName: string;
          party?: {
            id: number;
            name: string;
            description?: string;
            startDate: string;
            code: string;
            motto?: string;
            endDate?: string;
            logo?: string;
            coverImage?: string;
            localName: string;
            partyColorCode?: string;
            electionSymbol?: string;
            createdAt: string;
            updatedAt: string;
            fbPage: any;
            twitterPage: any;
            instagramPage: any;
            youtubePage: any;
            website: any;
          };
        };
        department: {
          id: number;
          createdAt: string;
          updatedAt: string;
          name: string;
          code: string;
          description: string;
          localName?: string;
          logo: any;
          coverImage: any;
          website?: string;
          fbPage: any;
          twitterPage: any;
          youtubePage: any;
          metadata: any;
          rank?: number;
        };
      }>;
    }>;
  };
  summary: string;
  projects: Array<any>;
};

export const getMinistersByParty = (root: Root) => {
  const tally: Record<string, number> = {};
  for (const member of root.cabinet_members) {
    const party = member.partyName || "Independent";
    tally[party] = (tally[party] || 0) + 1;
  }
  return Object.entries(tally).map(([party, ministers]) => ({
    party,
    ministers,
  }));
};

export const getMinistersByRole = (root: Root) => {
  const tally: Record<string, number> = {};
  for (const member of root.cabinet_members) {
    tally[member.role] = (tally[member.role] || 0) + 1;
  }
  return Object.entries(tally).map(([role, count]) => ({ role, count }));
};

export const getGenderBreakdown = (root: Root) => {
  let male = 0,
    female = 0;
  for (const member of root.cabinet_members) {
    const gender = member.leaders.gender?.toLowerCase();
    if (gender === "male") male++;
    else if (gender === "female") female++;
  }
  return [
    { gender: "Male", count: male },
    { gender: "Female", count: female },
  ];
};

export const getAverageTermDuration = (root: Root) => {
  const durations: number[] = [];
  for (const member of root.cabinet_members) {
    const start = new Date(member.startedAt);
    const end = new Date(member.endAt);
    const diffDays = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    if (!isNaN(diffDays)) durations.push(diffDays);
  }
  const average = durations.reduce((a, b) => a + b, 0) / durations.length;
  return Math.round(average);
};

export const getOldestAndYoungestLeaders = (root: Root) => {
  const leaders = root.cabinet_members.map((m) => m.leaders);
  const withBirth = leaders.filter((l) => l.birthDate);
  const sorted = withBirth.sort(
    (a, b) =>
      new Date(a.birthDate!).getTime() - new Date(b.birthDate!).getTime()
  );
  return {
    oldest: sorted[0],
    youngest: sorted[sorted.length - 1],
  };
};

export const getMostFrequentLeaders = (root: Root) => {
  const countMap: Record<number, { id: number; name: string; count: number }> =
    {};
  for (const member of root.cabinet_members) {
    const id = member.leaders.id;
    if (!countMap[id]) {
      countMap[id] = { id, name: member.leaders.name, count: 0 };
    }
    countMap[id].count++;
  }
  const sorted = Object.values(countMap).sort((a, b) => b.count - a.count);
  return sorted[0];
};

export const getLeaderLifespanStats = (root: Root) => {
  const leaders = root.cabinet_members.map((m) => m.leaders);
  const withBirth = leaders.filter((l) => l.birthDate);
  const lifespans: number[] = [];
  const serviceYears: number[] = [];

  for (const leader of withBirth) {
    const birth = new Date(leader.birthDate!);
    const end = leader.deathDate ? new Date(leader.deathDate) : new Date();
    const lifespan =
      (end.getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    lifespans.push(lifespan);

    const services = root.cabinet_members.filter(
      (m) => m.leaders.id === leader.id
    );
    let totalService = 0;
    for (const service of services) {
      const s = new Date(service.startedAt);
      const e = new Date(service.endAt);
      totalService +=
        (e.getTime() - s.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
    }
    serviceYears.push(totalService);
  }

  const avg = (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length;

  return {
    averageLifespan: Math.round(avg(lifespans)),
    averageServiceYears: Math.round(avg(serviceYears)),
  };
};

export const getGovernmentTimeline = (root: Root) => {
  return {
    name: root.name,
    startedAt: root.startedAt,
    endAt: root.endAt,
  };
};

export const getDepartmentChangeCounts = (root: Root) => {
  const counts: Record<string, number> = {};
  for (const member of root.cabinet_members) {
    const dept = truncateString(member.department?.code, 20) || "Unknown";
    counts[dept] = (counts[dept] || 0) + 1;
  }
  return Object.entries(counts).map(([department, changes]) => ({
    department,
    changes,
  }));
};

export const getCabinetReshuffles = (root: Root) => {
  const reshuffles: { leader: string; count: number }[] = [];
  const leaderMap: Record<number, number> = {};

  for (const member of root.cabinet_members) {
    const id = member.leaderId;
    leaderMap[id] = (leaderMap[id] || 0) + 1;
  }

  for (const member of root.cabinet_members) {
    if (leaderMap[member.leaderId] > 1) {
      reshuffles.push({
        leader: member.leaders.name,
        count: leaderMap[member.leaderId],
      });
    }
  }

  const unique = reshuffles.reduce<
    Record<string, { leader: string; count: number }>
  >((acc, curr) => {
    acc[curr.leader] = { leader: curr.leader, count: curr.count };
    return acc;
  }, {});

  return Object.values(unique);
};

export const getDepartmentTenures = (root: Root) => {
  const durations: Record<string, number[]> = {};

  for (const member of root.cabinet_members) {
    const dept = member.department?.code || "Unknown";
    const start = new Date(member.startedAt);
    const end = new Date(member.endAt);
    const diffDays = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    if (!isNaN(diffDays)) {
      if (!durations[dept]) durations[dept] = [];
      durations[dept].push(diffDays);
    }
  }

  return Object.entries(durations).map(([department, daysArray]) => {
    const average = daysArray.reduce((a, b) => a + b, 0) / daysArray.length;
    return { department, avgTenureDays: Math.round(average) };
  });
};

export const getMostCommonPortfolios = (root: Root) => {
  const countMap: Record<string, number> = {};
  for (const member of root.cabinet_members) {
    const title = member.department?.code || "Unknown";
    countMap[title] = (countMap[title] || 0) + 1;
  }
  return Object.entries(countMap)
    .map(([portfolio, count]) => ({ portfolio, count }))
    .sort((a, b) => b.count - a.count);
};
