import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Tab, Card } from "@mantine/core";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import {
  getMinistersByParty,
  getMinistersByRole,
  getGenderBreakdown,
  getAverageTermDuration,
  getOldestAndYoungestLeaders,
  getMostFrequentLeaders,
  getLeaderLifespanStats,
  getGovernmentTimeline,
  getDepartmentChangeCounts,
  getCabinetReshuffles,
  getDepartmentTenures,
  getMostCommonPortfolios,
} from "./";

const COLORS = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff7f50",
  "#a4de6c",
  "#d0ed57",
];

export default function PoliticalDashboard({ root }: { root: any }) {
  const partyData = getMinistersByParty(root);
  const roleData = getMinistersByRole(root);
  const genderData = getGenderBreakdown(root);
  const avgTerm = getAverageTermDuration(root);
  const { oldest, youngest } = getOldestAndYoungestLeaders(root);
  const mostFrequent = getMostFrequentLeaders(root);
  const { averageLifespan, averageServiceYears } = getLeaderLifespanStats(root);
  const timeline = getGovernmentTimeline(root);
  const departmentChanges = getDepartmentChangeCounts(root);
  const reshuffles = getCabinetReshuffles(root);
  const deptTenures = getDepartmentTenures(root);
  const topPortfolios = getMostCommonPortfolios(root);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Nepal Government Dashboard</h1>
      <Tabs defaultValue="party">
        <TabsList>
          <TabsTab value="party">Cabinet by Party</TabsTab>
          <TabsTab value="roles">Ministers by Role</TabsTab>
          <TabsTab value="gender">Gender Breakdown</TabsTab>
          <TabsTab value="term">Avg Term Duration</TabsTab>
          <TabsTab value="leaders">Leader Analytics</TabsTab>
          <TabsTab value="timeline">Timeline & History</TabsTab>
          <TabsTab value="departments">Department Analysis</TabsTab>
        </TabsList>

        <TabsPanel value="party">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">
              Cabinet Composition by Party
            </h2>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={partyData}
                  dataKey="ministers"
                  nameKey="party"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {partyData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="roles">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">Ministers by Role</h2>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={roleData}>
                <XAxis dataKey="role" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="gender">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">Gender Breakdown</h2>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={genderData}
                  dataKey="count"
                  nameKey="gender"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {genderData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>

        <TabsPanel value="term">
          <Card shadow="sm" padding="lg" className="mt-4">
            <h2 className="text-xl font-semibold mb-4">
              Average Term Duration
            </h2>
            <p className="text-lg">
              Average duration served by ministers:{" "}
              <strong>{avgTerm} days</strong>
            </p>
          </Card>
        </TabsPanel>

        <TabsPanel value="leaders">
          <Card shadow="sm" padding="lg" className="mt-4 space-y-3">
            <h2 className="text-xl font-semibold mb-2">Leader Analytics</h2>
            <p>
              <strong>Oldest Leader:</strong> {oldest?.name} (born{" "}
              {oldest?.birthDate})
            </p>
            <p>
              <strong>Youngest Leader:</strong> {youngest?.name} (born{" "}
              {youngest?.birthDate})
            </p>
            <p>
              <strong>Most Frequently Appointed:</strong> {mostFrequent?.name} (
              {mostFrequent?.count} times)
            </p>
            <p>
              <strong>Average Lifespan:</strong> {averageLifespan} years
            </p>
            <p>
              <strong>Average Political Service:</strong> {averageServiceYears}{" "}
              years
            </p>
          </Card>
        </TabsPanel>

        <TabsPanel value="timeline">
          <Card shadow="sm" padding="lg" className="mt-4 space-y-3">
            <h2 className="text-xl font-semibold mb-2">Government Timeline</h2>
            <p>
              <strong>Name:</strong> {timeline.name}
            </p>
            <p>
              <strong>Started:</strong> {timeline.startedAt}
            </p>
            <p>
              <strong>Ended:</strong> {timeline.endAt}
            </p>

            <h3 className="text-lg mt-4 font-semibold">
              Department Change Counts
            </h3>
            <ResponsiveContainer width="100%" height={600}>
              <BarChart data={departmentChanges} layout="vertical">
                <XAxis type="number" />
                <YAxis type="category" dataKey="department" />
                <Tooltip />
                <Bar dataKey="changes" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>

            <h3 className="text-lg mt-4 font-semibold">Cabinet Reshuffles</h3>
            <ul className="list-disc list-inside">
              {reshuffles.map((r, i) => (
                <li key={i}>
                  <strong>{r.leader}</strong> changed portfolio{" "}
                  <strong>{r.count}</strong> times
                </li>
              ))}
            </ul>
          </Card>
        </TabsPanel>
        <TabsPanel value="departments">
          <Card shadow="sm" padding="lg" className="mt-4 space-y-6">
            <h2 className="text-xl font-semibold">
              Department Tenure Averages
            </h2>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={deptTenures} layout="vertical">
                <XAxis type="number" />
                <YAxis dataKey="department" type="category" />
                <Tooltip />
                <Bar dataKey="avgTenureDays" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>

            <h2 className="text-xl font-semibold">Most Common Portfolios</h2>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={topPortfolios.slice(0, 10)} layout="vertical">
                <XAxis type="number" />
                <YAxis dataKey="portfolio" type="category" />
                <Tooltip />
                <Bar dataKey="count" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </TabsPanel>
      </Tabs>
    </div>
  );
}
