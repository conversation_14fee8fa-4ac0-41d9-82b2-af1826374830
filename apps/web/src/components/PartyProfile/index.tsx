import { useTranslation } from "next-i18next";
import { IParty } from "@/interfaces/IParty";
import { EntityInfo } from "../EntityInfo";
import { getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import { Group } from "@mantine/core";

type Params = {
  age: number;
};
interface IProps {
  data: IParty;
  renderDescription?: (params: Params) => React.ReactNode | JSX.Element;
  renderRightSection?: (params: Params) => React.ReactNode | JSX.Element;
  renderTitle?: (params: Params) => React.ReactNode | JSX.Element;
  renderInformation?: (params: Params) => React.ReactNode | JSX.Element;
  renderAfterContent?: (params: Params) => React.ReactNode | JSX.Element;
}
const PartyProfile = (props: IProps) => {
  const age = props.data.years || getAgeFromDate(props.data.startDate);
  const params = { age };
  return (
    <EntityInfo
      paperProps={{ h: "130px", miw: "300px" }}
      resources={"parties"}
      id={props.data.id + ""}
      rate={props.data.rating?.average}
      name={
        props.data.localName ||
        props.data.shortName ||
        props.data.name ||
        props.data.code
      }
      avatar={getImageUrlWithFallback(props.data.logo, props.data.id + "")}
      title={props.renderDescription?.(params)}
      address={props.renderInformation?.(params)}
      renderRightSection={() => props.renderRightSection?.(params)}
      renderInformation={() => props.renderInformation?.(params)}
      renderAfterContent={() => props.renderAfterContent?.(params)}
    />
  );
};

export default PartyProfile;
