// /components/NextBreadcrumb.tsx
"use client";

import React, { ReactNode } from "react";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Breadcrumbs, Group, Text } from "@mantine/core";
import { IconHome } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import SuperControl from "../SuperControl";
import { truncateString } from "@/utils";

type TBreadCrumbProps = {
  links: { label: string; href: string }[];
};

const AppBreadCrumb = ({ links }: TBreadCrumbProps) => {
  const { t } = useTranslation();

  return (
    <Breadcrumbs p={"xs"}>
      <Link href={"/"}>
        <IconHome size={14} />
      </Link>
      {links?.map(({ label, href }, index) => {
        return (
          <Group key={index}>
            <Link
              href={href}
              className="text-inherit text-sm line-clamp-1"
              key={index}
            >
              <Text hiddenFrom="md">
                {truncateString(t(`common:${label}`, label), 10)}
              </Text>
              <Text visibleFrom="md">{t(`common:${label}`, label)}</Text>
            </Link>{" "}
            {index === links.length - 1 && (
              <SuperControl
                entityType={links[0].href.split("/")[1]}
                entityId={links[links.length - 1].href.split("/").pop()!}
              />
            )}
          </Group>
        );
      })}
    </Breadcrumbs>
  );
};

export default AppBreadCrumb;
