import { getAgeFromDate } from "@/utils";
import {
  ActionIcon,
  Avatar,
  Box,
  Card,
  Paper,
  Rating,
  Stack,
  Text,
} from "@mantine/core";
import { IconHeart } from "@tabler/icons-react";
import Link from "next/link";

export function UserInfoAction(props: {
  img?: string | React.ReactNode;
  name?: string | React.ReactNode;
  description?: string | React.ReactNode;
  rating?: number;
  showRate?: boolean;
  resourceId?: number;
  resourceType?: string;
  renderBeforeAvatar?: () => React.ReactNode;
  renderTopRightSection?: (params: { age: number }) => React.ReactNode;
  additionalInformationEnabled?: boolean;
  isCakeDay?: boolean;
  renderDescription?: (params: {
    age: number;
  }) => React.ReactNode | JSX.Element;
  data?: any;
}) {
  const data = props.data || {};
  const age = data.birthDate ? getAgeFromDate(data.birthDate) : 0;
  const params = { age, data };

  return (
    <Card
      radius="md"
      p="xl"
      mah={"100%"}
      miw={{ base: "100%", sm: "15rem" }}
      maw={{ base: "100%" }}
      className="w-full md:w-72 md:shrink-0 relative transition-all"
      h={"100%"}
    >
      {/* <ActionIcon
        variant="transparent"
        size={"sm"}
        pos={"absolute"}
        top={10}
        right={20}
        color="gray"
      >
        <IconHeart />
      </ActionIcon> */}
      <Card.Section className="relative group overflow-hidden">
        <Stack w={"100%"} align="center" justify="center" p={"xs"}>
          {props.renderBeforeAvatar?.()}
          <Avatar
            src={typeof props.img === "string" ? props.img : undefined}
            size={80}
            radius={120}
          />
        </Stack>
      </Card.Section>
      <Link
        href={
          props.resourceId
            ? `/${props.resourceType}/${props.resourceId}`
            : "javascript:void(0)"
        }
        className="text-inherit  no-underline"
      >
        <Stack gap={1} align="center">
          {props.rating !== undefined && (
            <Rating value={props.rating} readOnly />
          )}
          <Text ta="center" fw={600} className="text-md  md:text-md">
            {props.name || "John Doe"}
          </Text>
          <Text
            ta="center"
            c="dimmed"
            className="text-xs  md:text-xs line-clamp-1"
          >
            {props.description || props.renderDescription?.(params)}
          </Text>
        </Stack>
      </Link>
    </Card>
  );
}
