import {
  Text,
  Avatar,
  Group,
  Box,
  Rating,
  Blockquote,
  Divider,
  <PERSON><PERSON>,
  <PERSON>ack,
  Toolt<PERSON>,
} from "@mantine/core";
import { IconStars } from "@tabler/icons-react";
import Link from "next/link";
import {
  FacebookIcon,
  FacebookShareButton,
  TwitterIcon,
  TwitterShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  RedditIcon,
  RedditShareButton,
} from "react-share";
import { SocialIcon } from "react-social-icons";

interface CommentProps {
  rating: number;
  postedAt: string;
  body: string;
  hash: string;
  author: {
    name: string;
    image: string;
  };
}
// Helper to determine color based on rating
const getRatingColor = (rating: number) => {
  if (rating >= 5) return "teal";
  if (rating >= 4) return "green";
  if (rating >= 3) return "yellow";
  if (rating >= 2) return "orange";
  return "red";
};

export function Comment({
  postedAt,
  body,
  author,
  rating,
  hash,
}: CommentProps) {
  const shareUrl = `https://nepaltracks.com/reviews/${hash}`;

  return (
    <Box w="100%" className="p-4">
      <Blockquote
        color={getRatingColor(rating)}
        iconSize={24}
        icon={<IconStars size={20} />}
        cite={
          <Group justify="space-between" className="mt-2">
            <Group gap="xs">
              <Avatar
                size="sm"
                src={author.image}
                alt={author.name}
                radius="xl"
              />
              <Group
                gap={5}
                className="leading-tight"
                align="center"
                justify="center"
              >
                <Link
                  target="_blank"
                  href={shareUrl}
                  className="no-underline text-inherit hover:underline text-sm font-medium text-foreground"
                >
                  {author.name}
                </Link>
                <Text size="xs" c="dimmed">
                  {postedAt}
                </Text>
              </Group>
            </Group>

            <Popover width={200} position="bottom-end" withArrow shadow="md">
              <Popover.Target>
                <Tooltip label="Share review" position="top">
                  <button className="hover:scale-105 transition-transform">
                    <SocialIcon
                      // url={shareUrl}
                      style={{ height: 20, width: 20 }}
                      bgColor="transparent"
                      fgColor="currentColor"
                    />
                  </button>
                </Tooltip>
              </Popover.Target>
              <Popover.Dropdown>
                <Group justify="center" gap="xs">
                  <FacebookShareButton url={shareUrl}>
                    <FacebookIcon size={24} round />
                  </FacebookShareButton>
                  <TwitterShareButton url={shareUrl}>
                    <TwitterIcon size={24} round />
                  </TwitterShareButton>
                  <WhatsappShareButton url={shareUrl}>
                    <WhatsappIcon size={24} round />
                  </WhatsappShareButton>
                  <RedditShareButton url={shareUrl}>
                    <RedditIcon size={24} round />
                  </RedditShareButton>
                  <SocialIcon
                    url={shareUrl}
                    style={{ height: 20, width: 20 }}
                    bgColor="transparent"
                    fgColor="currentColor"
                  />
                </Group>
              </Popover.Dropdown>
            </Popover>
          </Group>
        }
      >
        <Stack gap="xs">
          <Rating size="xs" readOnly value={rating} />
          <Text size="sm" lh={1.5}>
            {body}
          </Text>
        </Stack>
      </Blockquote>
    </Box>
  );
}
