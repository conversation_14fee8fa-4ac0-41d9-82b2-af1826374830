import { useState } from "react";
import {
  Card,
  Text,
  Progress,
  Group,
  Image,
  Badge,
  Title,
  ScrollArea,
  Box,
  Stack,
  ActionIcon,
  Rating,
} from "@mantine/core";
import { FaExclamationCircle } from "react-icons/fa";
import { ILeader } from "@/interfaces/ILeader";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  formatConstituencyAddress,
  formatDate,
  formatNumber,
  getAgeFromDate,
  getImageUrlWithFallback,
} from "@/utils";
import { IconStar, IconX } from "@tabler/icons-react";
import Link from "next/link";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import ContentsPopover from "../ContentsPopover";
import { IElectionResult } from "@/interfaces/IElections";
import { capitalize, uniq } from "lodash";
import { useTranslation } from "react-i18next";
import useLeaderImages from "@/hooks/useLeaderImages";

const parseLeader = (
  data: <PERSON>eader & { stats: any; rating: any; electionResults: any }
) => {
  const party = data.party_leaders?.[0]?.party;
  const electionResults = (data.electionResults || []) as IElectionResult[];

  return {
    ...data,
    id: data.id,
    name: data.name,
    localName: data.localName,
    position: data.party_leaders?.[0]?.position || "Unknown",
    country: "Nepal",
    approvalRating: parseFloat(data.rating?.average || "0") * 20,
    birthDate: data.birthDate?.split("T")[0],
    image: data.img,
    achievements: data.experience?.split("\n").filter(Boolean) || [],
    controversies: Object.entries({
      ...(data.stats?.SCANDAL?.statuses || {}),
      ...(data.stats?.CONTROVERSIES?.statuses || {}),
    }).map(([type, count]) => `${capitalize(type)} (${count})`),
    positions: uniq(
      electionResults
        .filter((item) => item.isElected)
        .map(
          (item) =>
            item.candidacyType?.localName + " / " + item.candidacyType?.name
        )
    ).concat(
      data.tenures.cabinetTenures.map(
        (item) =>
          `${item.department.name} | ${formatDate(
            item.startedAt,
            "yyyy/MM/dd"
          )}`
      )
    ),
    statistics: {
      votes: electionResults.map(
        (r: IElectionResult) =>
          `${r.elections.name}: ${formatNumber(r.voteCount)} ${
            r.isElected ? r.remarks : ""
          }, ${formatConstituencyAddress({
            area: r.area,
            district: r.districts,
            municipal: r.municipals,
            ward: r.ward,
          })}`
      ),
      scandalCount: data.stats?.SCANDAL?.total || "N/A",
      achievementCount: data.stats?.ACHIEVEMENTS?.total || "N/A",
      promisesCount: data.stats?.PROMISES?.total || "N/A",
      averageRating: data.rating?.average || "0",
    },
    projects: data.projects?.map(
      (item: any) => `${item.name} / ${item.status}`
    ),
    education: data.nameOfInst || "N/A",
    party: {
      name: party?.name || "",
      symbol: party?.electionSymbol || "",
      color: party?.partyColorCode || "#000",
      logo: party?.logo || "",
    },
  };
};

const LeaderCompare = (props: {
  leader: ILeader;
  onRemove?: (data: ILeader) => void;
}) => {
  //@ts-expect-error
  const leader = parseLeader(props.leader);
  const age = getAgeFromDate(leader.birthDate);
  const [hovered, setHovered] = useState(false);
  const { t } = useTranslation();
  const { defaultImage, randomImage } = useLeaderImages(leader);

  return (
    <Card
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      className="h-full flex flex-col"
      w={"100%"}
      h={"100%"}
    >
      {hovered && (
        <ActionIcon
          className="animate__animated animate__fadeIn"
          radius={"xl"}
          pos={"absolute"}
          right={10}
          variant="light"
          onClick={() => props.onRemove?.(props.leader)}
        >
          <IconX />
        </ActionIcon>
      )}
      <Box h={"40%"}>
        <Stack justify="center" h={"80%"} align="center">
          <Image
            fallbackSrc={defaultImage}
            src={randomImage}
            alt={leader.name}
            w={"200px"}
            h={"100px"}
            radius="xl"
          />
          <Stack gap={5} align="center">
            <Group justify="start" align="center" gap={5}>
              <Link
                href={`/leaders/${leader.id}`}
                target="_blank"
                className="text-inherit no-underline hover:underline"
              >
                <Title order={3}>{leader.name}</Title>
              </Link>
              <Badge color="gray" size="xs">
                {age}
              </Badge>
            </Group>
            <Stack gap={1} justify="center" align="center">
              {leader.rating !== undefined && (
                <Rating value={leader.rating.average} readOnly size="xs" />
              )}{" "}
              <Text size="sm">
                {t("common:born", "Born")}: {leader.birthDate}{" "}
              </Text>
              <Text size="sm" c={"dimmed"}>
                {leader.address}{" "}
              </Text>
            </Stack>
          </Stack>
        </Stack>

        <Box className="mt-4" h={"20%"}>
          <Text size="xs" fw={500}>
            {t("common:approval_rating", "Approval rating")}
          </Text>
          <Progress
            value={leader.approvalRating}
            color={leader.approvalRating > 50 ? "green" : "red"}
            size="lg"
          />

          <Text size="xs" ta="center">
            {leader.approvalRating}%
          </Text>
        </Box>
      </Box>

      <ScrollArea style={{ flex: 1, minHeight: 0 }} h={"60%"}>
        <div className="grid grid-rows-[minmax(6rem,auto)_minmax(6rem,auto)_minmax(6rem,auto)_minmax(6rem,auto)] gap-4">
          {/* <div>
            <Text fw={500}>Achievements</Text>
            <ul className="list-disc list-inside text-sm min-h-[6rem]">
              {leader.achievements.length > 0 ? (
                leader.achievements.map((ach, idx) => (
                  <li key={idx}>
                    <FaCheckCircle className="inline mr-2 text-green-600" />
                    {ach}
                  </li>
                ))
              ) : (
                <Text size="xs" color="dimmed">
                  No achievements listed.
                </Text>
              )}
            </ul>
          </div> */}
          <div>
            <Group>
              <Text fw={500}>{t("common:tenures", "Tenures")}</Text>
            </Group>
            <ul className="list-disc list-inside text-sm max-h-[6rem]">
              {leader.positions.length > 0 ? (
                leader.positions.map((con, idx) => (
                  <li key={idx} className="mt-1">
                    <Link href={`/leaders/${leader.id}/tenures`}>{con}</Link>
                  </li>
                ))
              ) : (
                <Text size="xs" color="dimmed">
                  {t("common:no_positions_recorded", {
                    defaultValue: "No positions recorded.",
                  })}
                </Text>
              )}
            </ul>
          </div>
          <div>
            <Group>
              <Text fw={500}>{t("common:projects", "Projects")}</Text>
            </Group>
            <ul className="list-disc list-inside text-sm max-h-[6rem]">
              {(leader?.projects ?? []).length > 0 ? (
                leader?.projects?.map((con, idx) => (
                  <li key={idx} className="mt-1">
                    <Link
                      href={`/leaders/${leader.id}/achievements`}
                      className="no-underline"
                    >
                      {con}
                    </Link>
                  </li>
                ))
              ) : (
                <Text size="xs" color="dimmed">
                  {t("common:no_projects_recorded", {
                    defaultValue: "No projects recorded.",
                  })}
                </Text>
              )}
            </ul>
          </div>

          <div>
            <Group>
              <Text fw={500}>{t("common:controversies", "Controversies")}</Text>
              {/* @ts-ignore */}
              <ContentTypeIconMap.CONTROVERSIES
                color={ContentTypeColors.CONTROVERSIES}
              />
            </Group>
            <ul className="list-disc list-inside text-sm max-h-[6rem]">
              {leader.controversies.length > 0 ? (
                leader.controversies.map((con, idx) => (
                  <li key={idx}>
                    <Link href={`/leaders/${leader.id}/scandals`}>
                      {/* @ts-ignore */}
                      <FaExclamationCircle className="inline mr-2 text-red-500" />
                      {con}
                    </Link>
                  </li>
                ))
              ) : (
                <Text size="xs" color="dimmed">
                  {t("common:no_controversies_recorded", {
                    defaultValue: "No controversies recorded.",
                  })}
                </Text>
              )}
            </ul>
          </div>

          <div>
            <Text fw={500}>{t("common:statistics", "Statistics")}</Text>
            <ul className="text-sm space-y-1 min-h-[6rem]">
              {/* @ts-ignore */}
              {leader.statistics.votes.map((vote, idx) => (
                <li key={idx}>{vote}</li>
              ))}
              <li>
                <ContentsPopover
                  renderTitle={() => {
                    return (
                      <Group gap={5}>
                        {
                          // @ts-expect-error
                          <ContentTypeIconMap.PROMISES
                            color={ContentTypeColors.PROMISES}
                          />
                        }
                        {t("common:promises", "Promises")}:{" "}
                        {leader.statistics.promisesCount || 0}
                      </Group>
                    );
                  }}
                  resourceType={EntityTypeEnum.Leader}
                  contentType={["PROMISES"]}
                  resourceId={leader.id + ""}
                />
              </li>
              <li>
                <ContentsPopover
                  renderTitle={() => {
                    return (
                      <Group gap={5}>
                        {
                          // @ts-expect-error
                          <ContentTypeIconMap.SCANDAL
                            color={ContentTypeColors.SCANDAL}
                          />
                        }
                        {t("common:scandals", "Scandals")}:{" "}
                        {leader.statistics.scandalCount || 0}
                      </Group>
                    );
                  }}
                  resourceType={EntityTypeEnum.Leader}
                  contentType={["SCANDAL"]}
                  resourceId={leader.id + ""}
                />
              </li>
              <li>
                <ContentsPopover
                  renderTitle={() => {
                    return (
                      <Group gap={5}>
                        {
                          // @ts-expect-error
                          <ContentTypeIconMap.ACHIEVEMENTS
                            color={ContentTypeColors.ACHIEVEMENTS}
                          />
                        }
                        {t("common:achievements_logged", "Achievements logged")}
                        : {leader.statistics.achievementCount || 0}
                      </Group>
                    );
                  }}
                  resourceType={EntityTypeEnum.Leader}
                  contentType={["ACHIEVEMENTS"]}
                  resourceId={leader.id + ""}
                />
              </li>
              <li>
                <Group gap={5}>
                  <IconStar size={12} />
                  {t("common:average_rating", "Average rating")}:{" "}
                  {leader.statistics.averageRating} ★
                </Group>
              </li>
            </ul>
          </div>

          <div>
            <Text fw={500}>
              {t("common:education_party", "Education & Party")}
            </Text>
            <Text size="sm">
              {t("common:education", "Education")}: {leader.education}
            </Text>
            <Group gap="xs" mt="xs">
              <Image
                src={leader.party?.logo}
                alt={leader.party?.name}
                width={24}
                height={24}
              />
              <Badge color={leader.party?.color}>{leader.party?.name}</Badge>
            </Group>
          </div>
        </div>
      </ScrollArea>
    </Card>
  );
};

export default LeaderCompare;
