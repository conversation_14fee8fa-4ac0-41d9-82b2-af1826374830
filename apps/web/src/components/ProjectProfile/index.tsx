import { EntityInfo } from "../EntityInfo";
import { getAgeFromDate } from "@/utils";
import { IProject } from "@/interfaces/IProject";
import { Group, Paper, Progress, Stack, Text } from "@mantine/core";
import Link from "next/link";
import TenureBadge from "../TenureBadge";
import { useTranslation } from "react-i18next";

type Params = {
  age: number;
};
interface IProps {
  data: IProject;
  renderDescription?: (params: Params) => React.ReactNode | JSX.Element;
  renderRightSection?: (params: Params) => React.ReactNode | JSX.Element;
  renderTitle?: (params: Params) => React.ReactNode | JSX.Element;
  renderInformation?: (params: Params) => React.ReactNode | JSX.Element;
}
const ProjectProfile = ({ data: project }: IProps) => {
  const { t } = useTranslation();

  return (
    <EntityInfo
      title={project.type}
      linkComponent={Link}
      resources={"projects"}
      partyId={project.id + ""}
      id={project.id + ""}
      name={project.name}
      avatar={
        project.image ??
        project.mediaGallery?.[0] ??
        "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
      }
      // phone={project.progress}
      rate={project.rating?.average}
      renderInformation={() => (
        <Text fz="xs" c="dimmed">
          {project.description}
        </Text>
      )}
      renderAfterContent={() => (
        <Stack w={"100%"} gap={5} mt={"md"}>
          <Group justify="apart">
            <TenureBadge
              startedAt={project.startDate}
              endAt={project.endDate}
            />
          </Group>
          <Progress.Root size="xl">
            <Progress.Section value={project.progress || 0} color="cyan">
              <Progress.Label>
                {t("common:completed", "Completed")} {project.progress}%
              </Progress.Label>
            </Progress.Section>
          </Progress.Root>{" "}
        </Stack>
      )}
      titleTextAlign="start"
    />
  );
};

export default ProjectProfile;
