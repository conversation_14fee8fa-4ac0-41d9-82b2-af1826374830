import { Avatar, Badge, Group, Text } from "@mantine/core";
import Link from "next/link";
import DataTable from "./index";
import { getImageUrlWithFallback } from "@/utils";

/**
 * Example usage of the DataTable component
 */
const DataTableExample = () => {
  return (
    <>
      {/* Example 1: Government Parties Table */}
      <DataTable
        endpoint="governments/federal/1/parties"
        queryKey="governmentParties"
        title="Coalition Parties"
        columns={[
          {
            id: "party",
            label: "Party",
            renderCell: (value, row: any) => (
              <Link href={`/parties/${row.party.id}`}>
                <Group gap="sm">
                  <Avatar
                    size="md"
                    src={getImageUrlWithFallback(row.party.img, row.party.ecCandidateID)}
                  />
                  <Text fw={500}>{row.party.name}</Text>
                </Group>
              </Link>
            ),
          },
          {
            id: "seats",
            label: "Seats",
            renderCell: (value, row: any) => (
              <Badge>{row.seats || 0}</Badge>
            ),
          },
          {
            id: "leader",
            label: "Leader",
            renderCell: (value, row: any) => (
              row.party.leader ? (
                <Link href={`/leaders/${row.party.leader.id}`}>
                  <Text>{row.party.leader.localName}</Text>
                </Link>
              ) : (
                <Text>-</Text>
              )
            ),
          },
        ]}
      />

      {/* Example 2: Leaders Table */}
      <DataTable
        endpoint="leaders"
        queryKey="leaders"
        title="Leaders"
        queryParams={{ limit: 20 }}
        columns={[
          {
            id: "localName",
            label: "Name",
            renderCell: (value, row: any) => (
              <Link href={`/leaders/${row.id}`}>
                <Group gap="sm">
                  <Avatar
                    size="md"
                    src={getImageUrlWithFallback(row.img, row.ecCandidateID)}
                  />
                  <Text fw={500}>{value}</Text>
                </Group>
              </Link>
            ),
          },
          {
            id: "birthDate",
            label: "Birth Date",
          },
          {
            id: "address",
            label: "Address",
          },
        ]}
      />
    </>
  );
};

export default DataTableExample;
