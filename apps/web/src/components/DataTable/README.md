# DataTable Component

A reusable component that abstracts data fetching and table rendering logic.

## Features

- Automatic data fetching using react-query
- Pagination support
- Loading states with skeletons
- Customizable columns with cell renderers
- Responsive design with ScrollArea

## Usage

```tsx
import DataTable from "@/components/DataTable";
import { Avatar, Group, Text } from "@mantine/core";
import Link from "next/link";

const GovernmentPartiesTable = ({ governmentId, level }) => {
  return (
    <DataTable
      endpoint={`governments/${level}/${governmentId}/parties`}
      queryKey={`governmentParties-${governmentId}`}
      title="Coalition Parties"
      columns={[
        {
          id: "party",
          label: "Party",
          renderCell: (value, row) => (
            <Link href={`/parties/${row.party.id}`}>
              <Group gap="sm">
                <Avatar size="md" src={row.party.img} />
                <Text fw={500}>{row.party.name}</Text>
              </Group>
            </Link>
          ),
        },
        {
          id: "seats",
          label: "Seats",
        },
        {
          id: "leader",
          label: "Leader",
          renderCell: (value, row) => (
            row.party.leader ? (
              <Link href={`/leaders/${row.party.leader.id}`}>
                <Text>{row.party.leader.localName}</Text>
              </Link>
            ) : (
              <Text>-</Text>
            )
          ),
        },
      ]}
    />
  );
};

export default GovernmentPartiesTable;
```

## Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| `endpoint` | `string` | API endpoint to fetch data from | Required |
| `queryKey` | `string` | Unique key for react-query cache | Required |
| `columns` | `DataTableColumn[]` | Column definitions for the table | Required |
| `queryParams` | `Record<string, any>` | Additional query parameters to pass to the API | `{}` |
| `limit` | `number` | Number of items per page | `10` |
| `initialPage` | `number` | Initial page number | `1` |
| `title` | `string` | Title to display above the table | `undefined` |
| `minHeight` | `number` | Minimum height of the table | `500` |
| `showPagination` | `boolean` | Whether to show pagination | `true` |

### DataTableColumn

| Prop | Type | Description |
|------|------|-------------|
| `id` | `string` | Unique identifier for the column, used to access data from the row |
| `label` | `string \| JSX.Element` | Column header label |
| `renderCell` | `(value: any, data: unknown) => JSX.Element` | Optional function to customize cell rendering |

## Examples

See `example.tsx` for more usage examples.
