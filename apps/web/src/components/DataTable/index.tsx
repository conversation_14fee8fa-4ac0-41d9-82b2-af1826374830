import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { useState } from "react";
import {
  Pagination,
  PaginationProps,
  Skeleton,
  Stack,
  Table,
  Title,
  ScrollArea,
} from "@mantine/core";
import { useTranslation } from "react-i18next";

export interface DataTableColumn {
  id: string;
  label: string | JSX.Element;
  renderCell?: (value: any, data: unknown) => JSX.Element;
}

export interface DataTableProps {
  /**
   * API endpoint to fetch data from
   * Example: "governments/federal/1/parties"
   */
  endpoint: string;

  /**
   * Unique key for react-query cache
   * Example: "governmentParties"
   */
  queryKey: string;

  /**
   * Additional query parameters to pass to the API
   */
  queryParams?: Record<string, any>;

  /**
   * Column definitions for the table
   */
  columns: DataTableColumn[];

  /**
   * Number of items per page
   * @default 10
   */
  limit?: number;

  /**
   * Initial page number
   * @default 1
   */
  initialPage?: number;

  /**
   * Title to display above the table
   */
  title?: string;

  /**
   * Minimum height of the table
   * @default 500
   */
  minHeight?: number;

  /**
   * Whether to show pagination
   * @default true
   */
  showPagination?: boolean;
}

/**
 * DataTable component that handles data fetching and rendering in a table
 *
 * @example
 * ```tsx
 * <DataTable
 *   endpoint="governments/federal/1/parties"
 *   queryKey="governmentParties"
 *   columns={[
 *     { id: "name", label: "Name" },
 *     { id: "seats", label: "Seats" },
 *     {
 *       id: "leader",
 *       label: "Leader",
 *       renderCell: (value, row) => <Link href={`/leaders/${row.leaderId}`}>{value}</Link>
 *     }
 *   ]}
 * />
 * ```
 */
const DataTable = (props: DataTableProps) => {
  const {
    endpoint,
    queryKey,
    queryParams = {},
    columns,
    limit = 10,
    initialPage = 1,
    title,
    minHeight = 500,
    showPagination = true,
  } = props;

  const [page, setPage] = useState(initialPage);

  // Fetch data using react-query
  const dataQuery = useQuery([queryKey, page, queryParams], async () => {
    const { data } = await ApiService.resource.getAll(endpoint, {
      page,
      limit,
      ...queryParams,
    });
    return data;
  });

  // @ts-expect-error
  const data = dataQuery.data?.items || dataQuery.data?.data || [];
  const totalItems = dataQuery.data?.totalItems || 0;
  const isLoading = dataQuery.isLoading;
  const { t } = useTranslation();
  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalItems / limit);

  return (
    <Stack align="center" gap="md">
      {title && <Title order={5}>{title}</Title>}

      <ScrollArea w={"100%"}>
        <Table striped verticalSpacing="sm" w="100%" mih={minHeight}>
          <Table.Thead>
            <Table.Tr>
              {columns.map((col) => (
                <Table.Th key={col.id}>{col.label}</Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            {/* Loading state */}
            {isLoading &&
              Array(limit)
                .fill(true)
                .map((_, index) => (
                  <Table.Tr key={`loading-${index}`}>
                    {columns.map((col, colIndex) => (
                      <Table.Td key={`loading-cell-${colIndex}`}>
                        <Skeleton h={30} w={70} />
                      </Table.Td>
                    ))}
                  </Table.Tr>
                ))}

            {/* Data rows */}
            {!isLoading &&
              // @ts-expect-error
              data.map((row, rowIndex) => (
                <Table.Tr key={`row-${rowIndex}`}>
                  {columns.map((col, colIndex) => (
                    <Table.Td key={`cell-${rowIndex}-${colIndex}`} maw={400}>
                      {col.renderCell
                        ? col.renderCell(row[col.id], row)
                        : row[col.id]}
                    </Table.Td>
                  ))}
                </Table.Tr>
              ))}
          </Table.Tbody>
        </Table>
      </ScrollArea>

      {/* Pagination */}
      {showPagination && totalPages > 0 && (
        <Pagination
          total={totalPages}
          value={page}
          onChange={setPage}
          mt="sm"
        />
      )}

      {/* Results count */}
      {!isLoading && <Title order={6}>{totalItems} Result(s)</Title>}
    </Stack>
  );
};

export default DataTable;
