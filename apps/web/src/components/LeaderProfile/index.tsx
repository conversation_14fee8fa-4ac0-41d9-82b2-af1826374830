import { ILeader } from "@/interfaces/ILeader";
import { UserInfoAction } from "../UserInfoAction";
import Link from "next/link";
import { getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import { VoteCountBox } from "../VoteCountBox";
import { Badge, Group } from "@mantine/core";

const LeaderProfile = ({
  data,
  renderDescription,
  renderBeforeAvatar,
}: {
  renderBeforeAvatar?: (params: { age: number }) => React.ReactNode;
  data: ILeader;
  renderDescription?: (params: {
    age: number;
  }) => React.ReactNode | JSX.Element;
}) => {
  if (!data) {
    return null;
  }
  const age = getAgeFromDate(data.birthDate);
  return (
    <UserInfoAction
      renderBeforeAvatar={() => renderBeforeAvatar?.({ age })}
      img={getImageUrlWithFallback(data.img, data.ecCandidateID)}
      rating={data.rating?.average}
      name={
        <Link
          href={`/leaders/${data.id}`}
          className="text-inherit no-underline"
        >
          <Group justify="center" align="center" gap={5}>
            {data.localName}{" "}
            <Badge color="gray" size="xs">
              {age}
            </Badge>
          </Group>
        </Link>
      }
      description={renderDescription?.({ age }) || data.address}
    />
  );
};

export default LeaderProfile;
