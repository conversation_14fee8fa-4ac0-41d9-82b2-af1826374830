import { useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { SekeletonFeatureSlider } from "@/components/SekeletonFeatureSlider";
import { Stack } from "@mantine/core";

export function LazyFeedChunk({
  chunk,
  chunkIndex,
}: {
  chunk: JSX.Element[];
  chunkIndex: number;
}) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin: "200px",
  });

  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (inView) {
      setVisible(true);

      // Analytics tracking for chunk visibility
      //@ts-expect-error
      if (window && typeof window.gtag === "function") {
        //@ts-expect-error
        window.gtag("event", "feed_chunk_visible", {
          chunkIndex,
          timestamp: Date.now(),
        });
      }
      // Or replace with your analytics tracking method here
    }
  }, [inView, chunkIndex]);

  return (
    <Stack
      ref={ref}
      style={{
        opacity: visible ? 1 : 0,
        transform: visible ? "translateY(0)" : "translateY(20px)",
        transition: "opacity 0.5s ease, transform 0.5s ease",
      }}
    >
      {visible ? chunk : <SekeletonFeatureSlider />}
    </Stack>
  );
}
