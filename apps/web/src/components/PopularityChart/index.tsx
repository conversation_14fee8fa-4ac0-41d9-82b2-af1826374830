import React from "react";
import { useQuery } from "react-query";
import { Badge, Center, Loader, Text } from "@mantine/core";
import {
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  CartesianGrid,
} from "recharts";
import { ApiService } from "../../../api";

type PopularityData = {
  week: string; // e.g. "2025-05-12T00:00:00.000Z"
  total_ratings: number;
  average_rating: number;
};

interface PopularityChartProps {
  resourceId: string;
  resourceType: string;
}

export function PopularityChart({
  resourceId,
  resourceType,
}: PopularityChartProps) {
  const { data, error, isLoading } = useQuery<PopularityData, Error>(
    ["popularity", resourceId, resourceType],
    async () => {
      const request = await ApiService.resource.getAll(
        `ratings/${resourceType}/${resourceId}/popularity`,
        {}
      );
      //@ts-expect-error
      return request.data?.ratingGraph;
    },
    { staleTime: 5 * 60 * 1000 } // cache for 5 mins
  );
  if (isLoading)
    return (
      <Center className="p-8">
        <Loader size="lg" variant="dots" />
      </Center>
    );

  if (error)
    return (
      <Center className="p-8">
        <Text color="red">Error loading popularity data: {error.message}</Text>
      </Center>
    );

  //@ts-expect-error
  if (!data || data.length < 5) return <></>;
  return (
    <div className="p-4 rounded shadow-md">
      <Text fw={700} size="lg" mb="md">
        Popularity Over Time
      </Text>
      <ResponsiveContainer width="100%" height={300}>
        {/*  @ts-expect-error   */}
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="week"
            tickFormatter={(tick) =>
              new Date(tick).toLocaleDateString(undefined, {
                month: "short",
                day: "numeric",
              })
            }
            minTickGap={15}
          />
          <YAxis yAxisId="left" orientation="left" />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, 5]}
            tickCount={6}
          />
          <Tooltip
            labelFormatter={(label) =>
              `Week of ${new Date(label).toLocaleDateString()}`
            }
          />
          <Legend verticalAlign="top" height={36} />
          <Bar
            yAxisId="left"
            dataKey="total_ratings"
            name="Total Ratings"
            fill="rgb(161 174 213)" // Indigo 600 (Tailwind color)
            barSize={20}
            radius={[4, 4, 0, 0]}
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="average_rating"
            name="Average Rating"
            stroke="#f59e0b" // Amber 500 (Tailwind color)
            strokeWidth={3}
            dot={{ r: 4 }}
          />
        </ComposedChart>
      </ResponsiveContainer>
      <Badge color="indigo" variant="light" mt="md">
        Data updates weekly
      </Badge>
    </div>
  );
}
