import { useEffect, useState } from "react";
import { ActionIcon } from "@mantine/core";
import { IconArrowUp } from "@tabler/icons-react";

const ScrollToTop = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      setVisible(window.pageYOffset > 300);
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (!visible) return null;

  return (
    <ActionIcon
      variant="filled"
      //   color="blue"
      radius="xl"
      size="lg"
      style={{
        position: "fixed",
        bottom: 20,
        right: 20,
        zIndex: 1000,
        boxShadow: "0 0 10px rgba(0,0,0,0.2)",
      }}
      onClick={scrollToTop}
    >
      <IconArrowUp />
    </ActionIcon>
  );
};

export default ScrollToTop;
