import React from "react";
import PopoverItem, { IPopoverItem } from "../PopoverItem";
import { IContentsContainerProps } from "@/containers/ContentsContainer";
import { Loader } from "@mantine/core";
import Link from "next/link";

const ContentsContainerLazy = React.lazy(
  () => import("../../containers/ContentsContainer")
);

const ContentsPopover = (
  props: Omit<IPopoverItem, "renderContent"> & IContentsContainerProps
) => {
  return (
    <React.Suspense>
      <PopoverItem
        {...props}
        renderTitle={() => {
          return (
            <Link href={`javascript:void(0)`} className="text-inherit">
              {props.renderTitle()}
            </Link>
          );
        }}
        renderContent={() => <ContentsContainerLazy {...props} />}
      />
    </React.Suspense>
  );
};
export default ContentsPopover;
