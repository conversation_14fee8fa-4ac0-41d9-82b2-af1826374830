import { useTranslation } from "next-i18next";
import { Carousel, CarouselProps } from "@mantine/carousel";
import {
  Skeleton,
  Stack,
  Text,
  Title,
  useMantineTheme,
  useMantineColorScheme,
  Paper,
  BoxProps,
} from "@mantine/core";
import { useDebounce } from "use-debounce";

declare module "@mantine/core" {
  interface BoxProps {
    "data-entity-type"?: string;
  }
}

interface IProps<T> {
  title: string | React.ReactNode;
  data: T[];
  isLoading?: boolean;
  renderItems: (data: T, index: number) => JSX.Element;
  carouselProps?: CarouselProps;
  fullWidthOnSP?: boolean;
  renderAfterData?: () => JSX.Element;
  renderAfterItem?: (data: T, index: number) => JSX.Element;
  boxProps?: BoxProps;
}
export function FeaturedSlider<T>(props: IProps<T>) {
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const slideSize = {
    base: props.data?.length < 2 || props.fullWidthOnSP ? "100%" : "50%",
    sm: props.data?.length < 2 || props.fullWidthOnSP ? "100%" : "30%",
    md: "18%",
  };

  const [loading] = useDebounce(props.isLoading, 1000);
  if (loading) {
    return (
      <Stack>
        <Text className="text-sm font-bold md:text-xl">
          <Skeleton w={"360px"} h={"20px"} mt={10} />
        </Text>
        <Carousel
          slideSize={slideSize}
          slideGap={{ base: 0, sm: "md", md: "xs" }}
          loop
          align={"start"}
          {...props.carouselProps}
        >
          {Array(6)
            .fill(true)
            .map?.((item, index) => (
              <Carousel.Slide key={index} ml={"md"}>
                <Skeleton w={"360px"} h={"200px"} mt={10} key={index} />
              </Carousel.Slide>
            ))}
        </Carousel>
      </Stack>
    );
  }

  const boxProps = props.boxProps || {};
  return props.data?.length || props.renderAfterData ? (
    <Paper
      bg={colorScheme === "dark" ? theme.colors.dark[5] : theme.colors.gray[1]}
      p={"xs"}
      radius={"sm"}
      {...boxProps}
    >
      <Stack datatype={boxProps["data-entity-type"] || ""}>
        {props.title ? <Title order={5}>{props.title}</Title> : null}
        <Carousel
          // mih={200}
          slideSize={slideSize}
          slideGap={{ base: "xs", sm: "md", md: "xs" }}
          loop
          align={"start"}
          withControls={props.data?.length > 1}
          {...props.carouselProps}
        >
          {props.data?.map?.((item, index) => (
            <Carousel.Slide key={index}>
              <Stack h={"100%"}>
                {props.renderItems?.(item, index) || <></>}
                {props.renderAfterItem?.(item, index)}
              </Stack>
            </Carousel.Slide>
          ))}

          {props.renderAfterData?.()}
        </Carousel>
      </Stack>
    </Paper>
  ) : null;
}
