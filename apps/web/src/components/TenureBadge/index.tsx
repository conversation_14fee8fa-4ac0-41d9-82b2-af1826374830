import { Badge, Tooltip, Group, Text } from "@mantine/core";
import {
  differenceInDays,
  differenceInMonths,
  differenceInYears,
  format,
  formatDistance,
} from "date-fns";
import { useTranslation } from "react-i18next";

interface TenureBadgeProps {
  startedAt?: string | Date | null;
  endAt?: string | Date | null;
  withTooltip?: boolean;
}

export default function TenureBadge({
  startedAt,
  endAt,
  withTooltip = true,
}: TenureBadgeProps) {
  const { t } = useTranslation();
  if (!startedAt) {
    return null;
  }

  const start = new Date(startedAt);
  const end = endAt ? new Date(endAt) : new Date();

  const days = differenceInDays(end, start);
  const months = differenceInMonths(end, start);
  const years = differenceInYears(end, start);

  let duration = "";
  if (years > 0) {
    duration = `${years} yr${years > 1 ? "s" : ""}`;
    if (months % 12 > 0) duration += ` ${months % 12} mo`;
  } else if (months > 0) {
    duration = `${months} mo`;
  } else {
    duration = `${days} day${days !== 1 ? "s" : ""}`;
  }

  const isOngoing = !endAt;

  const content = (
    <Group gap={6} wrap="nowrap" align="center">
      <Text fw={700} color={isOngoing ? "teal" : "dimmed"} size="xs">
        {duration}
      </Text>
      <Text c="dimmed" size="xs">
        |
      </Text>
      <Text c="dimmed" size="xs" fw={600}>
        {format(start, "yyyy-MM-dd")} –{" "}
        {isOngoing ? t("common:present") : format(end, "yyyy-MM-dd")}
      </Text>
    </Group>
  );

  if (withTooltip) {
    return (
      <Tooltip
        label={`Start: ${format(start, "PPP")}${
          isOngoing ? "" : ` | End: ${format(end, "PPP")}`
        }`}
        withArrow
        position="top"
        multiline
      >
        <Badge
          size="xs"
          variant="light"
          radius="sm"
          color={isOngoing ? "teal" : "gray"}
        >
          {content}
        </Badge>
      </Tooltip>
    );
  }

  return (
    <Badge
      variant="light"
      radius="sm"
      color={isOngoing ? "teal" : "gray"}
      px="xs"
      py={4}
      fw={500}
      size="sm"
    >
      {content}
    </Badge>
  );
}
