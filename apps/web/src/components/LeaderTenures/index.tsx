import { ILeader } from "@/interfaces/ILeader";
import { ICabinetMember } from "@/interfaces/ICabinetMember";
import { Accordion, Badge, Box, Card, Group, Stack, Text } from "@mantine/core";
import { differenceInDays, format } from "date-fns";
import { useTranslation } from "react-i18next";
import { EntityInfo } from "../EntityInfo";
import { startCase } from "lodash";
import { formatDate } from "@/utils";
import TenureBadge from "../TenureBadge";

interface LeaderTenuresProps {
  leader: ILeader;
}

const LeaderTenures: React.FC<LeaderTenuresProps> = ({ leader }) => {
  const { t } = useTranslation();
  return (
    <Accordion
      defaultValue={
        leader.tenures.cabinetTenures?.length ? "cabinet" : "parliament"
      }
    >
      <Accordion.Item value="cabinet">
        <Accordion.Control>
          <Group justify="space-between" align="end" w={"100%"} p={"sm"}>
            <Text fw={"500"}>{t("common:cabinet_ministries")}</Text>
            <Text size="sm">
              {leader.tenures.cabinetTenures?.length} {t("common:times")}
            </Text>
          </Group>
        </Accordion.Control>
        <Accordion.Panel>
          <Stack>
            {leader.tenures.cabinetTenures?.map?.((tenure) => {
              return (
                <EntityInfo
                  key={tenure.id}
                  resources="departments"
                  name={tenure.department.name}
                  avatar={
                    "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
                  }
                  id={tenure.department.id + ""}
                  partyId={tenure.department.id + ""}
                  address={tenure.department.description}
                  renderRightSection={() => {
                    return (
                      <Stack gap={5} align="center">
                        <Badge variant="filled  " radius={"xs"}>
                          {startCase(tenure.role + "")}
                        </Badge>
                        <TenureBadge
                          startedAt={tenure.startedAt}
                          endAt={tenure.endAt}
                        />
                      </Stack>
                    );
                  }}
                />
              );
            })}
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>

      <Accordion.Item value="parliament">
        <Accordion.Control>
          <Group justify="space-between" align="end" w={"100%"} p={"sm"}>
            <Text fw={"500"}>{t("common:parliaments")}</Text>
            <Text size="sm">
              {leader.tenures.parliamentTenures?.length} {t("common:times")}
            </Text>
          </Group>
        </Accordion.Control>
        <Accordion.Panel>
          <Stack>
            {leader.tenures.parliamentTenures?.map?.((tenure) => {
              return (
                <EntityInfo
                  key={tenure.id}
                  resources={`parliaments`}
                  name={
                    <Group>
                      <Badge variant="filled  " radius={"xs"}>
                        {tenure.parliament.houseType}
                      </Badge>

                      {tenure.parliament.name}
                    </Group>
                  }
                  avatar="https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
                  id={tenure.parliament.id + "/home"}
                  partyId={tenure.parliament.id + ""}
                  address={tenure.parliament.description}
                  renderRightSection={() => {
                    return (
                      <Stack gap={1} align="center">
                        <Badge variant="subtle" radius={"xs"}>
                          {tenure.parliament.startDate
                            ? formatDate(tenure.parliament.startDate)
                            : "N/A"}{" "}
                          -
                          {tenure.parliament.endDate
                            ? formatDate(tenure.parliament.endDate)
                            : " " +
                              t("common:present", { defaultValue: "Present" })}
                        </Badge>
                      </Stack>
                    );
                  }}
                />
              );
            })}
          </Stack>
        </Accordion.Panel>
      </Accordion.Item>
    </Accordion>
  );
};
export default LeaderTenures;
