import { useTranslation } from "next-i18next";
import { IGovernment } from "@/interfaces/IGovernment";
import { EntityInfo } from "../EntityInfo";
import { formatDate, getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import { Badge, Group, Stack, Text } from "@mantine/core";
import LeaderHeroProfile from "../LeaderHeroProfile";

type Params = {
  age: number;
};
interface IProps {
  data: IGovernment;
  renderDescription?: (params: Params) => React.ReactNode | JSX.Element;
  renderRightSection?: (params: Params) => React.ReactNode | JSX.Element;
  renderTitle?: (params: Params) => React.ReactNode | JSX.Element;
  renderInformation?: (params: Params) => React.ReactNode | JSX.Element;
  profileType?: "Leader" | "Party" | "Election";
  additionalInformationEnabled?: boolean;
  slideshowMode?: "hover" | "always";
  id?: string;
}

const GovernmentProfile = ({ data, ...props }: IProps) => {
  const { t } = useTranslation();

  const age = data.years || getAgeFromDate(data.startedAt);
  const params = { age };

  return (
    <EntityInfo
      paperProps={{ w: "350px" }}
      resources="governments"
      id={data.id + ""}
      name={`${
        data.head?.localName || data.head?.name || data.name
      }, ${formatDate(data.startedAt, "yyyy")}`}
      rate={data.rating?.average}
      avatar={
        data.img ||
        getImageUrlWithFallback(data.head?.img, data.head?.ecCandidateID)
      }
      title={
        <Group wrap="nowrap">
          <Text>
            {t(`common:${data.government_type.toLowerCase()}`, {
              defaultValue: data.government_type,
            })}{" "}
            {t("common:government", { defaultValue: "Government" })}
          </Text>
        </Group>
      }
      address={props.renderInformation?.(params)}
      renderRightSection={() => props.renderRightSection?.(params)}
      renderInformation={() => (
        <Stack gap={5}>
          <Text size="xs" c="dimmed">
            {formatDate(data.startedAt)} -{" "}
            {data.endAt ? formatDate(data.endAt) : t("common:present")}
          </Text>
          {!data.endAt && <Badge color={"green"}>{t("common:current")}</Badge>}
        </Stack>
      )}
    />
  );
};
export default GovernmentProfile;
