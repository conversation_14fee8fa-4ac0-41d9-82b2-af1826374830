import {
  Center,
  Flex,
  Grid,
  Paper,
  Progress,
  Rating,
  Stack,
  Text,
  Tooltip,
  ThemeIcon,
} from "@mantine/core";
import { IconThumbUp } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";

const ReviewAnalysis = (
  props: IReviewAnalysis & { onlyAnalysis?: boolean }
) => {
  const { t } = useTranslation();

  // Convert keys safely
  const rates = props.rates || {};

  const approvalCount = (rates["4"]?.total || 0) + (rates["5"]?.total || 0);
  const approvalPercent =
    props.count > 0 ? Math.round((approvalCount / props.count) * 100) : 0;

  // Find the most common rating
  const mostCommonRating = Object.entries(rates).sort(
    ([, a], [, b]) => (b.total || 0) - (a.total || 0)
  )[0]?.[0];

  return (
    <Paper
      p="md"
      radius="md"
      shadow="0"
      withBorder={!props.onlyAnalysis}
      className="transition-all  bg-dark-700"
      w={"100%"}
    >
      <Grid gutter="md" align="center">
        {/* Left: Summary */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Stack gap="xs" align="center" justify="center">
            <Text fz="4rem" fw={700}>
              {props.average || "0.0"}
            </Text>
            <Rating value={Math.floor(props.average || 0)} readOnly size="md" />
            <Text c="dimmed" size="xs">
              {props.count} {t("common:reviews", { defaultValue: "Reviews" })}
            </Text>

            <Tooltip label="Percentage of 4★ and 5★ ratings">
              <Flex gap={5} align="center" className="px-3 py-1 rounded-full">
                <ThemeIcon variant="light" size="sm">
                  <IconThumbUp size={14} />
                </ThemeIcon>
                <Text size="xs" fw={500}>
                  {approvalPercent}% {t("common:approval", "Approval")}
                </Text>
              </Flex>
            </Tooltip>

            {mostCommonRating && (
              <Text size="xs" c="dimmed">
                {t("common:most_common", "Most common")}: {mostCommonRating}★
              </Text>
            )}
          </Stack>
        </Grid.Col>

        {/* Right: Rating Breakdown */}
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Stack gap="xs" w="100%">
            {[5, 4, 3, 2, 1].map((star) => {
              const rate = rates[star.toString()];
              return (
                <Flex key={star} gap="sm" align="center">
                  <Text w={20} size="sm">
                    {star}★
                  </Text>
                  <Progress
                    value={rate?.percent || 0}
                    color={star >= 4 ? "teal" : star === 3 ? "yellow" : "red"}
                    radius="xl"
                    w="100%"
                    size="sm"
                  />
                  <Text size="xs" c="dimmed" w={40} ta="right">
                    {rate?.total || 0}
                  </Text>
                </Flex>
              );
            })}
          </Stack>
        </Grid.Col>
      </Grid>
    </Paper>
  );
};

export default ReviewAnalysis;
