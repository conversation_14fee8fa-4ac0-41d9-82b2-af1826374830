import { useTranslation } from "next-i18next";
import { IDepartment } from "@/interfaces/IDepartment";
import { EntityInfo } from "../EntityInfo";

const DepartmentProfile = ({
  data,
  renderAfterContent,
  renderRightSection,
  renderInformation,
}: {
  data: IDepartment;
  renderAfterContent?: (params: { data: IDepartment }) => React.ReactNode;
  renderRightSection?: (params: { data: IDepartment }) => React.ReactNode;
  renderInformation?: (params: { data: IDepartment }) => React.ReactNode;
}) => {
  return (
    <EntityInfo
      titleTextAlign="start"
      cardProps={{ mih: "150px" }}
      resources={"departments"}
      name={data.name}
      rate={data.rating?.average}
      noShadow
      avatar={
        data.logo ||
        "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
      }
      // avatar={getImageUrlWithFallback(null, props.data?.head?.ecCandidateID)}
      id={(data.id + "") as string}
      partyId={(data.id + "") as string}
      address={data.description}
      renderAfterContent={
        renderAfterContent ? () => renderAfterContent({ data }) : undefined
      }
      renderRightSection={
        renderRightSection ? () => renderRightSection({ data }) : undefined
      }
      renderInformation={
        renderInformation ? () => renderInformation({ data }) : undefined
      }

      // address={member.department.description}
      //   title={
      //     <Group>
      //       {goverment.government_type} Government
      //     </Group>
      //   }
    />
  );
};
export default DepartmentProfile;
