import { Box, Popover, PopoverProps, useMantineTheme } from "@mantine/core";
import { useDisclosure, useMediaQuery } from "@mantine/hooks";

export interface IPopoverItem {
  renderTitle: () => JSX.Element;
  renderContent: () => JSX.Element;
  popOverProps?: PopoverProps;
}
const PopoverItem = (props: IPopoverItem) => {
  const [opened, { close, open }] = useDisclosure(false);
  const theme = useMantineTheme();
  const mobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);

  return (
    <Popover
      width={mobile ? 300 : 450}
      // position="top"
      withArrow
      shadow="md"
      {...(props.popOverProps || {})}
    >
      <Popover.Target>
        <Box>{props.renderTitle()}</Box>
      </Popover.Target>

      <Popover.Dropdown>
        {/* <ContentsContainer
        resourceType={EntityTypeEnum.Leader}
        contentType={["ACHIEVEMENTS"]}
        resourceId={leader.id}
      /> */}
        {props.renderContent()}
      </Popover.Dropdown>
    </Popover>
  );
};

export default PopoverItem;
