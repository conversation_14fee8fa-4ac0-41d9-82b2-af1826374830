import { Carousel, CarouselProps } from "@mantine/carousel";
import { Skeleton, Stack, Text, Title } from "@mantine/core";

interface IProps<T> {
  carouselProps?: CarouselProps;
  fullWidthOnSP?: boolean;
}
export function SekeletonFeatureSlider<T>(props: IProps<T>) {
  const slideSize = {
    base: props.fullWidthOnSP ? "100%" : "50%",
    sm: props.fullWidthOnSP ? "100%" : "30%",
    md: "15%",
  };

  return (
    <Stack>
      <Text className="text-sm font-bold md:text-xl">
        <Skeleton w={"360px"} h={"20px"} mt={10} />
      </Text>
      <Carousel
        slideSize={slideSize}
        slideGap={{ base: 0, sm: "md", md: "xs" }}
        loop
        align={"start"}
        {...props.carouselProps}
      >
        {Array(6)
          .fill(true)
          .map?.((item, index) => (
            <Carousel.Slide key={index} ml={"md"}>
              <Skeleton w={"360px"} h={"200px"} mt={10} key={index} />
            </Carousel.Slide>
          ))}
      </Carousel>
    </Stack>
  );
}
