import { ICabinetMember } from "./ICabinetMember";
import { ICandidacyType } from "./IElectionSubResponse";
import { IParliamentMember } from "./IParliament";
import { IPartyLeader } from "./IPartyLeader";
import { IReviewAnalysis } from "./IReviewAnalysis";
export type GenderType = "MALE" | "FEMALE" | "OTHERS";
export interface ILeader {
  ecCandidateID: string;
  id: number;
  name: string;
  localName: any;
  description: any;
  img: any;
  address: any;
  contact: any;
  startedAt: any;
  endAt: any;
  createdAt: string;
  updatedAt: string;
  gender: GenderType;
  leaders_images: {
    enabled: boolean;
    isDefault: boolean;
    url: string;
    leadersId: number;
  }[];
  images?: string[];
  birthDate: string;
  deathDate: any;
  emailAddress: any;
  partyLeaders: IPartyLeader[];
  rating?: IReviewAnalysis;
  metadata: {
    [key: string]: string;
  };
  qualification: any;
  experience: string;
  otherDetails: string;
  nameOfInst: string;
  summaryNP?: string;
  summary?: string;
  tenures: {
    cabinetTenures: ICabinetMember[];
    parliamentTenures: IParliamentMember[];
  };
  candidacyType?: ICandidacyType;
  cabinets?: ICabinetMember[];
  party_leaders?: IPartyLeader[];
  projects?: any[];
  cabinet_members?: ICabinetMember[];
  fbPage?: string;
  twitterPage?: string;
  youtubePage?: string;
}
