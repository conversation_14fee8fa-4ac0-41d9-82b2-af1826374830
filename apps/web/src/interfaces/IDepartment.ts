import { ICabinetMember } from "./ICabinetMember";
import { ILeader } from "./ILeader";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IDepartment {
  id: number;
  createdAt: string;
  updatedAt: string;
  name: string;
  localName?: string;
  code: string;
  description: string;
  logo: string | null;
  coverImage: string | null;
  website: string | null;
  fbPage: string | null;
  twitterPage: string | null;
  youtubePage: string | null;
  metadata: any;
  cabinet_members: ICabinetMember[];
  rating?: IReviewAnalysis;
  summary?: string;
  summaryNP?: string;
  rank?: number;
  stats: any;
}
