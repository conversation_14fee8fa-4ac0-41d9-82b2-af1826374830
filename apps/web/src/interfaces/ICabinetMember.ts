import { IDepartment } from "./IDepartment";
import { ILeader } from "./ILeader";
import { IParty } from "./IParty";

export interface ICabinetMember {
  id: number;
  governmentId: number;
  leaderId?: number;
  img?: string;
  departmentId?: number;
  role: string | null;
  rank: number | null;
  startedAt: string;
  endAt: string | null;
  isResigned: boolean;
  isActing: boolean;
  portfolioTitle: string | null;
  remarks: string | null;
  appointedBy: string | null;
  appointmentMethod: string | null;
  officialLink: string | null;
  createdAt: string;
  updatedAt: string;
  leaders?: ILeader & { additionalCabinets?: ICabinetMember[] };
  department?: IDepartment;
  leaderName?: string;
  partyName?: string;
  party?: IParty;
}
