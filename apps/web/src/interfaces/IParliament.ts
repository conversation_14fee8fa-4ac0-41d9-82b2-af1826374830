import { IElections, IStates } from "./IElectionSubResponse";
import { IGovernment } from "./IGovernment";
import { ILeader } from "./ILeader";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IParliamentQuery {
  count: number;
  stateId: string;
  state?: {
    id: number;
    localName: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
}
export interface IParliament {
  id: number;
  governments?: IGovernment[];
  localName?: string;
  summaryNP?: string
  summary?: string
  stats: any
  name?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  createdAt: Date;
  houseType: string;
  states: IStates;
  updatedAt: Date;
  parliament_members?: IParliamentMember[];
  rating?: IReviewAnalysis;
  speakers?: {
    memberType: "SPEAKER" | "VICE_SPEAKER";
    electionType: "DIRECT";
    member: ILeader;
  }[];
  stateId?: string;
  electionId?: string;
  elections: IElections & {
    governments?: IGovernment[];
  }
}

export interface IParliamentMember {
  id: number;
  name: string;
  party: string;
  role: string;
  parliamentId: number;
  "memberType": "MEMBER",
  "electionType": "DIRECT" | "PROPORTIONAL",
  parliament: IParliament;
}
