import { CandidacyType, IElectionResult } from "./IElections";
import { ILeader } from "./ILeader";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IElections {
  id: number;
  name: string;
  year: string;
  createdAt: string;
  updatedAt: string;
  governmentId: any;
  electionType: string;
}

export interface ICandidacyType {
  localName: string;
  name: string;
}

export interface IMunicipals {
  id: number;
  localName: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  districtId: number;
  stateId: number;
  wards?: IWard[];
  districts?: IDistricts;
  states?: IStates;
  elections: IElectionResult[];
}

export interface IStates {
  id: number;
  localName: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface IDistricts {
  id: number;
  localName: string;
  name: string;
  code: string;
  createdAt: string;
  updatedAt: string;
  stateId: number;
  states?: IStates;
  districts?: IDistricts;
}

export interface IElectionSubResponse {
  ratings: IReviewAnalysis;
  allResults: IElectionResult[];
  elections: IElections;
  municipals: IMunicipals;
  states: IStates;
  ward: IWard;
  districts: IDistricts;
  candidacyType: CandidacyType;
  relatedElections: IElectionResult[];
  nearbyResults: IElectionResult[];
  previousElectionsSameArea: IElectionResult[];
  stats: {
    totalVote: number;
    percentages: {
      [key: string]: number;
    };
  };
}

export interface IWard {
  [key: string]: any;
}
