import { ILeader } from "./ILeader";
import { IParty } from "./IParty";

export type IElectionOverview = {
  electionsList: Array<{
    id: number;
    name: string;
    year: string;
    localName: string;
    electionType: string;
  }>;
  partyTrends: Array<{
    electionId: number;
    partyId: number;
    partyName: string;
    votes: number;
    seats: number;
    electionYear: number;
  }>;
  leaderTrends: Array<{
    leaderId: number;
    name: string;
    results: Array<{
      year: number;
      won: boolean;
    }>;
  }>;
  consistentWinners: Array<{
    leaderId: number;
    name: string;
    winCount: number;
    lossCount: number;
    totalContests: number;
    winRatio: number;
    leader: ILeader;
  }>;
  newPartyLaunches: Array<{
    partyId: number;
    name: string;
    localName?: string;
    logo?: string;
    electionSymbol?: string;
    color?: string;
    firstYear: number;
    seats: number;
    electionName?: string;
    votes: number;
  }>;
  fadingParties: Array<{
    partyId: number;
    name: string;
    lastYear: number;
    recentSeats: number;
  }>;
  leaderSwitches: Array<{
    leaderId: number;
    name: string;
    switches: number;
    leader: ILeader;
    parties: Array<{
      partyId: number;
      name: string;
      localName: string;
      logo: string;
      color: string;
    }>;
  }>;
  biggestWinMargins: Array<{
    year: number;
    district: string;
    margin: number;
    winner: string;
    runnerUp: string;
    leader: ILeader;
  }>;
  closeContests: Array<{
    year: number;
    leader: ILeader;
    district: string;
    margin: number;
    winner: string;
    runnerUp: string;
  }>;
  voteSeatTrends: Array<{
    partyId: number;
    partyName: string;
    year: number;
    votes: number;
    seats: number;
  }>;
  partyTrendsSeries: Array<{
    electionYear: number;
    partyId: number;
    name: string;
    localName: string;
    voteCount: number;
    votePercent: number;
    seatCount: number;
    seatPercent: number;
    color?: string;
  }>;
  bigDebutWinners: {
    leaderId: number;
    name: string;
    leader: ILeader;
    debutYear: number;
    debutVotes: number;
  }[];
  strugglingParties: {
    partyId: number;
    name: string;
    elections: number;
    wins: number;
    ratio: number;
    party: IParty;
  }[];
  repeatLosers: {
    leaderId: number;
    name: string;
    leader: ILeader;
    losses: number;
    total: number;
  }[];
  consistentParties: Array<{
    partyId: number;
    name: string;
    winYears: Array<number>;
    party: IParty;
    totalYears: Array<number>;
    consistencyRatio: number;
  }>;
  topLeadersPerElection: {
    leaderId: number;
    leader: ILeader;
    totalVotes: number;
    won: boolean;
    debut: boolean;
    electionName: string;
    electionId: number;
    electionYear: number;
  }[];
  topPartiesPerElection: {
    party: IParty;
    partyId: number;
    name: string;
    localName: string;
    logo: string | null;
    color: string;
    totalVotes: number;
    totalSeats: number;
    debut: boolean;
    electionId: number;
    electionName: string;
    electionYear: number;
  }[];
};
