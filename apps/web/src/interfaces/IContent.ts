import { IDepartment } from "./IDepartment";
import { IElections } from "./IElectionSubResponse";
import { IGovernment } from "./IGovernment";
import { ILeader } from "./ILeader";
import { IParliament } from "./IParliament";
import { IParty } from "./IParty";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IContent {
  id: number;
  resourceType: string;
  contentType: string;
  contentStatus: string;
  resourceId: number;
  summary?: string;
  content: string;
  title: string;
  cmsLink: string | null;
  slug: string | null;
  createdAt: string;
  updatedAt: string;
  metadata: any | null;
  eventDate: string | undefined;
  eventDueDate: string | undefined;
  eventEndDate: string | undefined;
  resource?:
    | IGovernment
    | ILeader
    | IParty
    | IParliament
    | IDepartment
    | IElections;
  childContents?: IContent[];
  counterContents?: IContent[];
  rating?: IReviewAnalysis;
}
