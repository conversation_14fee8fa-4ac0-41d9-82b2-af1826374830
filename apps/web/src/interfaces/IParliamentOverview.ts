import { IGovernment } from "./IGovernment";
import { ILeader } from "./ILeader";
import { IParliament } from "./IParliament";
import { IParty } from "./IParty";

export interface IParliamentMemberInfo {
    voteCount: number
    parties?: IParty
    leaders: ILeader;
    electionType: string;
    memberType: string;
}

export interface IParliamentPartyInfo {
    partyId: number;
    voteCount: number;
    runningPlaces: number;
    wonPlaces: number;
    party: IParty;
    members: IParliamentMemberInfo[];
    directMembersCount: number;
    proportionalMembersCount: number;
    totalMembersCount: number;
}

export interface IParliamentOverview {
    totalMembers: number;
    allDirectMembersCount: number;
    allProportionalMembersCount: number;
    rulingParties: IParliamentPartyInfo[];
    oppositionParties: IParliamentPartyInfo[];
    government: IGovernment;
    parliament: IParliament;
}