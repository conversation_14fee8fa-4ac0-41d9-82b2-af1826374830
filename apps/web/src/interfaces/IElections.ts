import {
  ICandidacyType,
  IDistricts,
  IMunicipals,
} from "./IElectionSubResponse";
import { ILeader } from "./ILeader";
import { IParty } from "./IParty";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IElectionResult {
  id: number;
  electionId: number;
  elCode: string;
  leaderId: number;
  voteCount: number;
  area: string;
  districtId: number;
  isElected: boolean;
  municipals: IMunicipals;
  createdAt: string;
  updatedAt: string;
  candidacyTypeId: number;
  wardId: any;
  partyId: number;
  code: string;
  remarks: string;
  candidacyType: ICandidacyType;
  districts: IDistricts;
  elections: Elections;
  ward: any;
  parties: IParty;
  localName?: string;
  leaders?: ILeader;
  second?: IElectionResult;
  rating?: IReviewAnalysis;
  previousElections?: IElectionResult[];
}

export interface CandidacyType {
  id: number;
  name: string;
  localName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Districts {
  id: number;
  localName?: string;
  name: string;
  code: string;
  createdAt: string;
  updatedAt: string;
  stateId: number;
}

export interface Elections {
  id: number;
  name: string;
  localName?: string;
  year: string;
  createdAt: string;
  updatedAt: string;
  governmentId: any;
  electionType: string;
}
