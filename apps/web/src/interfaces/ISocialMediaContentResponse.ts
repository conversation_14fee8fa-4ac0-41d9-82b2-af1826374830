import { IElectionResult } from "./IElections";
import { IGovernment } from "./IGovernment";
import { ILeader } from "./ILeader";
import { IParty } from "./IParty";

export interface ISocialMediaContentResponse {
    knowYourLocalRepresentatives: IElectionResult[],
    knowYourFederalRepresentatives: IElectionResult[],
    knowYourProvincialRepresentatives: IElectionResult[],
    knowYourLocalWardRepresentatives: IElectionResult[],
    randomElectionResults: IElectionResult[],
    anniversaryParties: IParty[],
    birthdayPost: ILeader[],
    anniversaryGovernments: IGovernment[],
}