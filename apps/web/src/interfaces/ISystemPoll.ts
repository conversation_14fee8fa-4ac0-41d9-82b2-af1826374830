export interface IPollOption {
  id: number;
  text: string;
  textLocal?: string;
  value: string;
  votes: number;
  percentage: number;
  _count?: {
    responses: number;
  };
}

export interface ISystemPoll {
  id: number;
  question: string;
  questionLocal?: string;
  title: string;
  titleLocal?: string;
  description?: string;
  descriptionLocal?: string;
  type: "RADIO" | "CHECKBOX";
  code?: string;
  hash?: string;
  resourceId: number;
  resourceType: string;
  resourceInfo?: IResourceInfo;
  createdBy?: "SYSTEM" | "USER";
  isDeleted?: boolean;
  createdAt: string;
  updatedAt: string;
  options: IPollOption[];
  totalResponses: number;
  hasUserResponded?: boolean;
  userResponse?: IPollOption[] | null;
  _count?: {
    responses: number;
  };
}

export interface ISystemPollsResponse {
  polls: ISystemPoll[];
  statistics?: {
    totalPolls: number;
    totalResponses: number;
    averageResponsesPerPoll: number;
    pollsWithResponses: number;
    pollsWithoutResponses: number;
  };
}

export interface IPollVoteRequest {
  pollId: number;
  optionIds: number[];
  anonymousId?: string;
}

export interface IPollVoteResponse {
  message: string;
  poll?: ISystemPoll;
}

export interface ITrendingPollsResponse {
  polls: ISystemPoll[];
  limit: number;
  resourceType?: string;
}

export interface IPollStatistics {
  totalPolls: number;
  totalResponses: number;
  averageResponsesPerPoll: number;
  pollsWithResponses: number;
  pollsWithoutResponses: number;
}

export type ResourceType =
  | "LEADER"
  | "PARTY"
  | "GOVERNMENT"
  | "PARLIAMENT"
  | "DEPARTMENT"
  | "PROJECT"
  | "MEDIA";

export interface IResourceInfo {
  type: ResourceType;
  id: number;
  name: string;
  nameLocal?: string;
  avatar?: string;
  logo?: string;
  description?: string;
  position?: string;
  positionLocal?: string;
  code?: string;
  electionSymbol?: string;
}

export interface IPollsResponse {
  data: ISystemPoll[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
