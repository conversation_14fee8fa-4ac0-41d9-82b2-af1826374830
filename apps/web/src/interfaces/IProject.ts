import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IProject {
  id: number;
  name: string;
  description?: string;
  type?: "MEGA" | "INFRASTRUCTURE" | "SMALL";
  status?: "PLANNED" | "ONGOING" | "COMPLETED" | "CANCELLED";
  sector?:
    | "ENERGY"
    | "TRANSPORT"
    | "WATER"
    | "IRRIGATION"
    | "URBAN_DEVELOPMENT"
    | "HEALTH"
    | "EDUCATION"
    | "AGRICULTURE"
    | "TELECOMMUNICATION";
  location?: string;
  startDate: string;
  endDate?: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  lastInspectedAt?: string;
  budget?: number;
  currency?: string; // e.g., "NPR", "USD"
  beneficiaries?: number;
  contractor?: string;
  fundingSource?: string;
  expectedOutcome?: string;
  challenges?: string;
  milestones?: Record<string, any>;
  image?: string;
  mediaGallery?: string[]; // Array of image/video URLs
  progress?: number; // 0-100
  link?: string;
  leaders?: any[];
  summary?: string;
  summaryNP?: string;
  rating?: IReviewAnalysis;
  createdAt: string;
  updatedAt: string;
}
