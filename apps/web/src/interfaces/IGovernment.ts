import { ICabinetMember } from "./ICabinetMember";
import { IGovernmentOverview } from "./IGovernmentOverview";
import { ILeader } from "./ILeader";
import { IReviewAnalysis } from "./IReviewAnalysis";

export interface IGovernment {
  id: number;
  years?: number;
  governmentName?: string;
  localName?: string;
  name: string;
  description: string;
  img: string | null;
  startedAt: string;
  endAt: string;
  createdAt: string;
  updatedAt: string;
  headId: number;
  government_type: string;
  municipalId: number | null;
  stateId: number | null;
  head: ILeader;
  cabinet_members: ICabinetMember[];
  governmentOverview?: IGovernmentOverview;
  summary?: string;
  summaryNP?: string;
  rating?: IReviewAnalysis;
  projects?: any[];
}
