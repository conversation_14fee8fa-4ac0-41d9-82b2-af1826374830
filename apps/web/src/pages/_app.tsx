import type { AppProps } from "next/app";
import RootStyleRegistry from "./emotion";
import {
  Alert,
  AppShell,
  Box,
  LoadingOverlay,
  Modal,
  Text,
} from "@mantine/core";
import Head from "next/head";
import { appWithTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { QueryClient, QueryClientProvider } from "react-query";
import NextNProgress from "nextjs-progressbar";
import React, { useEffect } from "react";
import { AppHeader } from "@/components/AppHeader";
//@ts-ignore
import { AuthProvider } from "@ri/fe-auth";
import Storage from "@/core/Storage";
import { AUTH_TOKEN_NAME } from "@/consts";
import { IOauthResponse } from "@/interfaces/IOauthResponse";
import { getDecodedCode, getFromCookie, saveToCookie } from "@/utils";
import { isPast } from "date-fns";
import AuthLayout from "@/components/AuthLayout";
// Import styles of packages that you've installed.
// All packages except `@mantine/hooks` require styles imports
import { Notifications } from "@mantine/notifications";
import "@mantine/core/styles.css";
import "@mantine/carousel/styles.css";
import "../../src/app/globals.css";
import { useGlobalState } from "@/store";
import "@mantine/notifications/styles.css";
import NextI18nextConfig from "../../next-i18next.config";
import { useTranslation } from "react-i18next";
import { Navbar } from "@/components/Navbar";
import {
  FaLandmark, // For parliaments
  FaSitemap, // For governments
  FaPeopleGroup, // For parties
  FaUserTie, // For leaders
  FaChartLine, // For elections
  FaRegNewspaper, // For contents
} from "react-icons/fa6";
import { useDisclosure } from "@mantine/hooks";
import SearchBox from "@/components/SearchBox";
import {
  FaBuilding,
  FaCity,
  FaHome,
  FaIndustry,
  FaTv,
  FaPoll,
} from "react-icons/fa";
import { HydrationSafeSuspense } from "../containers/HydrationSafeSuspense";
import Script from "next/script";
import { LeaderHoverProvider } from "@/contexts/LeaderHoverContext";
import ScrollToTop from "@/components/ScrollToTop";
import SigninContainer from "@/containers/SigninContainer";
import SocialShare from "@/components/SocialShare";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      refetchIntervalInBackground: false,
    },
  },
});

const menuItems = [
  { icon: FaHome, label: "common:home", link: "/" },
  { icon: FaUserTie, label: "common:leaders", link: "/leaders" },
  { icon: FaPeopleGroup, label: "common:parties", link: "/parties" },
  { icon: FaSitemap, label: "common:governments", link: "/governments" },
  { icon: FaLandmark, label: "common:parliaments", link: "/parliaments" },
  { icon: FaChartLine, label: "common:election", link: "/elections" },
  { icon: FaBuilding, label: "common:departments", link: "/departments" },
  {
    icon: FaCity,
    label: "common:municipals",
    link: "/governments/level/local",
  },
  { icon: FaPoll, label: "common:polls", link: "/polls" },
  { icon: FaRegNewspaper, label: "common:contents", link: "/contents" },
  { icon: FaTv, label: "common:medias", link: "/medias" },
  { icon: FaIndustry, label: "common:projects", link: "/projects" },
  { icon: FaPeopleGroup, label: "common:compare", link: "/compare" },
];
const linksMockdata = {
  Parliaments: [
    ["common:member"],
    ["bills"],
    ["notices"],
    ["news"],
    ["contacts"],
  ],
  "/governments": [
    ["Central government", "/governments/centre"],
    ["common:provincial_government", "/governments/province"],
    ["common:local_government", "/governments/local"],
  ],
  Parties: [
    ["common:member"],
    ["News"],
    ["common:scandals"],
    ["Manifestos"],
    ["About"],
  ],
};

const App = ({ Component, pageProps }: AppProps) => {
  const router = useRouter();
  const [isHamOpened, { close: closeHamMemu, open: openHamMemu }] =
    useDisclosure(!getFromCookie("isHamOpened"));

  const [
    isSearchModalOpened,
    { close: closeSearchModal, open: openSearchModal },
  ] = useDisclosure(false);

  const [isLoginModalOpened, { close: closeLoginModal, open: openLoginModal }] =
    useDisclosure(false);

  const onAuthenticationPath = React.useCallback(() => {
    if (typeof window === "undefined") return;
    const url = Storage.getInstance().get("returnUrl") || "/";
    return url;
  }, []);

  //@ts-ignore
  const links = linksMockdata[router.pathname]?.map?.(([label, link]) => ({
    link,
    label,
  }));
  const [setGlobalMessage, setGlobalLoading, message, isLoading] =
    useGlobalState((s) => [
      s.setMessage,
      s.toggleLoading,
      s.message,
      s.isLoading,
    ]);

  const handleMainHamClick = React.useCallback(() => {
    isHamOpened ? closeHamMemu() : openHamMemu();
    saveToCookie("isHamOpened", isHamOpened);
  }, [isHamOpened]);

  const isTopPage = router.pathname === "/";
  useEffect(() => {
    const handleRouteChangeStart = () => {
      //@ts-expect-error
      sessionStorage.setItem("scrollY", window.scrollY);
    };

    const handleRouteChangeComplete = () => {
      const y = sessionStorage.getItem("scrollY");
      if (y !== null) {
        window.scrollTo(0, parseInt(y));
      }
    };

    router.events.on("routeChangeStart", handleRouteChangeStart);
    router.events.on("routeChangeComplete", handleRouteChangeComplete);

    return () => {
      router.events.off("routeChangeStart", handleRouteChangeStart);
      router.events.off("routeChangeComplete", handleRouteChangeComplete);
    };
  }, [router]);

  useEffect(() => {
    const nextPath = router.asPath;
    if (
      nextPath.includes("callback") ||
      nextPath.includes("signin") ||
      nextPath.includes("signup") ||
      nextPath.includes("reset-password") ||
      nextPath.includes("forgot-password") ||
      nextPath.includes("resend-email")
    )
      return;
    Storage.getInstance().set("returnUrl", router.asPath);
  }, [router.asPath]);

  return (
    <>
      <Script
        defer={false}
        src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit"
        strategy="beforeInteractive"
      />
      <Script
        defer
        src="https://www.googletagmanager.com/gtag/js?id=G-WJHCN1DE1L"
        strategy="afterInteractive"
      />
      <Script
        defer
        id="ga-setup"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-WJHCN1DE1L');
          `,
        }}
      />
      <Head>
        {/* Animate.css */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
          integrity="sha512-c42qTSw/wPZ3/5LBzD+Bw5f7bSF2oxou6wEb+I/lqeaKV5FDIfMvvRp772y4jcJLKuGUOpbJMdg/BTl50fJYAw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </Head>
      <HydrationSafeSuspense>
        <NextNProgress
          color="#dc2d2e"
          options={{
            showSpinner: false,
          }}
        />

        <RootStyleRegistry>
          <Notifications />
          <Box>
            <LoadingOverlay
              visible={isLoading}
              pos={"fixed"}
              loaderProps={{
                pos: "fixed",
                size: "md",
                color: "#dc2d2e",
                variant: "oval",
              }}
            />
            <QueryClientProvider client={queryClient}>
              <AuthProvider<{ companyId: string }>
                restrictedPathsForAuth={[
                  "/auth/signin",
                  "/auth/signup",
                  "/account/reset-password",
                  "/account/forgot-password",
                ]}
                protectedPaths={["/app"]}
                pathname={router.pathname}
                onAuthenticationRedirectionPath={onAuthenticationPath()}
                renderOpenRoutes={(children: React.ReactNode) => {
                  return router.pathname.includes("/") ? (
                    <AuthLayout>{children}</AuthLayout>
                  ) : (
                    <> {children}</>
                  );
                }}
                renderPrivateRoutes={(children: React.ReactNode) => (
                  <AuthLayout> {children}</AuthLayout>
                )}
                onUnauthorizedAccess={() => router.push("/auth/signin")}
                onAuthenticated={() => {
                  const nextRoute = onAuthenticationPath();
                  router.push(nextRoute);
                }}
                authResolver={async () => {
                  const token = Storage.getInstance().get(
                    AUTH_TOKEN_NAME
                  ) as IOauthResponse;
                  if (!token || !token.accessToken)
                    throw new Error("Token not found");

                  const decodedCode = getDecodedCode();
                  if (isPast((decodedCode.exp as number) * 1000)) {
                    throw new Error("Token Expired");
                  }

                  return {
                    id: token.userId, // todo : ask BE to send userId in decoded code like of name
                    displayName: String(decodedCode.name),
                    email: String(decodedCode.email),
                    data: {},
                  };
                }}
              >
                <AppShell
                  w="100%"
                  header={{ height: 60 }}
                  navbar={{
                    width: {
                      base: 65,
                      sm: 0,
                      md: isTopPage && !isHamOpened ? 200 : 65,
                      lg: isTopPage && !isHamOpened ? 200 : 65,
                    },
                    breakpoint: "sm",
                    collapsed: {
                      mobile: true,
                      desktop: isTopPage ? false : isHamOpened,
                    },
                  }}
                >
                  <AppShell.Header>
                    <AppHeader
                      isHamOpened={isHamOpened}
                      onMainHamClick={handleMainHamClick}
                      userLinks={links || []}
                      mainLinks={menuItems || []}
                      onSearchIconClick={openSearchModal}
                    />
                  </AppShell.Header>
                  <AppShell.Navbar p="md" visibleFrom="md">
                    <Navbar
                      mainLinks={menuItems}
                      isTopPage={isTopPage}
                      isHamOpened={isHamOpened}
                    />
                  </AppShell.Navbar>
                  <AppShell.Main ml={"sm"} mt={"sm"}>
                    {message && (
                      <Alert
                        withCloseButton
                        className="text-center"
                        onClose={() => setGlobalMessage("")}
                      >
                        <Text>{message}</Text>
                      </Alert>
                    )}
                    <LeaderHoverProvider
                      openLoginModal={openLoginModal}
                      closeLoginModal={closeLoginModal}
                    >
                      {/* @ts-ignore */}
                      <Component {...pageProps} />
                      {router.pathname.includes("[tab]") &&
                        typeof window !== "undefined" && (
                          <Box bottom={10} right={10} p={"md"}>
                            <SocialShare url={window.location.href} />
                          </Box>
                        )}
                    </LeaderHoverProvider>
                    <Modal
                      title=""
                      size={"xl"}
                      overlayProps={{
                        // color:
                        //   //@ts-expect-error
                        //   theme.colorScheme === "dark"
                        //     ? theme.colors.dark[9]
                        //     : theme.colors.gray[2],
                        opacity: 1,
                        blur: 5,
                      }}
                      opened={isSearchModalOpened}
                      onClose={closeSearchModal}
                      radius={"md"}
                      bg={"transparent"}
                      styles={{
                        content: {
                          background: "transparent",
                          boxShadow: "none",
                        },
                        header: { background: "transparent" },
                        close: {
                          background: "black",
                          color: "white",
                          borderRadius: "100px",
                        },
                      }}
                    >
                      <Box w={"100%"} mih={"500px"}>
                        <SearchBox focus={isSearchModalOpened} />
                      </Box>
                    </Modal>
                    <Modal
                      title=""
                      size={"md"}
                      overlayProps={{
                        opacity: 1,
                        blur: 5,
                      }}
                      opened={isLoginModalOpened}
                      onClose={closeLoginModal}
                      radius={"md"}
                      bg={"transparent"}
                      styles={{
                        content: {
                          background: "transparent",
                          boxShadow: "none",
                        },
                        header: { background: "transparent" },
                        close: {
                          background: "black",
                          color: "white",
                          borderRadius: "100px",
                        },
                      }}
                    >
                      <SigninContainer returnUrl={router.asPath} />
                    </Modal>
                    <ScrollToTop />
                  </AppShell.Main>
                </AppShell>
              </AuthProvider>
            </QueryClientProvider>
          </Box>
        </RootStyleRegistry>
      </HydrationSafeSuspense>
    </>
  );
};
export default appWithTranslation(App, NextI18nextConfig);
