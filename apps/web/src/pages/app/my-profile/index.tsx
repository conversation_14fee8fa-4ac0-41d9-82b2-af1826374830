import {
  Box,
  Button,
  Center,
  Container,
  Group,
  Image,
  Paper,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import React, { useEffect } from "react";
import { z } from "zod";

import { loadTranslation } from "@/i18n";
import { ERR_GENERIC_REQUIRED } from "@/consts";
import { useTranslation } from "react-i18next";
import { useProfile } from "@/store";
import { notifications } from "@mantine/notifications";
import AppTextInput from "@/components/AppTextInput";
import { ApiService } from "../../../../api";
import Head from "next/head";

export const getServerSideProps = async (context: any) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const schema = z.object({
  firstName: z.string().min(2, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(2, ERR_GENERIC_REQUIRED),
});
const AccountProfile = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isProfileImageDeleting, setIsProfileImageDeleting] =
    React.useState(false);

  const [profile, setProfile] = useProfile((p) => [p.profile, p.setPorfile]);
  const form = useForm({
    initialValues: {
      firstName: profile?.firstName || "",
      lastName: profile?.lastName || "",
      email: profile?.email || "",
      isProfileImageDeleting: false,
      profileImage: profile?.profileImage || "",
    },
    validate: zodResolver(schema),
  });
  useEffect(() => {
    form.setValues({
      firstName: profile?.firstName || "",
      lastName: profile?.lastName || "",
      email: profile?.email || "",
      profileImage: profile?.profileImage || "",
      isProfileImageDeleting: false,
    });
  }, [profile]);
  const handleProfileUpdate = React.useCallback(
    async (values: typeof form.values) => {
      try {
        const response = await ApiService.updateProfile({
          firstName: values.firstName as string,
          lastName: values.lastName as string,
          isProfileImageDeleting: isProfileImageDeleting ? "true" : "false",
        });
        form.setFieldValue("profileImage", "");
        setProfile(response.data.data);
        notifications.show({
          message: "Profile has been updated",
          color: "green",
        });
      } catch (err: any) {
        notifications.show({
          message: "Unable to update profile",
          color: "red",
        });
      } finally {
      }
    },
    [setProfile, isProfileImageDeleting]
  );
  return (
    <Container>
      <Head>
        <title>My Profile | NepalTracks</title>
      </Head>
      <Center>
        <Stack>
          <Paper radius={"md"}>
            <form onSubmit={form.onSubmit(handleProfileUpdate)}>
              <Stack
                w={{
                  base: "20rem",
                  md: "30rem",
                  xs: "20rem",
                  sm: "20rem",
                }}
              >
                <AppTextInput
                  label={t("common.firstname", { defaultValue: "First Name" })}
                  withAsterisk
                  placeholder={
                    t("common.firstname", {
                      defaultValue: "First Name",
                    }) as string
                  }
                  {...form.getInputProps("firstName")}
                />
                <AppTextInput
                  label={t("common.lastname", { defaultValue: "Last Name" })}
                  withAsterisk
                  placeholder={
                    t("common.lastname", {
                      defaultValue: "Last Name",
                    }) as string
                  }
                  {...form.getInputProps("lastName")}
                />
                {form.values?.profileImage && (
                  <Stack>
                    <Image
                      src={profile?.profileImage}
                      alt="Profile Image"
                      className={`${
                        isProfileImageDeleting ? "opacity-30" : ""
                      }`}
                      width={100}
                      height={100}
                      radius="md"
                    />
                    <Box>
                      {isProfileImageDeleting ? (
                        <Text size="xs" c="red">
                          *Once deleted, it can't be undone.
                        </Text>
                      ) : null}

                      <Button
                        onClick={() =>
                          setIsProfileImageDeleting(!isProfileImageDeleting)
                        }
                      >
                        {isProfileImageDeleting
                          ? "Undo"
                          : "Remove Profile Image"}
                      </Button>
                    </Box>
                  </Stack>
                )}

                <AppTextInput
                  disabled
                  label={t("common:email", { defaultValue: "Email" })}
                  withAsterisk
                  placeholder={
                    t("common:email", { defaultValue: "Email" }) as string
                  }
                  {...form.getInputProps("email")}
                />
                <Button w={"100%"} type="submit" m="0.625rem 0 1.25rem">
                  {t("mypage:name-update.btn.txt", { defaultValue: "Update" })}
                </Button>
              </Stack>
            </form>
          </Paper>
        </Stack>
      </Center>
    </Container>
  );
};
export default AccountProfile;
