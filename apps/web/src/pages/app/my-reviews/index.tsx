import { useTranslation } from "next-i18next";
import { ApiService } from "../../../../api";
import { ActionIcon, Group, Stack, Text } from "@mantine/core";
import { useProfile } from "@/store";
import { useQuery } from "react-query";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IReview } from "@/interfaces/IReview";
import { Comment } from "@/components/Comment";
import { formatDistanceToNow } from "date-fns";
import RenderResourceType from "@/containers/RenderResourceType";
import { IconTrash } from "@tabler/icons-react";
import { useCallback, useState } from "react";
import { startCase, toLower } from "lodash";
import DynamicLinkBuilderForResource from "@/components/DynamicLinkBuilderForResource";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

const MyReviews = () => {
  const profile = useProfile((s) => s.profile);
  const [deletedItems, setDeletedItems] = useState<number[]>([]);

  const handleDelete = useCallback(async (id: number, ratingOnType: string) => {
    try {
      if (
        !confirm(t("common:delete_alert", "Are your sure to delete this item?"))
      )
        return;
      const response = await ApiService.removeReview(ratingOnType, id);
      setDeletedItems((ids) => [...ids, id]);
    } catch (err) {
      alert(
        t(
          "common:delete_error",
          "We are not able to delete this item, please try again."
        )
      );
    }
  }, []);

  const { t } = useTranslation();

  return (
    <DefaultIndexPage
      hideChips
      hideSearch
      pageTitle={t("common:my_reviews")}
      entityType={EntityTypeEnum.ALL}
      resource={"users/my-reviews"}
      skeletonType="list"
      auth={true}
      renderItems={(items) => {
        return (
          <Stack w={"100%"}>
            {items.map((rating: IReview, index: number) =>
              deletedItems.includes(rating.id) ? null : (
                <Stack w={"100%"} key={index}>
                  <Group justify="space-between" wrap="nowrap">
                    <DynamicLinkBuilderForResource
                      resourceType={rating.rateOnType}
                      data={rating.resource}
                    >
                      <Text>
                        [{" "}
                        {t(
                          `common:${toLower(rating.rateOnType)}`,
                          toLower(rating.rateOnType)
                        )}
                        ]{" "}
                        {t("common:review_message", {
                          defaultValue: "Review by",
                          user:
                            rating.user.firstName + " " + rating.user.lastName,
                          item:
                            rating.resource?.localName ||
                            rating.resource?.name ||
                            rating.resource?.title,
                          value: rating.value,
                        })}
                      </Text>
                    </DynamicLinkBuilderForResource>

                    <ActionIcon
                      variant="transparent"
                      size={"sm"}
                      c={"red"}
                      onClick={() => handleDelete(rating.id, rating.rateOnType)}
                    >
                      <IconTrash />
                    </ActionIcon>
                  </Group>
                  <Comment
                    hash={rating.hash}
                    rating={rating.value}
                    postedAt={formatDistanceToNow(new Date(rating.createdAt))}
                    body={rating.comment}
                    author={{
                      name: `${rating.user.firstName} ${rating.user.lastName}`,
                      image: rating.user.profileImage,
                    }}
                  />
                </Stack>
              )
            )}
          </Stack>
        );
      }}
    />
  );
};
export default MyReviews;
