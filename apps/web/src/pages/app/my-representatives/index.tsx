import { useTranslation } from "next-i18next";
import { useEffect, useState } from "react";
import { ApiService } from "../../../../api";
import { Alert, Center, Paper, Stack } from "@mantine/core";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { ILeader } from "@/interfaces/ILeader";
import { IParty } from "@/interfaces/IParty";
import {
  ICandidacyType,
  IDistricts,
  IMunicipals,
  IStates,
  IWard,
} from "@/interfaces/IElectionSubResponse";
import { getImageUrlWithFallback } from "@/utils";
import Link from "next/link";
import { useProfile } from "@/store";
import PartyBadge from "@/components/PartyBadge";
import { PartyResults } from "@/components/PartyResults";
import { UserInfoAction } from "@/components/UserInfoAction";
import MyAddressForm from "@/containers/MyAddressForm";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import Head from "next/head";

export type UserAnalyticsType = {
  candidacyTypeId: number;
  elCode: string;
  electionId: number;
  area: string;
  leaders: ILeader;
  parties: IParty;
  districts: IDistricts;
  states: IStates;
  municipals: IMunicipals;
  ward: IWard;
  candidacyType?: ICandidacyType;
};
export interface IUserAnalytics {
  wardRepresnetatives: UserAnalyticsType[];
  provincialRepresentatives: UserAnalyticsType[];
  centreRepresentatives: UserAnalyticsType[];
  municipalRepresentatives: UserAnalyticsType[];
}

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

const MyRepresentatives = () => {
  const [isLoading, setIsLoading] = useState(false);
  const profile = useProfile((s) => s.profile);
  const [userAnalytics, setUserAnalytics] = useState<IUserAnalytics | null>(
    null
  );

  useEffect(() => {
    const fetchUserAnalytics = async () => {
      setIsLoading(true);
      const { data } = await ApiService.getAuthAnalytics("users")
        .catch(() => ({
          data: {},
        }))
        .finally(() => {
          setIsLoading(false);
        });
      setUserAnalytics(data?.data);
    };

    fetchUserAnalytics();
  }, []);
  const { t } = useTranslation();
  return (
    <Stack justify="center">
      <Head>
        <title>My Representatives | NepalTracks</title>
      </Head>
      {!profile?.user_address?.length ? (
        <Alert variant="red">
          You can also see your area`s representatives, make sure you update
          your address here,{" "}
          <Link href={"/app/account?tab=address"}>
            {t("common:click_here_to_update")}
          </Link>
        </Alert>
      ) : (
        <>
          <Stack bg="#f2f4f7" p={"md"}>
            <FeaturedSlider<UserAnalyticsType>
              isLoading={isLoading}
              title={`${userAnalytics?.wardRepresnetatives[0]?.municipals?.localName}, Ward ${userAnalytics?.wardRepresnetatives[0]?.ward?.id}`}
              data={userAnalytics?.wardRepresnetatives || []}
              renderItems={(item) => {
                return (
                  <UserInfoAction
                    description={
                      <Stack>
                        <Link
                          className="text-inherit "
                          href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                        >
                          {item.candidacyType?.localName}
                        </Link>
                        <PartyBadge
                          partyId={item.parties.id}
                          partyImage={item.parties.logo}
                          partyName={item.parties.localName}
                        />
                      </Stack>
                    }
                    img={getImageUrlWithFallback(
                      item.leaders.img,
                      item.leaders.ecCandidateID
                    )}
                    name={
                      <Link
                        href={`/leaders/${item.leaders.id}`}
                        className="no-underline text-inherit"
                      >
                        {item.leaders?.localName}
                      </Link>
                    }
                    key={item.leaders.id}
                  />
                );
              }}
            />
            {/* @ts-expect-error */}
            <PartyResults data={userAnalytics?.wardRepresnetatives || []} />
          </Stack>

          <Stack bg="#f2f4f7" p={"md"}>
            <FeaturedSlider<UserAnalyticsType>
              isLoading={isLoading}
              title={`${userAnalytics?.municipalRepresentatives[0]?.municipals?.localName}`}
              data={userAnalytics?.municipalRepresentatives || []}
              renderItems={(item) => {
                return (
                  <UserInfoAction
                    description={
                      <Stack>
                        <Link
                          className="text-inherit "
                          href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                        >
                          {item.candidacyType?.localName}
                        </Link>
                        <PartyBadge
                          partyId={item.parties.id}
                          partyImage={item.parties.logo}
                          partyName={item.parties.localName}
                        />
                      </Stack>
                    }
                    img={getImageUrlWithFallback(
                      item.leaders.img,
                      item.leaders.ecCandidateID
                    )}
                    name={
                      <Link
                        className="no-underline text-inherit"
                        href={`/leaders/${item.leaders.id}`}
                      >
                        {item.leaders?.localName}
                      </Link>
                    }
                    key={item.leaders.id}
                  />
                );
              }}
            />
            <PartyResults
              // @ts-expect-error
              data={userAnalytics?.municipalRepresentatives || []}
            />
          </Stack>

          <Stack bg="#f2f4f7" p={"md"}>
            <FeaturedSlider<UserAnalyticsType>
              isLoading={isLoading}
              title={`Representatives from ${userAnalytics?.provincialRepresentatives[0]?.states?.localName} in Province`}
              data={userAnalytics?.provincialRepresentatives || []}
              renderItems={(item) => {
                return (
                  <UserInfoAction
                    description={
                      <Stack>
                        <Link
                          className="text-inherit "
                          href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                        >
                          {item.candidacyType?.localName}
                          <br />
                          {item.districts?.localName} - {item.area}
                        </Link>
                        <PartyBadge
                          partyId={item.parties.id}
                          partyImage={item.parties.logo}
                          partyName={item.parties.localName}
                        />
                      </Stack>
                    }
                    img={getImageUrlWithFallback(
                      item.leaders.img,
                      item.leaders.ecCandidateID
                    )}
                    name={
                      <Link
                        className="no-underline text-inherit"
                        href={`/leaders/${item.leaders.id}`}
                      >
                        {item.leaders?.localName}
                      </Link>
                    }
                    key={item.leaders.id}
                  />
                );
              }}
            />
            <PartyResults
              // @ts-expect-error
              data={userAnalytics?.provincialRepresentatives || []}
            />
          </Stack>

          <Stack bg="#f2f4f7" p={"md"}>
            <FeaturedSlider<UserAnalyticsType>
              isLoading={isLoading}
              title={`Representatives from ${userAnalytics?.centreRepresentatives[0]?.districts?.localName} in Federal`}
              data={userAnalytics?.centreRepresentatives || []}
              renderItems={(item) => {
                return (
                  <UserInfoAction
                    description={
                      <Stack>
                        <Link
                          className="text-inherit "
                          href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                        >
                          {item.candidacyType?.localName}
                          <br />
                          {item.districts?.localName} - {item.area}
                        </Link>
                        <PartyBadge
                          partyId={item.parties.id}
                          partyImage={item.parties.logo}
                          partyName={item.parties.localName}
                        />
                      </Stack>
                    }
                    img={getImageUrlWithFallback(
                      item.leaders.img,
                      item.leaders.ecCandidateID
                    )}
                    name={
                      <Link
                        className="no-underline text-inherit"
                        href={`/leaders/${item.leaders.id}`}
                      >
                        {item.leaders?.localName}
                      </Link>
                    }
                    key={item.leaders.id}
                  />
                );
              }}
            />
            <PartyResults
              //  @ts-expect-error
              data={userAnalytics?.centreRepresentatives || []}
            />{" "}
          </Stack>
        </>
      )}
    </Stack>
  );
};
export default MyRepresentatives;
