import { Container } from "@mantine/core";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import Head from "next/head";
import SigninContainer from "@/containers/SigninContainer";
import { useTranslation } from "next-i18next";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export default function AuthenticationTitle() {
  const { t } = useTranslation();
  return (
    <Container size={420} my={40}>
      <Head>
        <title>
          {" "}
          {t("common:login", { defaultValue: "Login" })} | NepalTracks
        </title>
      </Head>
      <SigninContainer />
    </Container>
  );
}
