"use client";
import Head from "next/head";
import { useEffect } from "react";
import { LoadingOverlay } from "@mantine/core";
import Storage from "@/core/Storage";
import { AUTH_TOKEN_NAME } from "@/consts";
import { useRouter } from "next/router";

const SocialLoginCallback = () => {
  const router = useRouter();
  const code = router.query.code || router.query.oauth_token;
  const token = window.location.hash.replace("#token=", "");

  useEffect(() => {
    if (token) {
      Storage.getInstance().set(AUTH_TOKEN_NAME, {
        //@ts-expect-error
        accessToken: token,
      });
      window.location.href = Storage.getInstance().get("returnUrl") || "/";
      Storage.getInstance().set("returnUrl", "");
      return;
    }

    if (!code) {
      window.location.href = Storage.getInstance().get("returnUrl") || "/";
      return;
    }
    const time = setTimeout(() => {
      let query = `code=${code}&service=${router.query.service}&oauth_token=${router.query.oauth_token}&oauth_verifier=${router.query.oauth_verifier}`;
      fetch(
        `${process.env.NEXT_PUBLIC_API_HOST}/api/v1/auth/${router.query.service}/redirect?${query}`
      )
        .then((response) => response.json())
        .then(async ({ data, ...rest }: any) => {
          try {
            if (data?.access_token) {
              Storage.getInstance().set(AUTH_TOKEN_NAME, {
                //@ts-expect-error
                accessToken: data.access_token,
              });

              window.location.href =
                Storage.getInstance().get("returnUrl") || "/";
              Storage.getInstance().set("returnUrl", "");
            } else {
              //@ts-ignore
              if (rest.statusCode === 400) {
                router.replace("/auth/signin");
              }
              alert("Something went wrong, please try again!");
              router.replace("/auth/signin");
            }
          } catch (error) {
            alert("Unexpected error, please try again!");
            router.replace("/auth/signin");
          }
        })
        .catch(() => {
          alert("Unexpected error, please try again!");
          router.replace("/auth/signin");
        });
    }, 1500);
    return () => clearTimeout(time);
  }, [token]);

  return (
    <>
      <Head>
        <meta name="robots" content="noindex" />
      </Head>
      <div>
        <LoadingOverlay visible />
      </div>
    </>
  );
};
SocialLoginCallback.getInitialProps = async () => {
  return {};
};
export default SocialLoginCallback;
