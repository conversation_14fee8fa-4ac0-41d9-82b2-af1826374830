import AppTextInput from "@/components/AppTextInput";
import {
  ERR_GENERIC_REQUIRED,
  ERR_STRONG_PASSWORD,
  REGEX_STRONG_PASSWORD,
} from "@/consts";
import { useGlobalState, useRegisterUser } from "@/store";
import {
  Checkbox,
  Anchor,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Button,
  Stack,
  Divider,
} from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { z } from "zod";
import { ApiService } from "../../../../api";
import { useTurnstileToken } from "@/hooks/useTurnsiteToken";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import Head from "next/head";
import { SocialSignin } from "@/components/SocialSign";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import { useTranslation } from "next-i18next";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export const signupSchema = z.object({
  firstName: z.string().min(2, ERR_GENERIC_REQUIRED),
  lastName: z.string().min(2, ERR_GENERIC_REQUIRED),
  email: z.string().email(),

  password: z.string().regex(REGEX_STRONG_PASSWORD, ERR_STRONG_PASSWORD),
  termsOfService: z.boolean({
    invalid_type_error: "Please agree to the terms and conditions",
    required_error: "Please agree to the terms and conditions",
  }),
});
export default function AuthenticationTitle() {
  const { closeLoginModal } = useLeaderHover();
  const setRegisterEmail = useRegisterUser((s) => s.setEmail);
  const token = useTurnstileToken(true);

  const [setGlobalMessage, setGlobalLoading] = useGlobalState((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);
  const router = useRouter();
  const { t } = useTranslation();

  const form = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      termsOfService: true,
    },
    validate: zodResolver(signupSchema),
  });
  const handleSubmit = React.useCallback(
    async (values: { email: string }) => {
      try {
        setGlobalLoading();
        await ApiService.register({ ...values, turnstileToken: token });
        router.push("/verify/process");
        setRegisterEmail(values.email);
      } catch (err: any) {
        setGlobalMessage(err.response.data.message);
      } finally {
        setGlobalLoading();
      }
    },
    [token]
  );

  useEffect(() => {
    closeLoginModal();
  }, []);

  return (
    <Container size={420} my={40}>
      <Head>
        <title>
          {" "}
          {t("common:create_account", { defaultValue: "Create an account" })} |
          NepalTracks
        </title>
      </Head>
      <Title ta="center">
        {t("common:create_account", { defaultValue: "Create an account" })}
      </Title>
      <Text color="dimmed" size="sm" ta="center" mt={5}>
        {t("common:already_have_account", {
          defaultValue: "Already have an account?",
        })}{" "}
        <Link href={"/auth/signin"} className="no-underline">
          <Anchor size="sm" component="button">
            {t("common:login", { defaultValue: "Login" })}
          </Anchor>
        </Link>
      </Text>
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Paper withBorder shadow="md" p={30} mt={30} radius="md">
          <Stack>
            <SocialSignin />
            <Divider
              label={t("common:or_continue_with_email", {
                defaultValue: "Or continue with email",
              })}
              labelPosition="center"
            />
            <AppTextInput
              withAsterisk
              placeholder={t("common:first_name", {
                defaultValue: "First name",
              })}
              {...form.getInputProps("firstName")}
            />
            <AppTextInput
              withAsterisk
              placeholder={t("common:last_name", { defaultValue: "Last name" })}
              {...form.getInputProps("lastName")}
            />
            <AppTextInput
              withAsterisk
              placeholder={t("common:email", { defaultValue: "Email" })}
              {...form.getInputProps("email")}
            />
            <AppTextInput
              withAsterisk
              placeholder={t("common:password", { defaultValue: "Password" })}
              type="password"
              {...form.getInputProps("password")}
            />
          </Stack>

          <Group justify="apart" mt="lg">
            <Checkbox
              checked
              disabled
              label={t(
                "common:by_signing_up_you_agree_to_our_terms_and_conditions",
                {
                  defaultValue:
                    "By signing up, you agree to our terms and conditions",
                }
              )}
            />
            {/* <Anchor component="button" size="sm">
              Forgot password?
            </Anchor> */}
          </Group>
          <div
            id="cf-container"
            className="cf-turnstile"
            data-sitekey={process.env.NEXT_PUBLIC_CF_TURNSTILE_SITE_KEY}
            data-callback="javascriptCallback"
          ></div>

          <Button type="submit" fullWidth mt="xl">
            {t("common:create_account", { defaultValue: "Create account" })}
          </Button>
        </Paper>
      </form>
    </Container>
  );
}
