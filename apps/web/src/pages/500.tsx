import { Button, Container, Group, Text, Title } from "@mantine/core";
import classes from "./servererror.module.css";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";

export const getStaticProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export default function ServerError() {
  return (
    <div className={classes.root}>
      <Container>
        <div className={classes.label}>500</div>
        <Title className={classes.title}>Something bad just happened...</Title>
        <Text size="lg" ta="center" className={classes.description}>
          Our servers could not handle your request. Don&apos;t worry, our
          development team was already notified. Try refreshing the page.
        </Text>
      </Container>
    </div>
  );
}
