// import { theme } from "@/theme/theme";
import { theme } from "@/theme";
import {
  MantineProvider,
  createTheme,
  localStorageColorSchemeManager,
} from "@mantine/core";
import { Notifications } from "@mantine/notifications";
import { useServerInsertedHTML } from "next/navigation";

const colorSchemeManager = localStorageColorSchemeManager({
  key: "my-app-color-scheme",
});

export default function RootStyleRegistry({
  children,
}: {
  children: React.ReactNode;
}) {
  useServerInsertedHTML(() => <style />);

  return (
    <MantineProvider colorSchemeManager={colorSchemeManager} theme={theme}>
      {children}
    </MantineProvider>
  );
}
