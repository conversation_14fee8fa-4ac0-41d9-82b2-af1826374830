import { GetStaticProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Head from "next/head";
import { Container, Title, Text } from "@mantine/core";
import PollsContainer from "@/containers/PollsContainer";

export default function PollsPage() {
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>{t("common:polls")} - Rajniti Report</title>
        <meta
          name="description"
          content={t("common:polls_page_description")}
        />
      </Head>

      <Container size="xl" py="xl">
        <div className="mb-8">
          <Title order={1} className="text-3xl font-bold mb-4">
            {t("common:all_polls")}
          </Title>
          <Text size="lg" c="dimmed">
            {t("common:polls_page_subtitle")}
          </Text>
        </div>

        <PollsContainer />
      </Container>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? "en", ["common"])),
    },
  };
};
