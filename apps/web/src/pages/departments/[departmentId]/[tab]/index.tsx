import { useTranslation } from "next-i18next";
import { BackgroundImage, Badge, Box, Flex, Stack, Table } from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { EntityInfo } from "@/components/EntityInfo";
import DisqusComment from "@/components/DisqusComment";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getContentMenus } from "@/containers/utils";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { useProfile } from "@/store";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import { useMemo } from "react";
import { IDepartment } from "@/interfaces/IDepartment";
import SummaryMD from "@/components/SummaryMD";
import { cleanUrl, formatDate, getImageUrlWithFallback } from "@/utils";
import SocialLinks from "@/components/SocialLinks";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import CabinetMemberProfile from "@/components/CabinerMemberProfile";
import { EntityTable } from "@/components/EntityTable";
import { Avatar, Group, Text } from "@mantine/core";
import { startCase } from "lodash";
import Link from "next/link";
import TopContentLeader from "@/components/TopContentLeader";
import { ILeader } from "@/interfaces/ILeader";
import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import { isPast } from "date-fns";

//@ts-expect-error
export const getServerSideProps: GetServerSideProps<{
  data?: IDepartment;
}> = async (context) => {
  const departmentId = context?.params?.departmentId;
  const translation = await loadTranslation(context.locale!, "common");
  if (!departmentId)
    return {
      props: {
        data: null,
      },
    };
  const response = await ApiService.resource.getById<any>(
    "departments",
    departmentId + ""
  );

  return {
    props: {
      data: response?.data,
      rating: response?.data?.rating,
      ...translation,
    },
  };
};

const DepartmentDetail = (props: {
  data: IDepartment & { rating: IReviewAnalysis };
}) => {
  const router = useRouter();
  const { departmentId } = router.query;
  const profile = useProfile();
  useDynamicEntityStorage("departments", departmentId + "");
  const { t, i18n } = useTranslation();

  const metadata = useMemo(() => {
    return (
      (typeof props?.data?.metadata === "string"
        ? JSON.parse(props?.data?.metadata)
        : props?.data?.metadata) || {}
    );
  }, [props.data.metadata]);

  const cabinetMinistersMemo = useMemo(() => {
    return props.data?.cabinet_members
      ?.filter((item) => item.role !== "STATE_MINISTER")
      .sort((a, b) => {
        const aPast = isPast(new Date(a.startedAt)) ? 1 : 0;
        const bPast = isPast(new Date(b.startedAt)) ? 1 : 0;

        if (bPast !== aPast) {
          return bPast - aPast; // Past first
        } else {
          // Both past or both future, sort descending by startedAt date
          // @ts-expect-error
          return new Date(b.startedAt) - new Date(a.startedAt);
        }
      });
  }, [props.data.cabinet_members]);

  return (
    <>
      <DynamicSEO data={props.data} type="departments" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:departments"),
            href: "/departments",
          },
          {
            label: props.data?.name,
            href: `/departments/${departmentId}`,
          },
        ]}
      />{" "}
      <Flex w={"100%"} direction="column" gap={10} pos={"relative"}>
        <Flex direction={"column"} w={"100%"} gap={10}>
          <EntityInfo
            name={props?.data.name || props?.data?.localName}
            rate={+props?.data?.rating?.average}
            avatar={
              props?.data?.logo ||
              "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
            }
            id={departmentId as string}
            partyId={departmentId as string}
            address={cleanUrl(props?.data.website || "")}
            renderRightSection={() => {
              return (
                <SocialLinks
                  links={{
                    facebook: props.data.fbPage || "",
                    twitter: props.data.twitterPage || "",
                    youtube: props.data.youtubePage || "",
                  }}
                />
              );
            }}
          />
          <SegmentedTab
            resources="departments"
            entityId={departmentId as string}
            defaultValue="departments"
            data={[
              {
                label: "Home",
                value: "home",
                content: (
                  <Stack>
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                      readMoreLink={`/departments/${departmentId}/summary`}
                    />
                    <ContentStatsCards stats={props?.data?.stats || {}} />
                    <FeaturedSlider
                      fullWidthOnSP
                      title={t("common:ministers", {
                        defaultValue: "Ministers",
                      })}
                      data={cabinetMinistersMemo}
                      renderItems={(item) => {
                        return (
                          <CabinetMemberProfile
                            variant="compact"
                            disableRanking
                            data={{ ...item, department: props.data }}
                          />
                        );
                      }}
                    />
                    <AsyncFeatureSlider
                      transformResponse={(data) => data || []}
                      entityId={departmentId as string}
                      title={t("common:most_repeated_ministers", {
                        defaultValue: "Most Repeated Ministers",
                      })}
                      entityType="departments"
                      resourceUrl={`departments/${departmentId}/leaders/most-repeated`}
                      renderItems={(item: ILeader) => {
                        return (
                          <LeaderHeroProfile
                            data={item}
                            renderBeforeAvatar={() =>
                              //@ts-expect-error
                              +item.appointment_count > 1 ? (
                                <Badge radius={"sm"} variant={"filled"}>
                                  {/* @ts-ignore */}
                                  {item.appointment_count} time(s)
                                </Badge>
                              ) : null
                            }
                          />
                        );
                      }}
                    />
                    <TopContentLeader
                      resourceUrl={`departments/${departmentId}/leaders/top?contentType=${[
                        "SCANDAL",
                        "CONTROVERSIES",
                      ].join(",")}&limit=10&page=1&context=${
                        EntityTypeEnum.Department
                      }`}
                      contentType={["SCANDAL", "CONTROVERSIES"]}
                      context={EntityTypeEnum.Department}
                    />
                    <TopContentLeader
                      resourceUrl={`departments/${departmentId}/leaders/top?contentType=${[
                        "ACHIEVEMENTS",
                        "MILESTONES",
                      ].join(",")}&limit=10&page=1&context=${
                        EntityTypeEnum.Department
                      }`}
                      contentType={["ACHIEVEMENTS", "MILESTONES"]}
                      context={EntityTypeEnum.Department}
                    />
                    <Recommendations
                      disableSeeMore
                      entityType="departments"
                      entityId={departmentId + ""}
                    />
                    <RecentlySelfViewed
                      lists={["departments"]}
                      entityId={departmentId as string}
                    />
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings
                    entityId={departmentId as string}
                    entityType={"DEPARTMENT"}
                  />
                ),
              },
              {
                label: "summary",
                value: "summary",
                content: (
                  <SummaryMD
                    summary={props.data?.summary || ""}
                    summaryNP={props.data?.summaryNP || ""}
                  />
                ),
              },

              {
                label: "ministers",
                value: "ministers",
                content: (
                  <>
                    <Table style={{ minWidth: 800 }} verticalSpacing="sm">
                      <Table.Thead>
                        <Table.Tr>
                          <Table.Th>{t("common:leader")}</Table.Th>
                          <Table.Th>{t("common:position")}</Table.Th>
                          <Table.Th>{t("common:party")}</Table.Th>
                          <Table.Th>{t("common:started_at")}</Table.Th>
                          <Table.Th>{t("common:end_at", "Ended at")}</Table.Th>
                        </Table.Tr>
                      </Table.Thead>
                      <Table.Tbody p={"md"}>
                        {cabinetMinistersMemo?.map?.(
                          ({ leaders, department, ...cabinet_member }) => (
                            <Table.Tr>
                              <Table.Td>
                                <Link
                                  className="no-underline text-inherit"
                                  href={`/leaders/${leaders.id}`}
                                >
                                  <Group gap="sm" wrap="nowrap">
                                    <Avatar
                                      size={"md"}
                                      src={getImageUrlWithFallback(
                                        leaders.img,
                                        leaders.ecCandidateID
                                      )}
                                    />
                                    <div>
                                      <Text fz="sm" fw={500}>
                                        {leaders.localName}
                                      </Text>
                                    </div>
                                  </Group>
                                </Link>
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {startCase(cabinet_member.role || "")}
                                </Text>
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {cabinet_member.party?.localName}
                                </Text>
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {cabinet_member.startedAt
                                    ? formatDate(
                                        new Date(cabinet_member.startedAt)
                                      )
                                    : ""}
                                </Text>
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {cabinet_member.endAt
                                    ? formatDate(cabinet_member.endAt)
                                    : ""}
                                </Text>
                              </Table.Td>
                            </Table.Tr>
                          )
                        )}
                      </Table.Tbody>
                    </Table>
                  </>
                ),
              },
              ...getContentMenus(
                departmentId as string,
                EntityTypeEnum.Department,
                props.data.stats
              ),

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"department" + departmentId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default DepartmentDetail;
