import { useTranslation } from "next-i18next";
import { Grid } from "@mantine/core";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import Link from "next/link";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { IDepartment } from "@/interfaces/IDepartment";
import { EntityInfo } from "@/components/EntityInfo";
import { cleanUrl } from "@/utils";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const departments = () => {
  const { t } = useTranslation();

  return (
    <>
      <DefaultIndexPage
        enableUrlChangeOnPageChange
        pageTitle={t("common:departments", { defaultValue: "Departments" })}
        entityType={EntityTypeEnum.Department}
        resource={"departments"}
        skeletonType="card"
        renderItems={(items: IDepartment[]) => {
          return (
            <>
              {items.map((department) => (
                <Grid.Col
                  key={department.id}
                  span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                >
                  <EntityInfo
                    titleTextAlign="start"
                    avatar={
                      department.logo ||
                      "https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
                    }
                    linkComponent={Link}
                    resources={"departments"}
                    partyId={department.id + ""}
                    id={department.id + ""}
                    name={department.name}
                    rate={department.rating?.average}
                    address={cleanUrl(department.website || "")}
                  />
                </Grid.Col>
              ))}
            </>
          );
        }}
      />
    </>
  );
};
export default departments;
