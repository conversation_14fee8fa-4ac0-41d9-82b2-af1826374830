import { ILeader } from "@/interfaces/ILeader";
import {
  Box,
  Flex,
  Stack,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { IParty } from "@/interfaces/IParty";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityInfo } from "@/components/EntityInfo";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { EntityTable } from "@/components/EntityTable";
import ElectionInTab from "@/containers/ElectionInTab";
import { getContentMenus } from "@/containers/utils";
import DisqusComment from "@/components/DisqusComment";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import SummaryMD from "@/components/SummaryMD";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import ElectionMostVoted from "@/containers/ElectionMostVoted";
import { IElections } from "@/interfaces/IElectionSubResponse";
import { loadTranslation } from "@/i18n";
import { useTranslation } from "react-i18next";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import ElectionRegionalReportAsync from "@/containers/ElectionRegionalReportAsync";
import { FaArrowDown, FaArrowUp } from "react-icons/fa";
import TopContentLeader from "@/components/TopContentLeader";
import { truncateString } from "@/utils";
import ProjectProfile from "@/components/ProjectProfile";
import SocialLinks from "@/components/SocialLinks";
import DistrictCard from "@/components/DistrictCard";

//@ts-expect-error
export const getServerSideProps: GetServerSideProps<{
  data?: IParty;
}> = async (context) => {
  const partyId = context?.params?.partyId;
  const translation = await loadTranslation(context.locale!, "common");
  if (!partyId)
    return {
      props: {
        data: null,
      },
    };
  const response = await ApiService.resource.getById<any>(
    "parties",
    partyId + ""
  );

  const { data: elections } = await ApiService.resource.getAll("elections", {});

  const { data: leaderAnalytics } = await ApiService.getAnalytics("leaders", {
    partyId: +partyId,
  });

  return {
    props: {
      data: response?.data,
      leaderAnalytics: leaderAnalytics.data,
      rating: response?.data?.rating,
      elections: elections,
      ...translation,
    },
  };
};
const PartyDetail = (props: {
  data: IParty;
  rating: IReviewAnalysis;
  elections: { items: IElections[] };
  leaderAnalytics: {
    mostLiked: { items: ILeader[] };
    most_viewed: { items: ILeader[] };
    recentlyAdded: { items: ILeader[] };
  };
}) => {
  const router = useRouter();
  const { t } = useTranslation();

  const { partyId } = router.query;
  useDynamicEntityStorage("parties", partyId + "");
  const { colorScheme } = useMantineColorScheme();
  const theme = useMantineTheme();

  return (
    <Box>
      <DynamicSEO data={props.data} type="parties" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:parties"),
            href: "/parties",
          },
          {
            label: truncateString(props.data?.localName, 40),
            href: `/parties/${partyId}`,
          },
        ]}
      />

      <Flex w={"100%"} direction="column" gap={10} pos={"relative"}>
        {/* {props.data.coverImage?.trim()?.length && (
          <Box w={"100%"} h={{ base: "10rem", md: "15rem" }}>
            <BackgroundImage
              src={props.data.coverImage}
              h={{ base: "10rem", md: "15rem" }}
              bgp={"fill"}
            />
          </Box>
        )} */}
        <Flex direction={"column"} w={"100%"} gap={10}>
          <EntityInfo
            resources="parties"
            name={props?.data.localName}
            rate={+props?.rating?.average}
            avatar={props?.data?.logo}
            id={partyId as string}
            partyId={partyId as string}
            renderRightSection={() => {
              return (
                <SocialLinks
                  links={{
                    facebook: props.data.fbPage || "",
                    twitter: props.data.twitterPage || "",
                    youtube: props.data.youtubePage || "",
                  }}
                />
              );
            }}
          />
          <SegmentedTab
            resources="parties"
            entityId={partyId as string}
            defaultValue="parties"
            data={[
              {
                label: "overview",
                value: "home",
                content: (
                  <Stack>
                    {/* @ts-expect-error */}
                    <ContentStatsCards stats={props?.data?.stats || {}} />
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                      readMoreLink={`/parties/${partyId}/summary`}
                    />{" "}
                    <FeaturedSlider<ILeader>
                      title={
                        t("common:most_liked_leaders", {
                          defaultValue: "Most liked leaders",
                        }) + ` ${props?.data.localName}`
                      }
                      data={props.leaderAnalytics.mostLiked?.items}
                      renderItems={(item) => {
                        return (
                          <LeaderHeroProfile
                            additionalInformationEnabled
                            data={item}
                          />
                        );
                      }}
                    />{" "}
                    <FeaturedSlider<ILeader>
                      title={t("common:popular_leaders", {
                        defaultValue: "Popular leaders",
                      })}
                      data={props.leaderAnalytics.most_viewed?.items}
                      renderItems={(item) => {
                        return (
                          <LeaderHeroProfile
                            additionalInformationEnabled
                            data={item}
                          />
                        );
                      }}
                    />
                    <TopContentLeader
                      contentType={["SCANDAL", "CONTROVERSIES"]}
                      partyId={partyId + ""}
                    />
                    <TopContentLeader
                      contentType={["ACHIEVEMENTS", "MILESTONES"]}
                      partyId={partyId + ""}
                    />
                    <FeaturedSlider
                      fullWidthOnSP
                      title={"Related Projects"}
                      data={props.data?.projects || []}
                      renderItems={(item) => {
                        return <ProjectProfile data={item} />;
                      }}
                    />
                    {/* @ts-expect-error */}
                    {props.elections?.map((item) => (
                      <>
                        <ElectionMostVoted
                          key={item.id}
                          electionId={item.id + ""}
                          partyId={partyId + ""}
                        />
                        <ElectionRegionalReportAsync
                          entityId={partyId + ""}
                          entityType="parties"
                          resourceUrl={`parties/analytics?partyId=${partyId}&electionId=${item.id}`}
                          // @ts-expect-error
                          renderItems={(d, { rank, region }) => {
                            return (
                              <DistrictCard
                                title={
                                  rank === "top"
                                    ? t("common:top_district", "Top District")
                                    : t(
                                        "common:bottom_district",
                                        "Bottom District"
                                      )
                                }
                                district={d}
                                icon={
                                  rank === "top" ? (
                                    //@ts-expect-error
                                    <FaArrowUp className="text-green-600" />
                                  ) : (
                                    //@ts-expect-error
                                    <FaArrowDown />
                                  )
                                }
                              />
                            );
                          }}
                          title={item.name}
                        />
                      </>
                    ))}
                    <Recommendations
                      entityType="parties"
                      entityId={partyId + ""}
                    />
                    <RecentlySelfViewed
                      lists={["parties"]}
                      entityId={partyId as string}
                    />
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings entityId={partyId as string} entityType={"Party"} />
                ),
              },

              {
                label: "leaders",
                value: "leaders",
                content: (
                  <DefaultIndexPage
                    skeletonType="list"
                    pageTitle={props?.data.localName}
                    entityType={EntityTypeEnum.Leader}
                    resource="leaders"
                    entityId={partyId as string}
                    renderItems={(items: ILeader[]) => {
                      return <EntityTable resource="leaders" data={items} />;
                    }}
                  />
                ),
              },
              {
                label: "elections",
                value: "elections",
                content: <ElectionInTab partyId={+(partyId || 0)} />,
              },
              ...getContentMenus(
                partyId as string,
                EntityTypeEnum.Party,
                //@ts-expect-error
                props.data.stats
              ),
              {
                label: "summary",
                value: "summary",
                content: (
                  <SummaryMD
                    summary={props.data?.summary || ""}
                    summaryNP={props.data?.summaryNP || ""}
                  />
                ),
              },
              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"leader" + partyId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </Box>
  );
};
export default PartyDetail;
