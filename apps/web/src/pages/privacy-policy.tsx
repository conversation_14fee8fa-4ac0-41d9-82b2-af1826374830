import Head from "next/head";
import React from "react";

const PrivacyPolicy = () => {
  return (
    <>
      <Head>
        <title>Privacy Policy | NepalTracks</title>
      </Head>
      <div className="max-w-3xl mx-auto px-4 py-10">
        <h1 className="text-3xl font-bold mb-6">
          Privacy Policy for NepalTracks.com
        </h1>
        <p className="text-sm text-gray-500 mb-8">
          Effective Date: April 28, 2025
        </p>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            1. Information We Collect
          </h2>
          <ul className="list-disc list-inside space-y-1">
            <li>
              User Information: Name, email, IP address, device/browser info.
            </li>
            <li>User Contributions: Ratings, reviews, comments.</li>
            <li>
              Behavioral Data: Pages visited, time spent, clicks (for
              analytics).
            </li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">
            2. How We Use Your Information
          </h2>
          <ul className="list-disc list-inside space-y-1">
            <li>Improve platform performance and user experience.</li>
            <li>Display personalized political content and recommendations.</li>
            <li>Prevent spam and ensure fair rating usage.</li>
            <li>Enhance security and compliance.</li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">3. Data Sharing</h2>
          <p>
            We do <strong>not sell</strong> your personal data. We may share
            data with:
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>Analytics providers (e.g., Google Analytics).</li>
            <li>Legal authorities, if required.</li>
            <li>Service providers (hosting, email, technical support).</li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">4. Cookies</h2>
          <p>
            We use cookies to remember preferences, analyze traffic, and provide
            features. You may disable cookies in your browser settings.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">5. Your Rights</h2>
          <ul className="list-disc list-inside space-y-1">
            <li>Access, correct, or delete your data.</li>
            <li>Withdraw consent at any time.</li>
            <li>
              Contact us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-blue-600 underline"
              >
                <EMAIL>
              </a>
              .
            </li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">6. Data Security</h2>
          <p>
            We apply industry-standard security measures (e.g., HTTPS,
            encryption).
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">7. Children’s Privacy</h2>
          <p>
            NepalTracks.com is not intended for users under 13. We do not
            knowingly collect data from minors.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-xl font-semibold mb-2">8. Updates</h2>
          <p>
            This policy may be updated occasionally. Changes will be posted here
            with an updated effective date.
          </p>
        </section>

        <section>
          <h2 className="text-xl font-semibold mb-2">Contact</h2>
          <p>
            Questions? Email us at:{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 underline"
            >
              <EMAIL>
            </a>
          </p>
        </section>
      </div>
    </>
  );
};

export default PrivacyPolicy;
