import { useTranslation } from "next-i18next";
import { <PERSON><PERSON>, Badge, Box, Chip, Group, Stack, Text } from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../api";
import Link from "next/link";
import { useProfile } from "@/store";

import { loadTranslation } from "@/i18n";
import Head from "next/head";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { shuffle, startCase } from "lodash";

import React, { useMemo } from "react";

import { useQuery } from "react-query";
import { SekeletonFeatureSlider } from "@/components/SekeletonFeatureSlider";
import { ILeader } from "@/interfaces/ILeader";

import MediaProfile from "@/components/MediaProfile";
import ElectionSubProfile from "@/components/ElectionSubProfile";
import RatingProfile from "@/components/RatingProfile";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import SocialMediaContent from "@/containers/SocialMediaContent";
import GovernmentProfile from "@/components/GovernmentProfile";
import WardProfile from "@/components/WardProfile";
import ElectionProfile from "@/components/ElectionProfile";
import ParliamentProfile from "@/components/ParliamentProfile";
import ContentProfile from "@/components/ContentProfile";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import PartyProfile from "@/components/PartyProfile";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import { FooterCentered } from "@/components/Footer";
import ProjectProfile from "@/components/ProjectProfile";
import {
  FaBirthdayCake,
  FaBuilding,
  FaChartLine,
  FaCity,
  FaIndustry,
  FaLandmark,
  FaRegNewspaper,
  FaSitemap,
  FaTv,
  FaUserTie,
} from "react-icons/fa";
import { FaPeopleGroup, FaUserGroup } from "react-icons/fa6";
import DepartmentProfile from "@/components/DepartmentProfile";
import withDynamicProfile from "@/containers/withDnamicProfile";

import { useInView } from "react-intersection-observer";
// import { LazyFeedChunk } from "@/components/LazyLoadChunk";

function LazyFeedChunk({ chunk }: { chunk: JSX.Element[] }) {
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin: "200px", // preload just before scroll
  });

  return <Stack ref={ref}>{inView ? chunk : <SekeletonFeatureSlider />}</Stack>;
}
export const getServerSideProps: GetServerSideProps = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  return {
    props: {
      ...translation,
    },
  };
};

const categories = [
  "mostLiked",
  "lessLiked",
  "most_viewed",
  "recentlyAdded",
  "news",
  "young",
  "old",
  "hotParliamentLeaders",
  "hotCabinetMembers",
  "repeatedLeaders",
];

function useAnalytics<T>(key: string) {
  return useQuery({
    queryKey: ["analytics", key],
    queryFn: async () => {
      const { data } = await ApiService.getAnalytics(key);
      return data?.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

const CHUNK_SIZE = 3;

function chunkArray<T>(arr: T[], chunkSize: number): T[][] {
  return Array.from({ length: Math.ceil(arr.length / chunkSize) }, (_, i) =>
    arr.slice(i * chunkSize, i * chunkSize + chunkSize)
  );
}

export default function DoubleNavbar() {
  const { t } = useTranslation();
  const profile = useProfile((s) => s.profile);

  const QuickLinks = useMemo(
    () =>
      shuffle([
        // {
        //   name: t("common:birthdayPost_socialmediacontent", "Birthday Post"),
        //   value: "birthdayPost",
        //   Icon: FaBirthdayCake,
        // },
        {
          name: t("common:leaders", "Leaders"),
          value: EntityTypeEnum.Leader,
          Icon: FaUserTie,
        },
        {
          name: t("common:parties", "Parties"),
          value: EntityTypeEnum.Party,
          Icon: FaPeopleGroup,
        },
        {
          name: t("common:governments", "Governments"),
          value: EntityTypeEnum.Government,
          Icon: FaSitemap,
        },
        {
          name: t("common:parliaments", "Parliaments"),
          value: EntityTypeEnum.Parliament,
          Icon: FaLandmark,
        },
        {
          name: t("common:projects", "Projects"),
          value: EntityTypeEnum.Project,
          Icon: FaIndustry,
        },
        {
          name: t("common:medias", "Medias"),
          value: EntityTypeEnum.Media,
          Icon: FaTv,
        },
        {
          name: t("common:contents", "Content"),
          value: EntityTypeEnum.Content,
          Icon: FaRegNewspaper,
        },
        {
          name: t("common:departments", "Departments"),
          value: EntityTypeEnum.Department,
          Icon: FaSitemap,
        },
        {
          name: t("common:elections", "Elections"),
          value: "randomElectionResults",
          Icon: FaChartLine,
        },
        {
          name: t("common:ward", "Wards"),
          value: EntityTypeEnum.Ward,
          Icon: FaBuilding,
        },
        {
          name: t("common:municipals", "Municipals"),
          value: EntityTypeEnum.Municipal,
          Icon: FaCity,
        },
        {
          name: t(
            "common:knowYourFederalRepresentatives_socialmediacontent",
            "Know Your Representatives"
          ),
          value: "knowYourLocalRepresentatives",
          Icon: FaUserGroup,
        },
      ]),
    []
  );

  const dashboardQuery = useAnalytics<any>("dashboard");

  const feeds = useMemo(
    () =>
      [
        [
          EntityTypeEnum.Leader,
          dashboardQuery.data?.leaders,
          LeaderHeroProfile,
          {
            query: dashboardQuery,
            componentProps: (
              item: any,
              { category }: { category: string }
            ) => ({
              additionalInformationEnabled: category === "mostLiked",
              slideshowMode: "hover",
              renderBeforeAvatar({ data }: { data: ILeader }) {
                if (data?.candidacyType)
                  return <Badge>{data?.candidacyType.localName}</Badge>;
                return null;
              },
            }),
          },
        ],
        [
          EntityTypeEnum.Content,
          dashboardQuery.data?.contents,
          ContentProfile,
          { fullWidthOnSP: true, query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Party,
          dashboardQuery.data?.parties,
          PartyProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Ward,
          dashboardQuery.data?.wards,
          WardProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Municipal,
          dashboardQuery.data?.municipals,
          GovernmentProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Election,
          dashboardQuery.data?.elections,
          ElectionProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Parliament,
          dashboardQuery.data?.parliaments,
          withDynamicProfile(ParliamentProfile),
          {
            query: dashboardQuery,
            componentProps: (
              item: any,
              { category }: { category: string }
            ) => ({
              slideshowMode: "hover",
              profileType:
                category === "hotParliamentLeaders" ? "Leader" : undefined,
            }),
          },
        ],
        [
          EntityTypeEnum.Government,
          dashboardQuery.data?.governments,
          withDynamicProfile(GovernmentProfile),
          {
            query: dashboardQuery,
            componentProps: (
              item: any,
              { category }: { category: string }
            ) => ({
              slideshowMode: "hover",
              profileType:
                category === "hotCabinetMembers" ? "CabinetMember" : undefined,
              data:
                category === "hotCabinetMembers"
                  ? {
                      leaders: item,
                      startedAt: item.governmentStartedAt,
                      endAt: item.governmentEndAt,
                      department: {
                        name: item.departmentName,
                      },
                    }
                  : item,
            }),
          },
        ],
        [
          EntityTypeEnum.Rating,
          dashboardQuery.data?.ratings,
          RatingProfile,
          { fullWidthOnSP: true, query: dashboardQuery },
        ],

        [
          EntityTypeEnum.ElectionSub,
          dashboardQuery.data?.electionSubs,
          ElectionSubProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Media,
          dashboardQuery.data?.medias,
          MediaProfile,
          { query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Project,
          dashboardQuery.data?.projects,
          ProjectProfile,
          {
            query: dashboardQuery,
            componentProps: (
              item: any,
              { category }: { category: string }
            ) => ({
              slideshowMode: "hover",
            }),
          },
        ],
        [
          EntityTypeEnum.RecentlyAddedSummary,
          dashboardQuery.data?.recentlyAddedSummary,
          ContentProfile,
          { fullWidthOnSP: true, query: dashboardQuery },
        ],
        [
          EntityTypeEnum.Department,
          dashboardQuery.data?.departments,
          withDynamicProfile(DepartmentProfile),
          {
            query: dashboardQuery,
            componentProps: (
              item: any,
              { category }: { category: string }
            ) => ({
              slideshowMode: "hover",
              profileType:
                category === "repeatedLeaders" ? "CabinetMember" : undefined,
              data:
                category === "repeatedLeaders"
                  ? {
                      leaders: item,
                      startedAt: item.governmentStartedAt,
                      endAt: item.governmentEndAt,
                      role: `${item.appointment_count} times`,
                      address: "",
                    }
                  : item,
            }),
          },
        ],
      ] as const,
    [dashboardQuery.data]
  );

  const feedsMemo = useMemo(() => {
    let feedItems = feeds
      .map(
        (
          [
            entityType,
            feedAnalytics,
            Component,
            //@ts-expect-error
            { query, componentProps, ...rest },
          ],
          index
        ) => {
          return categories.map((category) => {
            if (!feedAnalytics?.[category]?.items) return null;

            const formattedEntityType = entityType.toLowerCase();

            return (
              <FeaturedSlider
                boxProps={{
                  "data-entity-type": entityType,
                }}
                isLoading={
                  query === undefined
                    ? true
                    : query?.isLoading || query?.isFetching
                }
                key={`${category}-${index}`}
                title={
                  <Group className="justify-between md:justify-normal items-center">
                    {t(`common:${category}_${formattedEntityType}`, {
                      defaultValue: startCase(
                        `${category}_${formattedEntityType}`
                      ),
                    })}
                    <Link
                      href={`/feed?type=${entityType}&category=${category}`}
                      className="text-inherit text-xs mt-1"
                    >
                      {t("common:see_more", "See more")}
                      {">>"}
                    </Link>
                  </Group>
                }
                data={feedAnalytics?.[category]?.items || []}
                renderItems={(item) => (
                  <Component
                    data={item}
                    {...(componentProps?.(item, { category }) || {})}
                  />
                )}
                {...(rest || {})}
                fullWidthOnSP={entityType !== EntityTypeEnum.Leader}
              />
            );
          });
        }
      )
      .flat()
      .filter(Boolean);
    const firstItem = feedItems.shift();

    return [firstItem, feedItems, <SocialMediaContent key="social" />];
  }, [feeds, t]);

  const [firstItem, otherItems, socialMediaContent] = feedsMemo;

  // @ts-expect-error
  const chunkedFeeds = chunkArray(otherItems, CHUNK_SIZE);

  return (
    <>
      <Head>
        <title>NepalTracks | Nepal’s Ultimate Political Tracker</title>
        <meta
          name="description"
          content="Follow Nepal's political landscape with updates on elections, leaders, parties, and governance."
        />
        <meta
          property="og:title"
          content="NepalTracks – Nepal’s Ultimate Political Tracker"
        />
        <meta
          property="og:description"
          content="Follow Nepal's political landscape with updates on elections, leaders, parties, and governance."
        />
        <meta
          property="og:image"
          content="https://nepaltracks.com/logos/nepaltracks.png"
        />
        <meta property="og:url" content="https://nepaltracks.com/" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="NepalTracks – Track Nepal's Politics"
        />
        <meta
          name="twitter:description"
          content="Your go-to platform for political insights, election results, and leader profiles in Nepal."
        />
        <meta
          name="twitter:image"
          content="https://nepaltracks.com/logos/nepaltracks.png"
        />
      </Head>
      <Stack>
        <Box className="sticky top-[56px] z-30 border-b">
          <FeaturedSlider
            title=""
            data={QuickLinks}
            carouselProps={{
              slideSize: { base: "1%", sm: "1%", md: "1%" },
              slideGap: "sm",
            }}
            renderItems={(item) => (
              <Chip
                checked={false}
                value={item.value}
                variant="outline"
                onClick={() => {
                  const element = document.querySelector(
                    `[data-entity-type="${item.value}"]`
                  );
                  if (element) {
                    element.scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                      inline: "start",
                    });
                  }
                }}
              >
                <Group wrap="nowrap" gap={5}>
                  {/* @ts-expect-error */}
                  <item.Icon />
                  {item.name}
                </Group>
              </Chip>
            )}
          />
        </Box>

        {dashboardQuery.isLoading ? (
          <>
            <SekeletonFeatureSlider />
            <SekeletonFeatureSlider />
            <SekeletonFeatureSlider />
          </>
        ) : null}
        {firstItem}
        {chunkedFeeds.map((chunk, idx) => (
          // @ts-expect-error
          <LazyFeedChunk key={idx} chunk={chunk} chunkIndex={idx} />
        ))}
        {socialMediaContent}
        <Recommendations entityType="leaders" entityId={""} hideBadge />
        <RecentlySelfViewed lists={["leaders"]} />
        <FooterCentered />
      </Stack>
    </>
  );
}
