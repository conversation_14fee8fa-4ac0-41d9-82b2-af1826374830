import { useTranslation } from "next-i18next";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IParliament } from "@/interfaces/IParliament";
import { formatDate } from "@/utils";
import {
  Avatar,
  Badge,
  Box,
  Card,
  Grid,
  Group,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import { useRouter } from "next/router";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import Head from "next/head";
import AppBreadCrumb from "@/components/AppBreadCrumb";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Parliament = () => {
  const router = useRouter();
  const { levelId } = router.query;
  const { t } = useTranslation();

  return (
    <>
      <Head>
        <title>{t("common:parliaments")}</title>
      </Head>
      <AppBreadCrumb
        links={[
          {
            label: t("common:parliaments"),
            href: "/parliaments",
          },
        ]}
      />
      <DefaultIndexPage
        entityType={EntityTypeEnum.Parliament}
        pageTitle={t("common:parliaments")}
        resource={`parliaments/level/${levelId}`}
        renderItems={(items: IParliament[]) => {
          return (
            <Grid>
              {items.map((item, idnex) => {
                return (
                  <Grid.Col span={{ base: 12, md: 12 }} key={idnex}>
                    <Link
                      href={`/parliaments/${item.id}/home`}
                      className="no-underline"
                    >
                      <Card shadow="xs" h={"7rem"} p={"md"}>
                        <Group wrap="nowrap" h={"100%"} justify="space-around">
                          <Box>
                            <Avatar src="/logos/parliament_of_nepal.png" />
                          </Box>

                          <Stack gap={5} justify="end" w={"100%"}>
                            <Stack gap={1}>
                              <Group align="center">
                                <Title order={5}>
                                  {item.name} ({formatDate(item.startDate)} -{" "}
                                  {item.endDate
                                    ? formatDate(item.endDate)
                                    : t("common:present")}
                                  ){" "}
                                  <Badge size="sm" variant={"light"}>
                                    {t(
                                      "common:" + item.houseType.toLowerCase(),
                                      {
                                        defaultValue: item.houseType,
                                      }
                                    )}
                                  </Badge>
                                </Title>
                              </Group>

                              <Text
                                size="xs"
                                c={"gray"}
                                className="line-clamp-6"
                              >
                                {item.description},{" "}
                              </Text>
                            </Stack>
                            {/* <Rating value={2} size={"xs"} /> */}
                          </Stack>
                          {/* <Box>
                        <Avatar.Group>
                          <Avatar src="image.png" />
                          <Avatar src="image.png" />
                        </Avatar.Group>
                      </Box> */}
                        </Group>
                      </Card>
                    </Link>
                  </Grid.Col>
                );
              })}
            </Grid>
          );
        }}
      ></DefaultIndexPage>
    </>
  );
};
export default Parliament;
