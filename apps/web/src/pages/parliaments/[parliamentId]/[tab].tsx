import { useTranslation } from "next-i18next";
import { EntityInfo } from "@/components/EntityInfo";
import {
  Avatar,
  Badge,
  Box,
  Center,
  Divider,
  Flex,
  Grid,
  Group,
  List,
  Modal,
  Paper,
  Pill,
  RingProgress,
  Skeleton,
  Stack,
  Text,
  ThemeIcon,
  Title,
} from "@mantine/core";
import { useRouter } from "next/router";
import Ratings from "@/containers/Ratings";
import SegmentedTab from "@/components/SegmentedTab";
import Link from "next/link";
import { GetServerSideProps } from "next";
import DisqusComment from "@/components/DisqusComment";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { ILeader } from "@/interfaces/ILeader";
import {
  formatConstituencyAddress,
  formatDate,
  getImageUrlWithFallback,
  getPublicSiteUrl,
} from "@/utils";
import { IParty } from "@/interfaces/IParty";
import ElectionResultsContents from "@/containers/ElectionResultContents";
import { VoteCountBox } from "@/components/VoteCountBox";
import ElectionPartiesTab from "@/containers/ElectionPartiesTab";
import { IParliament } from "@/interfaces/IParliament";
import { getContentMenus } from "@/containers/utils";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { useQuery } from "react-query";
import { IGovernmentOverview } from "@/interfaces/IGovernmentOverview";
import { useMemo, useState } from "react";
import { IGovernmentParty } from "@/interfaces/IGovernmentParty";
import { UserInfoAction } from "@/components/UserInfoAction";
import { IElectionPartyResponse } from "@/components/PartiesTable";
import { IGovernment } from "@/interfaces/IGovernment";
import SummaryMD from "@/components/SummaryMD";
import SeatDistribution from "@/components/SeatDistribution";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import { loadTranslation } from "@/i18n";
import { ApiService } from "../../../../api";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import {
  IParliamentMemberInfo,
  IParliamentOverview,
} from "@/interfaces/IParliamentOverview";
import { useDisclosure } from "@mantine/hooks";
import ParliamentParty from "@/components/ParliamentParty";
import { truncate } from "lodash";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import PartyBadge from "@/components/PartyBadge";
import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import GovernmentProfile from "@/components/GovernmentProfile";
import PollsContainer from "@/containers/PollContainer";

export const FEDERAL_PARLIAMENT_TOTAL_SEATS = 275;
export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  //@ts-expect-error
  const { data } = await ApiService.resource.getById(
    `parliaments`,
    context.query.parliamentId as string
  );
  const { data: elections } = data.electionId
    ? await ApiService.resource.getAll(`elections/${data.electionId}`, {})
    : { data: null };
  data.img = getPublicSiteUrl() + "/logos/parliament_of_nepal.png";
  return {
    props: {
      ...translation,
      elections,
      parliament: data,
    },
  };
};

const Parliament = (props: { parliament: IParliament; elections: any }) => {
  const router = useRouter();
  const [selectedParliamentMembers, setSelectedParliamentMembers] = useState<
    IParliamentMemberInfo[]
  >([]);
  const { parliamentId, levelId } = router.query;
  const { t } = useTranslation();
  const government = props.parliament?.governments?.find((item) => !item.endAt);
  const [opened, { close, open }] = useDisclosure();

  const speakers = useMemo(() => {
    return (
      props.parliament.speakers?.sort?.((a, b) => {
        if (a.memberType === "SPEAKER" && b.memberType !== "SPEAKER") return -1;
        if (a.memberType !== "SPEAKER" && b.memberType === "SPEAKER") return 1;
        return 0;
      }) || []
    );
  }, [props.parliament.speakers]);

  const governmentOverviewQuery = useQuery(
    "governmentOverviewQuery" + government?.id,
    async () => {
      const data = await ApiService.resource.getAll<{
        data: IGovernmentOverview;
      }>(
        "governments/" +
          government?.government_type +
          "/" +
          government?.id +
          "/overview",
        {}
      );
      return data;
    },
    { enabled: !!government }
  );

  const parliamentOverviewQuery = useQuery(
    "parliamentOverviewQuery" + parliamentId,
    async () => {
      const response = await ApiService.resource.getAll<{
        data: IParliamentOverview;
      }>("parliaments/" + parliamentId + "/overview", {});
      return response.data;
    }
  );
  const electionsQuery = useQuery(
    [t("common:data"), parliamentId, t("common:parliaments")],
    async () => {
      const response = await ApiService.resource.getAll(
        `parliaments/${parliamentId}/parties`,
        {}
      );
      return response.data as unknown as IElectionPartyResponse[];
    }
  );

  const parties = electionsQuery.data || [];

  const governmentOverview = governmentOverviewQuery.data
    ?.data as unknown as IGovernmentOverview;
  const parliamentOverview =
    parliamentOverviewQuery?.data as unknown as IParliamentOverview;
  const data = governmentOverview?.coaltion.map((coalition) => ({
    name: coalition.party.localName,
    cabinetCount: coalition.members.length,
  }));

  const cabinetDistribution = useMemo(() => {
    const allmembers = governmentOverview?.coaltion.flatMap(
      (item) => item.members
    );
    return (
      governmentOverview?.coaltion?.map((item) => {
        return {
          label: item.party.localName,
          count: item.members.length,
          part: +((item.members.length / allmembers.length) * 100).toFixed(2),
          color: `${item.party.partyColorCode}`,
          img: item.party.logo,
        };
      }) || []
    );
  }, [governmentOverview?.coaltion]);

  const governmentParties = useMemo(() => {
    return parliamentOverview?.rulingParties || [];
  }, [parliamentOverviewQuery.data]);

  const oppositionDistribution = useMemo(() => {
    return parliamentOverview?.oppositionParties || [];
  }, [parliamentOverviewQuery.data]);

  const governmentSeatData = useMemo(() => {
    return governmentParties.map(({ party, wonPlaces, totalMembersCount }) => ({
      name: party.localName,
      seats: totalMembersCount,
      img: party.logo,
      color: party.partyColorCode,
    }));
  }, [governmentParties]);

  const oppositionSeatData = useMemo(() => {
    return oppositionDistribution.map(
      ({ party, wonPlaces, totalMembersCount }) => ({
        name: party.localName,
        seats: totalMembersCount,
        color: party.partyColorCode,
        img: party.logo,
      })
    );
  }, [oppositionDistribution]);
  useDynamicEntityStorage("parliaments", parliamentId + "");

  const chartData = useMemo(() => {
    return [...oppositionSeatData, ...governmentSeatData].sort(
      (a, b) => b.seats - a.seats
    );
  }, [oppositionSeatData, governmentSeatData]);
  const seatData = chartData;

  return (
    <>
      <DynamicSEO data={props.parliament} type="parliaments" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:parliaments"),
            href: "/parliaments",
          },
          {
            label: props.parliament?.name || "",
            href: `/parliaments/${parliamentId}`,
          },
        ]}
      />
      <Flex w={"100%"} direction="column" gap={10}>
        <Box>
          <EntityInfo
            rate={props.parliament.rating?.average}
            partyId=""
            linkComponent={Link}
            name={<Group>{props.parliament?.name}</Group>}
            avatar={"/logos/parliament_of_nepal.png"}
            id={props.parliament?.id + ""}
            address={`${
              props.parliament.states?.localName || "Centre"
            } ${formatDate(props.parliament.startDate)} -
${props.parliament.endDate ? formatDate(props.parliament.endDate) : "Present"}`}
          />
        </Box>

        <SegmentedTab
          onTabChange={(tab, router) => {
            router.push(`/parliaments/${props.parliament.id}/${tab}`);
          }}
          entityId={parliamentId as string}
          resources={`parliaments/level/${parliamentId}`}
          data={[
            {
              label: "Home",
              value: "home",
              content: (
                <Stack>
                  <Stack>
                    <SummaryMD
                      summary={props.parliament?.summary || ""}
                      summaryNP={props.parliament?.summaryNP || ""}
                      readMoreLink={`/parliaments/level/${levelId}/parliament/${props.parliament.id}/summary`}
                    />
                    <ContentStatsCards stats={props.parliament?.stats || {}} />
                    <FeaturedSlider<
                      NonNullable<IParliament["speakers"]>[number]
                    >
                      isLoading={electionsQuery.isLoading}
                      title={t("common:parliament-heads", {
                        defaultValue: "Speakers",
                      })}
                      data={speakers || []}
                      renderItems={function (
                        item: NonNullable<IParliament["speakers"]>[number]
                      ): JSX.Element {
                        return (
                          <UserInfoAction
                            img={getImageUrlWithFallback(
                              item.member.img,
                              item.member.ecCandidateID
                            )}
                            name={
                              <Link
                                href={`/leaders/${item.member.id}`}
                                className="text-inherit no-underline"
                              >
                                {item.member.localName}
                              </Link>
                            }
                            description={
                              t("common:" + item.memberType, item.memberType) ||
                              item.memberType
                            }
                          />
                        );
                      }}
                    />
                    <AsyncFeatureSlider
                      // @ts-expect-error
                      transformResponse={(data) => data?.items || []}
                      entityId={parliamentId as string}
                      title={t("common:hot_parliamentarians", {
                        defaultValue: "Hot Parliamentarians",
                      })}
                      entityType="parliaments"
                      resourceUrl={`parliaments/${parliamentId}/hot-parliament-leaders`}
                      renderItems={(item: ILeader) => {
                        return <LeaderHeroProfile data={item} />;
                      }}
                    />
                    <FeaturedSlider<IGovernmentParty>
                      isLoading={governmentOverviewQuery.isLoading}
                      title={`Ruling Parties (सत्ता पक्ष) (${governmentOverview?.coaltion?.length})`}
                      data={governmentOverview?.coaltion || []}
                      fullWidthOnSP
                      renderItems={function ({
                        party: item,
                        members,
                      }: IGovernmentParty): JSX.Element {
                        return (
                          <EntityInfo
                            cardProps={{ mih: "160px" }}
                            resources="parties"
                            id={item.id + ""}
                            name={item.localName}
                            avatar={getImageUrlWithFallback(
                              item.logo,
                              item.id + ""
                            )}
                            title={
                              <Group>
                                {" "}
                                {t("common:cabinet_members_count", {
                                  count: members.length,
                                })}
                              </Group>
                            }
                          />
                        );
                      }}
                    />
                    <FeaturedSlider<IGovernment>
                      fullWidthOnSP
                      isLoading={electionsQuery.isLoading}
                      title={
                        t("common:governments", {
                          defaultValue: "Government",
                        }) +
                        " (" +
                        props.parliament?.governments?.length +
                        ")"
                      }
                      data={props.parliament?.governments || []}
                      renderItems={function (
                        government: IGovernment
                      ): JSX.Element {
                        return <GovernmentProfile data={government} />;
                      }}
                    />{" "}
                    {props.elections?.mostVotedLeaders && (
                      <FeaturedSlider<ILeader>
                        title={t("common:most_voted_leaders", {
                          defaultValue: "Most voted leaders",
                        })}
                        data={props.elections.mostVotedLeaders}
                        //@ts-expect-error
                        renderItems={(item: {
                          voteCount: number;
                          leaders: ILeader;
                        }) => {
                          return (
                            <UserInfoAction
                              img={getImageUrlWithFallback(
                                item.leaders.img,
                                item.leaders.ecCandidateID
                              )}
                              name={
                                <Link
                                  href={`/leaders/${item.leaders.id}`}
                                  className="text-inherit no-underline"
                                >
                                  {item.leaders.localName}
                                </Link>
                              }
                              description={
                                <VoteCountBox count={item.voteCount} />
                              }
                            />
                          );
                        }}
                      />
                    )}{" "}
                    {props.elections?.topPartiesWithDetails && (
                      <FeaturedSlider<ILeader>
                        fullWidthOnSP
                        title={t("common:most_voted_parties", {
                          defaultValue: "Most voted parties",
                        })}
                        data={props.elections.topPartiesWithDetails}
                        //@ts-expect-error
                        renderItems={(item: {
                          value: number;
                          party: IParty;
                        }) => {
                          return (
                            <EntityInfo
                              resources="parties"
                              cardProps={{ mih: "160px" }}
                              id={item.party.id + ""}
                              partyId={item.party.id + ""}
                              name={item.party.localName}
                              avatar={getImageUrlWithFallback(
                                item.party.logo,
                                item.party.id + ""
                              )}
                              address={<VoteCountBox count={item.value} />}
                            />
                          );
                        }}
                      />
                    )}{" "}
                    {props.elections?.mostWin && (
                      <FeaturedSlider<ILeader>
                        fullWidthOnSP
                        title={t("common:most_win_parties", {
                          defaultValue: "Most win parties",
                        })}
                        data={props.elections.mostWin}
                        //@ts-expect-error
                        renderItems={(item: {
                          value: number;
                          party: IParty;
                        }) => {
                          return (
                            <EntityInfo
                              resources="parties"
                              cardProps={{ mih: "160px" }}
                              partyId={item.party.id + ""}
                              name={item.party.localName}
                              id={item.party.id + ""}
                              avatar={getImageUrlWithFallback(
                                item.party.logo,
                                item.party.id + ""
                              )}
                              address={<VoteCountBox count={item.value} />}
                            />
                          );
                        }}
                      />
                    )}
                    <Stack>
                      <Title order={4}>
                        {t("common:government_opposition")}
                      </Title>

                      <SegmentedTab
                        defaultValue={"bar"}
                        entityId={parliamentId as string}
                        resources={`parliaments/level/${parliamentId}`}
                        disableRouting
                        data={[
                          {
                            label: "bar",
                            value: "bar",
                            content: (
                              <Grid>
                                <Grid.Col span={{ md: 6, base: 12 }}>
                                  <Paper p={"xs"}>
                                    <Title order={4}>
                                      {t("common:ruling_parties")}
                                    </Title>
                                    <Text size="sm" c={"dimmed"}>
                                      {t("common:government_parties_seats", {
                                        count: governmentParties?.length,
                                        seats: governmentParties?.reduce(
                                          (acc, item) =>
                                            acc + item.members.length,
                                          0
                                        ),
                                      })}
                                    </Text>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-1">
                                      {governmentParties.map(
                                        (parliamentInfo) => (
                                          <ParliamentParty
                                            parliamentInfo={parliamentInfo}
                                            onSeeMoreMembersClick={(
                                              members
                                            ) => {
                                              open();
                                              return setSelectedParliamentMembers(
                                                members
                                              );
                                            }}
                                          />
                                        )
                                      )}
                                    </div>
                                  </Paper>
                                </Grid.Col>
                                <Grid.Col span={{ md: 6, base: 12 }}>
                                  <Paper p={"xs"}>
                                    <Title order={4}>
                                      {t("common:opponent_parties")}
                                    </Title>
                                    <Text size="sm" c={"dimmed"}>
                                      {t("common:government_parties_seats", {
                                        count: oppositionDistribution?.length,
                                        seats: oppositionDistribution?.reduce(
                                          (acc, item) =>
                                            acc + item.totalMembersCount,
                                          0
                                        ),
                                      })}
                                    </Text>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-1">
                                      {oppositionDistribution.map(
                                        (parliamentInfo) => (
                                          <ParliamentParty
                                            parliamentInfo={parliamentInfo}
                                            onSeeMoreMembersClick={(
                                              members
                                            ) => {
                                              open();
                                              return setSelectedParliamentMembers(
                                                members
                                              );
                                            }}
                                          />
                                        )
                                      )}
                                    </div>
                                  </Paper>
                                </Grid.Col>
                              </Grid>
                            ),
                          },
                          {
                            label: "treemap",
                            value: "treemap",
                            content: (
                              <Grid>
                                <Grid.Col span={{ md: 6, base: 12 }}>
                                  <Box w={"100%"} h={"500px"}>
                                    <Title order={4}>
                                      {t("common:ruling_parties")}
                                    </Title>
                                    <Text size="sm" c={"dimmed"}>
                                      {t("common:government_parties_seats", {
                                        count: governmentParties?.length,
                                        seats: governmentParties?.reduce(
                                          (acc, item) =>
                                            acc + item.totalMembersCount,
                                          0
                                        ),
                                      })}
                                    </Text>
                                    <SeatDistribution
                                      seatData={governmentSeatData}
                                    />
                                  </Box>
                                </Grid.Col>
                                <Grid.Col span={{ md: 6, base: 12 }}>
                                  <Title order={4}>
                                    {t("common:opponent_parties")}
                                  </Title>
                                  <Text size="sm" c={"dimmed"}>
                                    {t("common:government_parties_seats", {
                                      count: oppositionDistribution?.length,
                                      seats: oppositionDistribution?.reduce(
                                        (acc, item) =>
                                          acc + item.totalMembersCount,
                                        0
                                      ),
                                    })}
                                  </Text>
                                  <Box w={"100%"} h={"500px"}>
                                    <SeatDistribution
                                      seatData={oppositionSeatData}
                                    />
                                  </Box>
                                </Grid.Col>
                              </Grid>
                            ),
                          },
                        ]}
                      />
                    </Stack>
                    <Stack>
                      <Title order={4}>{t("common:seats_distribution")}</Title>
                      <SegmentedTab
                        defaultValue={"bar"}
                        entityId={parliamentId + ""}
                        resources={"parliaments"}
                        disableRouting
                        data={[
                          {
                            label: "bar",
                            value: "bar",
                            content: (
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                                {chartData.map((party, index) => (
                                  <Paper
                                    withBorder
                                    radius="md"
                                    p="xs"
                                    key={index}
                                  >
                                    <Group align="center">
                                      <RingProgress
                                        size={80}
                                        roundCaps
                                        thickness={8}
                                        sections={[
                                          {
                                            value:
                                              (party.seats /
                                                FEDERAL_PARLIAMENT_TOTAL_SEATS) *
                                              100,
                                            color: party.color || "gray",
                                          },
                                        ]}
                                        label={
                                          <Center>
                                            <Text className="text-xl font-semibold">
                                              #{index + 1}
                                            </Text>
                                          </Center>
                                        }
                                      />

                                      <div>
                                        <Text
                                          c="dimmed"
                                          size="xs"
                                          tt="uppercase"
                                          fw={700}
                                          aria-label={party.name}
                                          title={party.name}
                                        >
                                          {truncate(party.name, { length: 30 })}
                                        </Text>
                                        <Text fw={700} size="xl">
                                          {party.seats} seats
                                        </Text>
                                      </div>
                                    </Group>
                                  </Paper>

                                  // <div
                                  //   key={party.name}
                                  //   className="flex items-center p-3 rounded-lg shadow bg-white"
                                  // >
                                  //   <Badge radius={"xl"} color={party.color}>
                                  //     {index + 1}
                                  //   </Badge>
                                  //   <div>
                                  //     <div className="font-semibold text-gray-800">
                                  //       {party.name}
                                  //     </div>
                                  //     <div className="text-sm text-gray-600">
                                  //       {party.seats} seats
                                  //     </div>
                                  //   </div>
                                  // </div>
                                ))}
                              </div>
                            ),
                          },
                          {
                            label: t("common:treemap"),
                            value: "treemap",
                            content: (
                              <Box w={"100%"} h={"500px"}>
                                <SeatDistribution seatData={seatData} />
                              </Box>
                            ),
                          },
                        ]}
                      />
                      <SegmentedTab
                        defaultValue={"bar"}
                        entityId={parliamentId + ""}
                        resources={"parliaments"}
                        disableRouting
                        data={[
                          {
                            label: "bar",
                            value: "bar",
                            content: (
                              <ResponsiveContainer width="100%" height={400}>
                                <BarChart
                                  width={500}
                                  height={300}
                                  data={chartData}
                                  margin={{
                                    top: 5,
                                    right: 30,
                                    left: 20,
                                    bottom: 5,
                                  }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" />
                                  <XAxis dataKey={"name"} />
                                  <YAxis
                                    domain={[0, FEDERAL_PARLIAMENT_TOTAL_SEATS]}
                                  />
                                  <Tooltip />
                                  <Bar
                                    dataKey={"seats"}
                                    background={{ fill: "#eee" }}
                                  >
                                    {chartData.map((entry, index) => (
                                      <Cell
                                        key={`cell-${index}`}
                                        fill={entry.color}
                                      />
                                    ))}
                                  </Bar>
                                </BarChart>
                              </ResponsiveContainer>
                            ),
                          },
                          {
                            label: "pie",
                            value: "pie",
                            content: (
                              <ResponsiveContainer width="100%" height={400}>
                                <PieChart>
                                  <Pie
                                    dataKey={"seats"}
                                    data={seatData}
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={130}
                                    label
                                  >
                                    {seatData.map((entry, index) => (
                                      <Cell
                                        key={`cell-${index}`}
                                        fill={entry.color}
                                      />
                                    ))}
                                  </Pie>
                                  <Tooltip />
                                  <Legend />
                                </PieChart>
                              </ResponsiveContainer>
                            ),
                          },
                        ]}
                      />
                    </Stack>
                    {/* <ElectionPartiesTab
                      displayCharts
                      hideTable
                      entityId={parliamentId as string}
                      resources="parliaments"
                    />{" "} */}
                  </Stack>
                </Stack>
              ),
            },
            {
              label: "reviews",
              value: "ratings",
              content: (
                <Ratings
                  entityId={`${parliamentId}`}
                  entityType={"PARLIAMENT"}
                />
              ),
            },
            {
              label: "Polls",
              value: "polls",
              content: (
                <PollsContainer
                  entityId={parliamentId + ""}
                  entityType={EntityTypeEnum.Parliament}
                />
              ),
            },
            {
              label: "parties",
              value: "parties",
              content: (
                <ElectionPartiesTab
                  entityId={parliamentId as string}
                  resources="parliaments"
                />
              ),
            },
            {
              label: "members",
              value: "leaders",
              content: (
                <ElectionResultsContents
                  noFilter
                  url={`parliaments/${props.parliament.id}/leaders`}
                  electionId={props.parliament.electionId || ""}
                  renderCustomTable={(items, options) => (
                    <Grid mt={"md"} p={"md"}>
                      {items.map((item: any) => (
                        <>
                          <Grid.Col
                            key={item.id}
                            span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                          >
                            <LeaderHeroProfile
                              data={item.leaders}
                              renderBeforeAvatar={() => (
                                <Badge
                                  radius={"sm"}
                                  variant={t("common:outline")}
                                  color={"gray"}
                                >
                                  {t(`common:${item.electionType}`, {
                                    defaultValue: item.electionType,
                                  })}
                                  {/* {item.electionType} */}
                                </Badge>
                              )}
                              renderDescription={() => (
                                <Stack gap={3}>
                                  {item.area && (
                                    <Text size="xs" c="dimmed">
                                      {formatConstituencyAddress({
                                        area: item.area,
                                        district: item.districts,
                                        municipal: item.municipals,
                                        ward: item.ward,
                                      })}
                                    </Text>
                                  )}
                                  {item.parties && (
                                    <PartyBadge
                                      partyId={item.parties?.id}
                                      partyImage={item.parties?.logo}
                                      partyName={item.parties?.localName}
                                    />
                                  )}
                                  {item.voteCount && (
                                    <VoteCountBox count={item.voteCount} />
                                  )}
                                </Stack>
                              )}
                            />
                          </Grid.Col>
                          {options.isLoading ? (
                            <Grid.Col
                              key={item.id}
                              span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                            >
                              <Skeleton h={300} />
                            </Grid.Col>
                          ) : null}
                        </>
                      ))}
                    </Grid>
                  )}
                />
              ),
            },
            ...getContentMenus(
              parliamentId as string,
              EntityTypeEnum.Parliament,
              props.parliament.stats,
              [
                // {
                //   label: "Bills & Laws",
                //   key: "bills",
                //   types: ["BILLS", "LAWS", "NOTICES"],
                // },
              ]
            ),
            {
              label: "summary",
              value: "summary",
              content: (
                <SummaryMD
                  summary={props.parliament?.summary || ""}
                  summaryNP={props.parliament?.summaryNP || ""}
                />
              ),
            },
            {
              label: "discussions",
              value: "discussions",
              content: (
                <DisqusComment
                  url={
                    (typeof window !== "undefined" && window?.location?.href) ||
                    ""
                  }
                  identifier={"election" + props.parliament?.id}
                />
              ),
            },
          ]}
        />
      </Flex>
      <Modal
        opened={opened}
        onClose={close}
        title={`${
          selectedParliamentMembers?.[0]?.parties?.localName || "Unknown"
        } (${selectedParliamentMembers.length})`}
      >
        <List spacing="md" size="sm" center w={"100%"}>
          {selectedParliamentMembers.map((member, index) => {
            return (
              <List.Item
                className="bg-gray-100 p-2 rounded-lg"
                key={index}
                icon={
                  <Avatar
                    size={"sm"}
                    src={getImageUrlWithFallback(
                      member.leaders.img,
                      member.leaders.ecCandidateID + ""
                    )}
                  />
                }
              >
                <Stack gap={1} w={"100%"}>
                  <Link
                    href={`/leaders/${member.leaders.id}`}
                    className="text-inherit no-underline text-xs"
                  >
                    <Text className={"text-gray-800 text-xs"} fw={600}>
                      {member.leaders.localName} ({member.leaders.name})
                    </Text>
                  </Link>
                  <Group gap={5}>
                    <Badge
                      size="xs"
                      color={
                        member.electionType === "DIRECT" ? "green" : "lime"
                      }
                    >
                      {member.electionType}
                    </Badge>
                    {member.electionType === "DIRECT" && (
                      <VoteCountBox count={member.voteCount || 0} />
                    )}
                  </Group>
                </Stack>
              </List.Item>
            );
          })}
        </List>
      </Modal>
    </>
  );
};
export default Parliament;
