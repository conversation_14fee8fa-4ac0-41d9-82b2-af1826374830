import { useTranslation } from "next-i18next";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IParliamentQuery } from "@/interfaces/IParliament";
import {
  Avatar,
  Box,
  Card,
  Grid,
  Group,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Parliaments = () => {
  const { t } = useTranslation();

  return (
    <DefaultIndexPage
      pageTitle={t("common:parliaments")}
      entityType={EntityTypeEnum.Parliament}
      resource={"parliaments"}
      renderItems={(items: IParliamentQuery[]) => {
        return (
          <Grid>
            {items.map((item, idnex) => {
              return (
                <Grid.Col key={idnex} span={{ base: 12, md: 12 }}>
                  <Link
                    href={`/parliaments/level/${item.stateId}`}
                    className="no-underline"
                  >
                    <Card shadow="xs" h={"7rem"} p={"md"}>
                      <Group wrap="nowrap" h={"100%"} justify="space-around">
                        <Box>
                          <Avatar src="/logos/parliament_of_nepal.png" />
                        </Box>

                        <Stack gap={5} justify="end" w={"100%"}>
                          <Stack gap={1}>
                            <Title order={5}>
                              {item.state?.localName ||
                                t("common:centre", {
                                  defaultValue: "Centre",
                                })}
                            </Title>
                            <Text size="xs" c={"gray"} className="line-clamp-6">
                              ( {item.count} ){" "}
                              {t("common:parliaments", {
                                defaultValue: "Parliament",
                              })}
                            </Text>
                          </Stack>
                          {/* <Rating value={2} size={"xs"} /> */}
                        </Stack>
                        {/* <Box>
                        <Avatar.Group>
                          <Avatar src="image.png" />
                          <Avatar src="image.png" />
                        </Avatar.Group>
                      </Box> */}
                      </Group>
                    </Card>
                  </Link>
                </Grid.Col>
              );
            })}
          </Grid>
        );
      }}
    ></DefaultIndexPage>
  );
};
export default Parliaments;
