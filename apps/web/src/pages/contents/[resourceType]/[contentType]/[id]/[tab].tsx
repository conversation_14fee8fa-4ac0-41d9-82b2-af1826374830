import { useTranslation } from "next-i18next";
import DisqusComment from "@/components/DisqusComment";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import {
  Accordion,
  Avatar,
  Badge,
  Blockquote,
  Box,
  Breadcrumbs,
  Center,
  Container,
  Group,
  Paper,
  Stack,
  Text,
  Timeline,
  Title,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../../api";
import { useRouter } from "next/router";
import { IContent } from "@/interfaces/IContent";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  extractLinks,
  formatDate,
  getImageUrlWithFallback,
  handleURLPreviewFetcher,
  replaceLinksWithSources,
} from "@/utils";
import { sortBy, startCase } from "lodash";
import { formatDistance } from "date-fns";
import RenderResourceType from "@/containers/RenderResourceType";
import { IContentStats } from "@/interfaces/IContentStats";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import Recommendations from "@/containers/Recommendations";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import Link from "next/link";
import { IconHome } from "@tabler/icons-react";
import { LinkPreview } from "@dhaiwat10/react-link-preview";
import { useMemo, useState } from "react";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import ReactMarkdown from "react-markdown";
import { theme } from "@/theme";
import { FaI } from "react-icons/fa6";
import { FaBook, FaMicrochip } from "react-icons/fa";
import ContentSeries from "@/components/ContentSeries";
import YouTube from "react-youtube";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  const { data } = await ApiService.resource.getAll(
    `contents/${context.params?.id}`,
    {}
  );
  return {
    props: {
      content: data,
      ...translation,
    },
  };
};

const extractYoutubeVideoIdFromLink = (url: string) => {
  const regExp =
    /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
  const match = url.match(regExp);
  return match && match[7].length === 11 ? match[7] : null;
};

const ContentsDetails = ({
  content: { item, stats },
}: {
  content: { item: IContent; stats: IContentStats };
}) => {
  const router = useRouter();
  const { id, contentType, resourceType } = router.query;
  const { colorScheme } = useMantineColorScheme();
  const theme = useMantineTheme();

  const color =
    ContentTypeColors[item.contentType as ContentTypeIconMapType] || "gray";
  const { t } = useTranslation();

  const isCMSLinkYoutube = extractYoutubeVideoIdFromLink(item.cmsLink || "");

  const links = useMemo(() => {
    return (extractLinks(item.content) || []).filter(
      (link) => link !== item.cmsLink
    );
  }, [item, isCMSLinkYoutube]);
  const series = useMemo(() => {
    const onlyThisResource = item.childContents?.map((e: any) => {
      const icon = ContentTypeIconMap[e.contentType as ContentTypeIconMapType];
      return {
        ...e,
        icon,
      };
    });

    return sortBy(
      [...(onlyThisResource || []), item],
      (e) => new Date(e.eventDate)
    );
  }, [item]);

  return (
    <>
      <DynamicSEO
        forcePageTitle={item.title}
        data={item}
        type="contents"
        slug={`contents/${resourceType}/${contentType}`}
      />
      <Container size={"lg"}>
        <Breadcrumbs p={"sm"}>
          <Link href={`/`} className="text-inherit">
            <Group gap={1} align="center">
              <IconHome size={14} />
            </Group>
          </Link>
          <Link href={`/contents`} className="text-inherit">
            {t("common:contents")}
          </Link>
          <Link href={`/contents/${resourceType}`} className="text-inherit">
            {t(`common:${resourceType}`)}
          </Link>
          <Link
            href={`/contents/${resourceType}/${contentType}`}
            className="text-inherit"
          >
            {t(`common:${contentType}`)}
          </Link>
        </Breadcrumbs>
        <Stack w={"100%"}>
          <Paper>
            <Stack>
              <Stack gap={0}>
                <Group>
                  <Title order={3}>{item.title}</Title>
                  <Text c="dimmed">
                    {/* @ts-expect-error */}
                    {item.resource?.localName || item.resource?.name}
                  </Text>
                </Group>
                {item.cmsLink && (
                  <Link
                    target="_blank"
                    href={item.cmsLink}
                    className="text-inherit text-sm"
                  >
                    <Text c="dimmed" size="sm">
                      {item.cmsLink}
                    </Text>
                  </Link>
                )}
              </Stack>
              <Group>
                <Badge color={color}>{item.contentType}</Badge>{" "}
                <Badge color={color} variant={"outline"}>
                  {startCase(item.contentStatus)}
                </Badge>
                <Group>
                  <Text c="dimmed">
                    {formatDate(item.eventDate || item.createdAt)}
                    {" - "}
                    {item.eventDate
                      ? formatDistance(
                          new Date(item.eventDate || ""),
                          new Date()
                        )
                      : formatDistance(
                          new Date(item.createdAt || ""),
                          new Date()
                        )}{" "}
                    ago
                  </Text>
                </Group>
              </Group>
              {item.summary && (
                // <Paper
                //   p={"md"}
                //   variant="outline"
                //   shadow="xs"
                //   bg={colorScheme === "dark" ? "gray.8" : "blue.0"}
                // >
                <Blockquote
                  color="blue"
                  cite={`– AI Summary`}
                  // @ts-expect-error
                  icon={<FaBook />}
                  mt="xl"
                >
                  <ReactMarkdown>{item.summary}</ReactMarkdown>
                </Blockquote>
                // </Paper>
              )}
              {item.summary && <Text fw={700}>{"Excerpts from source: "}</Text>}
              <Text
                className={`text-md md:text-md  leading-loose ${
                  item.summary ? "line-clamp-10 italic" : ""
                }`}
                fw={500}
              >
                {item.content}
              </Text>

              {isCMSLinkYoutube && (
                <YouTube
                  videoId={isCMSLinkYoutube}
                  title={item.title}
                  opts={{
                    width: "100%",
                    height: "400px",
                  }}
                />
              )}
              <Stack>
                {item.cmsLink && !isCMSLinkYoutube && (
                  <LinkPreview
                    showPlaceholderIfNoImage
                    descriptionLength={50}
                    className="text-md"
                    url={item.cmsLink}
                    fetcher={() =>
                      // @ts-expect-error
                      handleURLPreviewFetcher(item.cmsLink, item.metadata)
                    }
                  />
                )}

                <FeaturedSlider
                  fullWidthOnSP
                  data={links}
                  title={"References mentioned"}
                  renderItems={(item) => {
                    const isYoutube = extractYoutubeVideoIdFromLink(item);
                    if (isYoutube) {
                      return (
                        <Paper className="text-xs w-full md:w-[300px]">
                          <YouTube
                            videoId={isYoutube}
                            title={item}
                            opts={{
                              width: "100%",
                              height: "400px",
                            }}
                          />
                        </Paper>
                      );
                    }
                    return (
                      <LinkPreview
                        imageHeight={"150px"}
                        className="text-xs w-full md:w-[250px]"
                        showPlaceholderIfNoImage
                        descriptionLength={50}
                        height={"300px"}
                        url={item}
                        // @ts-expect-error
                        fetcher={handleURLPreviewFetcher}
                      />
                    );
                  }}
                />

                <Title order={5}>
                  {t("common:invovled-event", {
                    entity: t(`common:${resourceType}`),
                    item: t(`common:${contentType}`),
                    defaultValue: `  ${startCase(
                      resourceType?.toString().toLowerCase().replace(/_/g, " ")
                    )}{" "}
                involved in this ${contentType}`,
                  })}{" "}
                </Title>
                <Paper
                  bg={
                    colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[1]
                  }
                  p={"xs"}
                  radius={"md"}
                >
                  <RenderResourceType
                    resourceType={resourceType as any}
                    //@ts-expect-error
                    resource={item.resource}
                  />
                </Paper>

                <ContentSeries series={series} content={item} />
                <br />
                <Recommendations
                  disableSeeMore
                  fullWidthOnSP
                  entityType="contents"
                  entityId={id + ""}
                />
                <ContentStatsCards stats={stats || {}} />
              </Stack>
            </Stack>
          </Paper>
          <SegmentedTab
            entityId={id as string}
            resources={`/contents/${resourceType}/${contentType}`}
            defaultValue={"ratings"}
            data={[
              {
                label: "Home",
                value: "home",
                content: (
                  <Stack p={"md"}>
                    <Ratings entityId={`${id}`} entityType={"CONTENT"} />
                  </Stack>
                ),
              },

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={
                      "contents" + `${id}-${contentType}-${resourceType}`
                    }
                  />
                ),
              },
            ]}
          />
        </Stack>
      </Container>
    </>
  );
};
export default ContentsDetails;
