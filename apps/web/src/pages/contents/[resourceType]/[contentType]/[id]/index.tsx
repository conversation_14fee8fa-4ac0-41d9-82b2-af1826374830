import { useEffect } from "react";
import { useRouter } from "next/router";

import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";

export const getServerSideProps: GetServerSideProps = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  const { resourceType, contentType, id } = context.params as {
    [key: string]: string;
  };
  return {
    redirect: {
      destination:
        "/" +
        context.locale +
        `/contents/${resourceType}/${contentType}/${id}/home`,
      permanent: false,
    },
    props: {
      ...translation,
    },
  };
};
const ContentsDetails = () => {
  const router = useRouter();
  const { id, contentType, resourceType } = router.query;

  useEffect(() => {
    router.push("/home");
  }, [router]);

  return null;
};

export default ContentsDetails;
