import ContentsContainer from "@/containers/ContentsContainer";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { Breadcrumbs, Chip, Group, Stack } from "@mantine/core";
import { useRouter } from "next/router";
import { ResourceTypeChips } from "../..";
import { startCase } from "lodash";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { IconHome } from "@tabler/icons-react";
import AppBreadCrumb from "@/components/AppBreadCrumb";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const ContentsPage = () => {
  const router = useRouter();
  const { resourceType, contentType } = router.query;
  const { t } = useTranslation();
  return (
    <Stack>
      <AppBreadCrumb
        links={[
          {
            label: t("common:contents"),
            href: "/contents",
          },
          {
            label: t(`common:${resourceType}`),
            href: `/contents/${resourceType}`,
          },
          {
            label: t(`common:${contentType}`),
            href: `/contents/${resourceType}/${contentType}`,
          },
        ]}
      />
      <Group>
        <ResourceTypeChips />
      </Group>
      <ContentsContainer
        resourceType={resourceType as EntityTypeEnum}
        contentType={[router.query.contentType as string]}
        title={`${startCase(
          contentType?.toString().toLowerCase().replace(/_/g, " ")
        )} | ${startCase(
          resourceType?.toString().toLowerCase().replace(/_/g, " ")
        )} | Contents | NepalTracks – Nepal’s Ultimate Political Tracker | Leaders, Elections & Parties`}
      />
    </Stack>
  );
};
export default ContentsPage;
