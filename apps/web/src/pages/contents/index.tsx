import ContentsContainer from "@/containers/ContentsContainer";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { ContentTypeIconMap } from "@/utils";
import { Chip, Group, Paper, Stack } from "@mantine/core";
import { useRouter } from "next/router";
import React from "react";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { useTranslation } from "react-i18next";
import AppBreadCrumb from "@/components/AppBreadCrumb";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export const ResourceTypeChips = () => {
  const router = useRouter();
  const { resourceType, contentType } = router.query;
  const { t } = useTranslation();

  return (
    <Stack align="start">
      <Chip.Group
        defaultValue={resourceType}
        onChange={(resourceType) => {
          router.push(`/contents/${resourceType}`);
        }}
      >
        <Group justify="center">
          <Chip value="ALL" size="sm">
            {t("common:all", { defaultValue: "All" })}
          </Chip>
          <Chip value="government" size="sm">
            {t("common:government", { defaultValue: "Governments" })}
          </Chip>
          <Chip value="parliament" size="sm">
            {t("common:parliament", { defaultValue: "Parliaments" })}
          </Chip>
          <Chip value="party" size="sm">
            {t("common:party", { defaultValue: "Parties" })}
          </Chip>
          <Chip value="leader" size="sm">
            {t("common:leader", { defaultValue: "Leaders" })}
          </Chip>
          <Chip value="department" size="sm">
            {t("common:department", { defaultValue: "Departments" })}
          </Chip>
          <Chip value="election" size="sm">
            {t("common:election", { defaultValue: "Elections" })}
          </Chip>
          <Chip value="media" size="sm">
            {t("common:medias", { defaultValue: "Medias" })}
          </Chip>
        </Group>
      </Chip.Group>

      {resourceType && (
        <Paper shadow="sm" p={"xs"}>
          <Chip.Group
            defaultValue={contentType}
            onChange={(contentType) => {
              router.push(`/contents/${resourceType}/${contentType}`);
            }}
          >
            <Group justify="center">
              <Chip value="ALL" variant="outline" size="xs">
                {t("common:all", { defaultValue: "All" })}
              </Chip>{" "}
              {Object.entries(ContentTypeIconMap).map(([key, Icon]) => (
                <Chip
                  key={key}
                  value={key.toLowerCase()}
                  variant="light"
                  size="xs"
                  radius={"sm"}
                >
                  <Group wrap="nowrap">
                    {/*  @ts-ignore */}
                    {React.isValidElement(Icon) ? Icon : <Icon />}
                    {t(`common:${key}`, {
                      defaultValue: key.replace(/_/g, " ").toLowerCase(),
                    })}
                  </Group>
                </Chip>
              ))}
            </Group>
          </Chip.Group>
        </Paper>
      )}
    </Stack>
  );
};
const ContentsPage = () => {
  const { t } = useTranslation();
  return (
    <Stack>
      <AppBreadCrumb
        links={[
          {
            label: t("common:contents"),
            href: "/contents",
          },
        ]}
      />

      <Group>
        <ResourceTypeChips />
      </Group>
      <ContentsContainer
        resourceType={EntityTypeEnum.ALL}
        title="Contents | NepalTracks – Nepal’s Ultimate Political Tracker|Leaders,Elections&Parties"
      />
    </Stack>
  );
};
export default ContentsPage;
