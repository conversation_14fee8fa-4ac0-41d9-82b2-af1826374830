import { useTranslation } from "next-i18next";
import { BackgroundImage, Box, Flex, Stack } from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { EntityInfo } from "@/components/EntityInfo";
import DisqusComment from "@/components/DisqusComment";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getContentMenus } from "@/containers/utils";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { useProfile } from "@/store";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import { useMemo } from "react";
import { IMedia } from "@/interfaces/IMedia";
import SummaryMD from "@/components/SummaryMD";
import { cleanUrl } from "@/utils";
import SocialLinks from "@/components/SocialLinks";

//@ts-expect-error
export const getServerSideProps: GetServerSideProps<{
  data?: IMedia;
}> = async (context) => {
  const mediaId = context?.params?.mediaId;
  const translation = await loadTranslation(context.locale!, "common");
  if (!mediaId)
    return {
      props: {
        data: null,
      },
    };
  const response = await ApiService.resource.getById<any>(
    "medias",
    mediaId + ""
  );

  return {
    props: {
      data: response?.data,
      rating: response?.data?.rating,
      ...translation,
    },
  };
};

const MediaDetail = (props: { data: IMedia & { rating: IReviewAnalysis } }) => {
  const router = useRouter();
  const { mediaId } = router.query;
  const profile = useProfile();
  useDynamicEntityStorage("medias", mediaId + "");
  const { t, i18n } = useTranslation();

  const metadata = useMemo(() => {
    return (
      (typeof props?.data?.metadata === "string"
        ? JSON.parse(props?.data?.metadata)
        : props?.data?.metadata) || {}
    );
  }, [props.data.metadata]);
  return (
    <>
      <DynamicSEO data={props.data} type="medias" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:medias"),
            href: "/medias",
          },
          {
            label: props.data?.localName,
            href: `/medias/${mediaId}`,
          },
        ]}
      />{" "}
      <Flex w={"100%"} direction="column" gap={10} pos={"relative"}>
        {props.data.coverImage && (
          <Box w={"100%"} h={{ base: "10rem", md: "15rem" }}>
            <BackgroundImage
              src={props.data.coverImage}
              h={{ base: "10rem", md: "15rem" }}
              bgp={"fill"}
            />
          </Box>
        )}
        <Flex direction={"column"} w={"100%"} gap={10}>
          <EntityInfo
            name={props?.data.localName}
            rate={+props?.data?.rating?.average}
            avatar={props?.data?.logo}
            id={mediaId as string}
            partyId={mediaId as string}
            address={cleanUrl(props?.data?.website)}
            renderRightSection={() => {
              return (
                <SocialLinks
                  links={{
                    facebook: props.data.fbPage,
                    twitter: props.data.twitterPage,
                    youtube: props.data.youtubePage,
                  }}
                />
              );
            }}
          />
          <SegmentedTab
            resources="medias"
            entityId={mediaId as string}
            defaultValue="medias"
            data={[
              {
                label: "Home",
                value: "home",
                content: (
                  <Stack>
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                      readMoreLink={`/medias/${mediaId}/summary`}
                    />
                    {/* @ts-expect-error */}
                    <ContentStatsCards stats={props?.data?.stats || {}} />

                    <Recommendations
                      disableSeeMore
                      entityType="medias"
                      entityId={mediaId + ""}
                    />
                    <RecentlySelfViewed
                      lists={["medias"]}
                      entityId={mediaId as string}
                    />
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings entityId={mediaId as string} entityType={"MEDIA"} />
                ),
              },
              {
                label: "summary",
                value: "summary",
                content: (
                  <SummaryMD
                    summary={props.data?.summary || ""}
                    summaryNP={props.data?.summaryNP || ""}
                  />
                ),
              },

              ...getContentMenus(
                mediaId as string,
                EntityTypeEnum.Media,
                //@ts-expect-error
                props.data.stats
              ),

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"media" + mediaId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default MediaDetail;
