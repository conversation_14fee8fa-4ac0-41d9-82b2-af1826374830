import { useTranslation } from "next-i18next";
import { Grid } from "@mantine/core";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import Link from "next/link";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { IMedia } from "@/interfaces/IMedia";
import { EntityInfo } from "@/components/EntityInfo";
import { cleanUrl } from "@/utils";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Medias = () => {
  const { t } = useTranslation();

  return (
    <>
      <DefaultIndexPage
        enableUrlChangeOnPageChange
        pageTitle={t("common:medias", { defaultValue: "Medias" })}
        entityType={EntityTypeEnum.Media}
        resource={"medias"}
        skeletonType="card"
        renderItems={(items: IMedia[]) => {
          return (
            <>
              {items.map((media) => (
                <Grid.Col
                  key={media.id}
                  span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                >
                  <EntityInfo
                    linkComponent={Link}
                    resources={"medias"}
                    partyId={media.id + ""}
                    id={media.id + ""}
                    name={media.name}
                    avatar={media.logo}
                    phone={media.contact}
                    rate={media.rating?.average}
                    address={cleanUrl(media.website)}
                  />
                </Grid.Col>
              ))}
            </>
          );
        }}
      />
    </>
  );
};
export default Medias;
