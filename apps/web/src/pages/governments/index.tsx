import { capitalize<PERSON>irstLetter } from "@/utils";
import { Box, Card, Group, Stack, Text, Title } from "@mantine/core";
import { IconHome } from "@tabler/icons-react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { loadTranslation } from "@/i18n";
import { GetServerSideProps, GetServerSidePropsContext } from "next";
import { useRouter } from "next/router";
import Head from "next/head";
export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export const GovernmentTypes = [
  {
    level: "FEDERAL",
    icon: IconHome,
    name: "federal_government",
    enabled: true,
  },

  // {
  //   level: "PROVINCIAL",
  //   icon: IconHome,
  //   name: "provincial_government",
  //   enabled: false,
  // },
  {
    level: "LOCAL",
    icon: IconHome,
    name: "local_government",
    enabled: !false,
  },
];

const Government = () => {
  const { t } = useTranslation();
  return (
    <>
      <Head>
        <title>
          {t("common:government")} | NepalTracks – Nepal’s Ultimate Political
          Tracker | Leaders, Elections & Parties
        </title>
      </Head>

      <Stack gap="md">
        {GovernmentTypes.map((type) => {
          const Icon = type.icon;
          return (
            <Link
              href={
                type.enabled
                  ? `/governments/level/${type.level.toLowerCase()}`
                  : "javascript:alert('Coming Soon')"
              }
              key={type.level}
              className="no-underline"
            >
              <Card key={type.level} shadow="xs" h={"7rem"} p={"md"}>
                <Group wrap="nowrap" h={"100%"} justify="space-around">
                  <Box>
                    <Icon />
                  </Box>

                  <Stack gap={5} justify="end" w={"100%"}>
                    <Stack gap={1}>
                      <Title order={3}>
                        {t(`common:${type.level}`, {
                          defaultValue: capitalizeFirstLetter(type.level),
                        })}
                      </Title>
                      <Text size="sm" c={"gray"}>
                        {t(`common:${type.name}`, {
                          defaultValue: capitalizeFirstLetter(type.name),
                        })}
                      </Text>
                    </Stack>
                  </Stack>
                </Group>
              </Card>
            </Link>
          );
        })}
      </Stack>
    </>
  );
};

export default Government;
