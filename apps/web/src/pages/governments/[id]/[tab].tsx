import { useTranslation } from "next-i18next";
import {
  Accordion,
  AvatarGroup,
  Badge,
  Box,
  Breadcrumbs,
  Button,
  Flex,
  Group,
  ScrollArea,
  Stack,
  Table,
  Tabs,
  Title,
} from "@mantine/core";
import { GetServerSideProps } from "next";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { IParty } from "@/interfaces/IParty";
import { EntityInfo } from "@/components/EntityInfo";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IGovernment } from "@/interfaces/IGovernment";
import { formatDate, getImageUrlWithFallback, truncateString } from "@/utils";
import DisqusComment from "@/components/DisqusComment";
import { loadTranslation } from "@/i18n";
import { Avatar, Paper, Text } from "@mantine/core";
import { GroupedStats } from "@/components/GroupedStats";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import Link from "next/link";
import { useQuery } from "react-query";
import { IGovernmentParty } from "@/interfaces/IGovernmentParty";
import { ICabinetMember } from "@/interfaces/ICabinetMember";
import { ICoaltion } from "@/interfaces/ICoaltion";
import { IGovernmentOverview } from "@/interfaces/IGovernmentOverview";
import { groupBy, sortBy, startCase } from "lodash";
import { useMemo } from "react";
import PartyHeroCard from "@/components/PartyHeroCard";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { CabinetDistributionByParty } from "@/containers/Sections/CabinetDistributionByParty";
import { getContentMenus } from "@/containers/utils";
import { UserInfoAction } from "@/components/UserInfoAction";
import ReactMarkdown from "react-markdown";
import SummaryMD from "@/components/SummaryMD";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import PartyProfile from "@/components/PartyProfile";
import { DynamicSEO } from "@/containers/DynamicSEO";
import { ApiService } from "../../../../api";
import { IconHome } from "@tabler/icons-react";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import TenureBadge from "@/components/TenureBadge";
import CabinetMemberProfile from "@/components/CabinerMemberProfile";
import ProjectProfile from "@/components/ProjectProfile";
import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import { ILeader } from "@/interfaces/ILeader";
import SuperControl from "@/components/SuperControl";
import LeaderInfoProfile from "@/components/LeaderInfoProfile";
import PoliticalDashboard from "@/components/GovernmentDataAnalyst";
import PoliticalDashboardA from "@/components/GovernmentDataAnalyst/component";
import DepartmentProfile from "@/components/DepartmentProfile";
import { useDisclosure } from "@mantine/hooks";
import DistanceFromNow from "@/components/DistanceFromNow";
import PollsContainer from "@/containers/PollContainer";

function getPartyCabinetMembers(item: ICoaltion) {
  const addedItems: (number | undefined)[] = [];
  const grouped = groupBy(item.members, "leader.id");
  const sortedMembers = item.members.sort((a, b) => {
    return (a.department?.rank ?? Infinity) - (b.department?.rank ?? Infinity);
  });
  const uniqueMembers = sortedMembers
    .filter((item, index) => {
      if (addedItems.includes(item.leader.id)) return false;
      addedItems.push(item.leader.id);
      return true;
    })
    .map((item) => ({
      ...item,
      additionalCabinets: grouped[item.leader.id]
        .filter((cur: any) => item.cabinet_member.id !== cur.cabinet_member.id)
        .map((item: any) => ({
          ...item.cabinet_member,
          department: item.department,
          leaders: item.leader,
        })),
    }));

  return uniqueMembers;
}

export const getServerSideProps: GetServerSideProps<{
  data?: IParty;
}> = async (context) => {
  const { id: governmentId } = context?.params as {
    level: string;
    id: string;
  };
  if (!governmentId)
    return {
      props: {
        data: null,
      },
    };
  const translation = await loadTranslation(context.locale!, "common");
  const [level, id] = governmentId.split(":");
  const response =
    level === "local"
      ? await ApiService.resource.getById<any>(`geo-municipals`, id + "")
      : await ApiService.resource.getById<any>(
          `governments`,
          governmentId + ""
        );
  return {
    props: {
      ...translation,
      data: response?.data,
    },
  };
};
const PartyDetail = (props: { data: IGovernment; rating: IReviewAnalysis }) => {
  const router = useRouter();
  const { data: goverment } = props;
  const { id: governmentId, level } = router.query;
  const governmentOverviewQuery = {
    data: { data: props.data.governmentOverview },
    isLoading: false,
  };

  const governmentOverview = governmentOverviewQuery.data
    ?.data as unknown as IGovernmentOverview;

  const headsOfState = useMemo(() => {
    const grouped = groupBy(goverment.cabinet_members, "leaderId");
    const addedItems: (number | undefined)[] = [];
    const result = goverment.cabinet_members
      .sort(
        (a, b) =>
          ((a.rank || a.department?.rank) ?? Infinity) -
          ((b.rank || b.department?.rank) ?? Infinity)
      )
      ?.filter((item) => {
        if (addedItems.includes(item.leaderId)) return false;
        addedItems.push(item.leaderId);

        const rank = item.rank || item.department?.rank || 4;
        return rank <= 3 && item.role !== "STATE_MINISTER";
      })

      .map((item) => ({
        ...item,
        leaders: {
          ...item.leaders,
          // @ts-expect-error
          additionalCabinets: grouped[item.leaderId].filter(
            (cur: ICabinetMember) => item.id !== cur.id
          ),
        },
      }));
    return result;
  }, [goverment]);

  const colationsByMemebers = useMemo(() => {
    const data = governmentOverview?.coaltion.sort((a, b) => {
      return b.members.length - a.members.length;
    });
    const addedItems: (number | undefined)[] = [];

    return data
      .filter((item) => {
        if (addedItems.includes(item.party.id)) return false;
        addedItems.push(item.party.id);
        return true;
      })
      .map((item) => ({
        ...item,
        members: getPartyCabinetMembers(item),
      }));
  }, [governmentOverview?.coaltion]);

  const cabinetDistribution = useMemo(() => {
    const allmembers = governmentOverview?.coaltion.flatMap(
      (item) => item.members
    );
    return (
      governmentOverview?.coaltion?.map((item) => {
        return {
          label: item.party.localName,
          count: item.members.length,
          part: +((item.members.length / allmembers.length) * 100).toFixed(2),
          color: `${item.party.partyColorCode}`,
          img: item.party.logo,
        };
      }) || []
    );
  }, [governmentOverview?.coaltion]);
  useDynamicEntityStorage("governments", governmentId + "");
  const groupedCabinetMembers = useMemo(() => {
    if (!Array.isArray(goverment?.cabinet_members)) return [];

    const sorted = sortBy(
      goverment.cabinet_members,
      (member) => member?.department?.rank ?? Infinity
    );

    const grouped = groupBy(sorted, "leaderId");

    return (
      Object.entries(grouped)
        .sort((a, b) => {
          // Optional: sort by first member's department rank
          const rankA = a[1][0]?.department?.rank ?? Infinity;
          const rankB = b[1][0]?.department?.rank ?? Infinity;
          return rankA - rankB;
        })
        .map(([leaderId, members]) => ({ leaderId, members })) || []
    );
  }, [goverment?.cabinet_members]);

  const { t } = useTranslation();

  return (
    <>
      <DynamicSEO data={props.data} type="governments" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:government"),
            href: "/governments",
          },
          {
            // label: goverment.name,
            label: t(
              `common:${props.data.government_type.toLowerCase()}_government`,
              props.data.government_type.toLowerCase()
            ),
            href: `/governments/level/${props.data.government_type.toLowerCase()}`,
          },
        ]}
      />

      <Flex w={"100%"} direction="column" gap={10}>
        <EntityInfo
          resources="governments"
          name={
            <Stack gap={0} justify="flex-start" align="flex-start">
              {props?.data?.name}
              <Link
                href={`/leaders/${props.data?.head?.id}`}
                className="text-inherit no-underline"
              >
                <Text size="sm" c="dimmed">
                  {props.data?.head?.localName}
                </Text>
              </Link>
            </Stack>
          }
          avatarProps={{ size: "xl" }}
          rate={+props?.rating?.average}
          // avatar="https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
          avatar={
            props.data?.img ||
            props.data?.head?.img ||
            getImageUrlWithFallback(null, props.data?.head?.ecCandidateID)
          }
          id={governmentId as string}
          partyId={governmentId as string}
          title={
            <Group>
              <Text>
                {t(`common:${goverment.government_type.toLowerCase()}`, {
                  defaultValue: goverment.government_type,
                })}{" "}
                {t("common:government", { defaultValue: "Government" })}
              </Text>
              {!goverment.endAt && (
                <Badge color={"green"}>{t("common:current")}</Badge>
              )}
            </Group>
          }
          renderInformation={() => (
            <Stack gap={0} mt={"sm"}>
              <TenureBadge
                startedAt={governmentOverview?.government.startedAt}
                endAt={governmentOverview?.government.endAt}
              />{" "}
              <Box ml={"-xs"}>
                <DistanceFromNow
                  startedAt={governmentOverview?.government.startedAt}
                />
              </Box>
            </Stack>
          )}
          renderRightSection={() => (
            <>
              <GroupedStats
                data={[
                  {
                    title: t("common:cabinet_members", {
                      defaultValue: "Members",
                    }),
                    stats:
                      governmentOverview?.coaltion.flatMap(
                        (item) => item.members
                      ).length + "",
                  },
                  {
                    title: t("common:started_at", { defaultValue: "Started" }),
                    stats:
                      formatDate(governmentOverview?.government.startedAt) + "",
                  },
                  {
                    title: t("common:end_at", { defaultValue: "Ended" }),
                    stats: governmentOverview?.government.endAt
                      ? formatDate(governmentOverview?.government.endAt) + ""
                      : "N/A" + "",
                  },
                  // { title: "Vote of Confidence", stats: "123" },
                  {
                    title: t("common:coalition_parties", {
                      defaultValue: "Parties",
                    }),
                    stats: governmentOverview?.coaltion.length + "",
                  },
                ]}
              />
            </>
          )}
        />

        <Flex direction={"column"} w={"100%"} gap={10}>
          <SegmentedTab
            resources={"governments/"}
            entityId={governmentId as string}
            defaultValue="home"
            data={[
              {
                label: "Overview",
                value: "home",
                content: (
                  <Stack>
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                      readMoreLink={`/governments/${props.data.id}/summary`}
                    />{" "}
                    <FeaturedSlider<ICabinetMember>
                      fullWidthOnSP
                      title={t("common:state_heads_count", {
                        defaultValue: "State heads",
                        count: headsOfState?.length,
                      })}
                      // @ts-expect-error
                      data={headsOfState || []}
                      renderItems={function (
                        member: ICabinetMember
                      ): JSX.Element {
                        return (
                          <CabinetMemberProfile
                            data={member}
                            variant="compact"
                          />
                        );
                      }}
                    />{" "}
                    <FeaturedSlider<IGovernmentParty>
                      isLoading={governmentOverviewQuery.isLoading}
                      title={`${t("common:coalition_parties", {
                        defaultValue: "Coalition parties",
                      })} (${governmentOverview?.coaltion?.length})`}
                      data={governmentOverview?.coaltion || []}
                      renderItems={function ({
                        party: item,
                      }: IGovernmentParty): JSX.Element {
                        return <PartyProfile data={item} />;
                      }}
                    />
                    <ContentStatsCards
                      stats={governmentOverview?.stats || {}}
                    />
                    <Stack>
                      <AsyncFeatureSlider
                        // @ts-expect-error
                        transformResponse={(data) => data?.items || []}
                        fullWidthOnSP
                        entityId={governmentId as string}
                        title={t(
                          "common:hot_cabinet_members",
                          "Hot Cabinet Leaders"
                        )}
                        entityType="governments"
                        resourceUrl={`governments/${governmentId}/hot-cabinet-members`}
                        renderItems={(item: ILeader) => {
                          return <LeaderInfoProfile data={item} />;
                        }}
                      />
                      <Stack>
                        <Title order={4}>
                          {t("common:cabinet_distributions")}
                        </Title>
                        <CabinetDistributionByParty
                          data={cabinetDistribution}
                        />
                      </Stack>
                      {/* <StatsSegments /> */}
                      <Stack>
                        <Title order={4}>{t("common:cabinet_members")}</Title>

                        <Accordion defaultValue={"top"}>
                          {colationsByMemebers.map((item, index) => (
                            <Accordion.Item
                              value={item.party.id + ""}
                              key={index}
                            >
                              <Accordion.Control>
                                <Group justify="space-between" wrap="nowrap">
                                  <Group wrap="nowrap">
                                    <Avatar src={item.party.logo} radius="xs" />
                                    <Text
                                      fz="xs"
                                      tt="uppercase"
                                      fw={700}
                                      c="dimmed"
                                    >
                                      {item.party.localName} (
                                      {item.members.length})
                                    </Text>
                                  </Group>

                                  <AvatarGroup>
                                    {item.members.slice(0, 3).map((member) => (
                                      <Avatar
                                        alt={member.leader.localName}
                                        size={"sm"}
                                        key={member.leader.id}
                                        src={getImageUrlWithFallback(
                                          member.leader.img,
                                          member.leader.ecCandidateID + ""
                                        )}
                                        radius={100}
                                      />
                                    ))}
                                    {item.members.length > 3 && (
                                      <Avatar size={"sm"}>
                                        +{item.members.length - 3}
                                      </Avatar>
                                    )}
                                  </AvatarGroup>
                                </Group>
                              </Accordion.Control>
                              <Accordion.Panel>
                                <FeaturedSlider
                                  title={`${item.party.localName} (${item.members.length})`}
                                  fullWidthOnSP
                                  data={item.members}
                                  renderItems={(member) => {
                                    return (
                                      <CabinetMemberProfile
                                        data={{
                                          ...member.cabinet_member,
                                          leaders: {
                                            ...member.leader,
                                            additionalCabinets:
                                              member.additionalCabinets,
                                          },
                                          department: member.department,
                                        }}
                                        variant="compact"
                                      />
                                    );
                                  }}
                                />
                              </Accordion.Panel>
                            </Accordion.Item>
                          ))}
                        </Accordion>
                      </Stack>

                      <Stack>
                        <Title order={4}>
                          {t("common:department_distributions")}
                        </Title>
                        <Accordion>
                          {governmentOverview?.coaltion.map((item, index) => (
                            <Accordion.Item value={item.party.id + ""}>
                              <Accordion.Control>
                                <Group justify="space-between" wrap="nowrap">
                                  <Group wrap="nowrap">
                                    <Avatar
                                      src={item.party.logo}
                                      size={48}
                                      radius="xs"
                                    />
                                    <Text
                                      fz="xs"
                                      tt="uppercase"
                                      fw={700}
                                      c="dimmed"
                                    >
                                      {item.party.localName} (
                                      {item.members.length})
                                    </Text>
                                  </Group>

                                  <Group className="line-clamp-3" wrap="nowrap">
                                    <Text
                                      size="xs"
                                      c={"dimmed"}
                                      className="italic"
                                    >
                                      {item.members[0].department.name}...
                                    </Text>
                                  </Group>
                                </Group>
                              </Accordion.Control>
                              <Accordion.Panel>
                                <FeaturedSlider
                                  title={`${item.party.localName} (${item.members.length})`}
                                  fullWidthOnSP
                                  data={item.members}
                                  renderItems={(member) => {
                                    return (
                                      <DepartmentProfile
                                        data={member.department}
                                        renderAfterContent={() => {
                                          return (
                                            <Stack gap={1}>
                                              <Text size="xs" fw={600}>
                                                {member.leader.name}
                                              </Text>
                                              <TenureBadge
                                                startedAt={
                                                  member.cabinet_member
                                                    .startedAt
                                                }
                                                endAt={
                                                  member.cabinet_member.endAt
                                                }
                                              />
                                            </Stack>
                                          );
                                        }}
                                      />
                                    );
                                  }}
                                />
                              </Accordion.Panel>
                            </Accordion.Item>
                          ))}
                        </Accordion>
                      </Stack>
                    </Stack>
                    <FeaturedSlider
                      fullWidthOnSP
                      title={"Related Projects"}
                      data={props.data?.projects || []}
                      renderItems={(item) => {
                        return <ProjectProfile data={item} />;
                      }}
                    />
                    <PoliticalDashboardA root={goverment} />
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings
                    entityId={governmentId as string}
                    entityType={EntityTypeEnum.Government}
                  />
                ),
              },
              {
                label: "Polls",
                value: "polls",
                content: (
                  <PollsContainer
                    entityId={governmentId + ""}
                    entityType={EntityTypeEnum.Government}
                  />
                ),
              },
              {
                label: "summary",
                value: "summary",
                content: (
                  <SummaryMD
                    summary={props.data?.summary || ""}
                    summaryNP={props.data?.summaryNP || ""}
                  />
                ),
              },

              {
                label: "parties",
                value: "parties",
                content: (
                  <>
                    <ScrollArea w={"100%"}>
                      <Table style={{ minWidth: 800 }} verticalSpacing="sm">
                        <Table.Thead>
                          <Table.Tr>
                            <Table.Th>{t("common:party")}</Table.Th>
                            <Table.Th>{t("common:cabinet_members")}</Table.Th>
                          </Table.Tr>
                        </Table.Thead>
                        <Table.Tbody>
                          {governmentOverview?.coaltion?.map?.(
                            (item: ICoaltion, index: number) => (
                              <Table.Tr key={index}>
                                <Table.Td>
                                  <Link
                                    className="no-underline text-inherit"
                                    href={`/parties/${item.party.id}`}
                                  >
                                    <Group gap="sm">
                                      <Avatar
                                        size={"md"}
                                        src={item.party.logo}
                                      />
                                      <div>
                                        <Text fz="sm" fw={500}>
                                          {item.party.localName}
                                        </Text>
                                      </div>
                                    </Group>
                                  </Link>
                                </Table.Td>
                                <Table.Td>
                                  <Text fz="xs">{item.members.length}</Text>
                                </Table.Td>
                              </Table.Tr>
                            )
                          )}
                        </Table.Tbody>
                      </Table>
                    </ScrollArea>
                  </>
                ),
              },
              {
                label: "cabinet_members",
                value: "cabinet_members",
                content: (
                  <ScrollArea w={"100%"}>
                    <Table style={{ minWidth: 800 }} verticalSpacing="sm">
                      <Table.Thead>
                        <Table.Tr>
                          <Table.Th>{t("common:leader")}</Table.Th>
                          <Table.Th>{t("common:position")}</Table.Th>
                          <Table.Th>{t("common:department")}</Table.Th>
                          <Table.Th>{t("common:started_at")}</Table.Th>
                        </Table.Tr>
                      </Table.Thead>
                      <Table.Tbody>
                        {groupedCabinetMembers?.map?.((cabinet_member) =>
                          cabinet_member.members.map((item, index) => (
                            <Table.Tr
                              key={item.id}
                              className={`divide-none ${
                                cabinet_member.members.length > 1 &&
                                index !== cabinet_member.members.length - 1
                                  ? "border-b-0"
                                  : ""
                              }`}
                            >
                              <Table.Td>
                                {index === 0 && (
                                  <Link
                                    className="no-underline text-inherit"
                                    href={`/leaders/${item.leaders?.id}`}
                                  >
                                    <Group gap="sm" wrap="nowrap">
                                      <Avatar
                                        size={"md"}
                                        src={getImageUrlWithFallback(
                                          item.leaders?.img,
                                          item.leaders?.ecCandidateID || ""
                                        )}
                                      />
                                      <div>
                                        <Text fz="sm" fw={500}>
                                          {item.leaders?.name}
                                        </Text>
                                      </div>
                                    </Group>
                                  </Link>
                                )}
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {t(`common:${item?.role?.toLowerCase?.()}`, {
                                    defaultValue: item.role,
                                  })}
                                </Text>
                              </Table.Td>
                              <Table.Td>
                                <Group gap="xs" wrap="nowrap">
                                  <Avatar
                                    size={"md"}
                                    src={item.department?.logo}
                                  />
                                  <Text fz="xs">{item.department?.name}</Text>
                                </Group>
                              </Table.Td>
                              <Table.Td>
                                <Text fz="xs">
                                  {item.startedAt
                                    ? formatDate(item.startedAt)
                                    : ""}
                                </Text>
                                <SuperControl
                                  entityId={item.id + ""}
                                  newData={{
                                    leaders: item.leaders?.id,
                                    departmentId: item.department?.id,
                                    governmentId: item.governmentId,
                                  }}
                                  entityType="cabinet_members"
                                />
                              </Table.Td>
                            </Table.Tr>
                          ))
                        )}
                      </Table.Tbody>
                    </Table>
                  </ScrollArea>
                ),
              },
              ...getContentMenus(
                governmentId as string,
                EntityTypeEnum.Government,
                governmentOverview?.stats
              ),
              // {
              //   label: "Elections",
              //   value: "elections",
              //   content: <ElectionInTab partyId={+(governmentId || 0)} />,
              // },

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"government" + governmentId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default PartyDetail;
