import { useTranslation } from "next-i18next";
import { EntityInfo } from "@/components/EntityInfo";
import {
  Avatar,
  AvatarGroup,
  Box,
  Breadcrumbs,
  Card,
  Grid,
  Group,
  Rating,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { IDistricts } from "@/interfaces/IElectionSubResponse";
import { IconHome } from "@tabler/icons-react";
import Link from "next/link";
import { IGovernment } from "@/interfaces/IGovernment";
import { loadTranslation } from "@/i18n";
import { GetServerSidePropsContext } from "next";
import { useRouter } from "next/router";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import MunicipalProfile from "@/components/MunicipalProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

const Government = ({ context }: { context: GetServerSidePropsContext }) => {
  const router = useRouter();
  const { level } = router.query;
  const { t } = useTranslation();

  return (
    <Stack>
      <AppBreadCrumb
        links={[
          {
            label: t("common:government"),
            href: "/governments",
          },
        ]}
      />

      <DefaultIndexPage
        pageTitle={t(`common:${level}_government`)}
        entityType={EntityTypeEnum.Government}
        limit={20}
        resource={`governments/level/${level}`}
        renderItems={(items: IGovernment[]) => {
          return (
            <Grid>
              {items.map((item) => {
                return (
                  <Grid.Col span={{ base: 12, md: 3 }}>
                    {level === "local" ? (
                      // @ts-expect-error
                      <MunicipalProfile data={item} />
                    ) : (
                      <GovernmentProfile data={item} />
                    )}
                    {/* <MunicipalProfile data={item} />
                      <Card shadow="xs" h={"7rem"} p={"lg"} miw={"20rem"}>
                        <Group wrap="nowrap" h={"100%"} justify="space-around">
                          <Box>
                            <IconHome />
                          </Box>

                          <Stack gap={5} justify="end" w={"100%"}>
                            <Stack gap={1}>
                              <Title order={3}>
                                {item.localName || item.name}
                              </Title>
                              <Text size="sm" c={"gray"}>
                                {item.head?.localName}
                              </Text>
                            </Stack>
                          </Stack>
                        </Group>
                      </Card> */}
                  </Grid.Col>
                );
              })}
            </Grid>
          );
        }}
      ></DefaultIndexPage>
    </Stack>
  );
};
export default Government;
