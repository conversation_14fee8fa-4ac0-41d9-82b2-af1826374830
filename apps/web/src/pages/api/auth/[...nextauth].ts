import NextAuth from "next-auth"
import GithubProvider from "next-auth/providers/github"
import GoogleProvider from "next-auth/providers/google"
import FacebookProvider from "next-auth/providers/facebook"

export const authOptions = {
    // Configure one or more authentication providers
    providers: [
        // GithubProvider({
        //     clientId: process.env.GITHUB_ID,
        //     clientSecret: process.env.GITHUB_SECRET,
        // }),
        // GoogleProvider({
        //     clientId: "967750942304-ssmu4dbpptjg22h6mfkrkrng4qnn85k2.apps.googleusercontent.com",
        //     clientSecret: "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",

        // }),
        // FacebookProvider({
        //     clientId: process.env.GITHUB_ID,
        //     clientSecret: process.env.GITHUB_SECRET,
        // }),
        // ...add more providers here
    ],
}

export default NextAuth(authOptions)