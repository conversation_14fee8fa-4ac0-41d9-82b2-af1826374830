import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  Group,
  Paper,
  Button,
  Skeleton,
  Stack,
  Box,
  ComboboxStore,
  LoadingOverlay,
  Select,
  Avatar,
} from "@mantine/core";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderCompare from "@/components/LeaderCompare";
import { IconPlus } from "@tabler/icons-react";
import SearchBox from "@/components/SearchBox";
import { ApiService } from "../../../api";
import { ILeader } from "@/interfaces/ILeader";
import { useRouter } from "next/router";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { Carousel } from "@mantine/carousel";
import { DynamicSEO } from "@/containers/DynamicSEO";
import Recommendations from "@/containers/Recommendations";
import { RecommendationBadges } from "@/containers/RecommendationBadges";
import { getImageUrlWithFallback } from "@/utils";
import Ratings from "@/containers/Ratings";
import DisqusComment from "@/components/DisqusComment";
import SegmentedTab from "@/components/SegmentedTab";
import { sortBy } from "lodash";
import { loadTranslation } from "@/i18n";
import { GetServerSideProps } from "next";
import { useTranslation } from "react-i18next";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

export default function LeaderDetail() {
  const [isLoading, setIsLoading] = useState(false);
  const [recommendedLeaderId, setRecommendedLeaderId] = useState(0);
  const router = useRouter();
  const { candidates } = router.query;
  const { t } = useTranslation();

  const [leaders, setLeaders] = useState<ILeader[]>([]);

  const handleLeaderSelect = useCallback(
    async (resource: { id: number }, store?: ComboboxStore) => {
      if (leaders.find((item) => item.id === resource.id)) return;
      await getLeaders(resource.id);
      store?.closeDropdown();
      store?.resetSelectedOption();
      setRecommendedLeaderId(resource.id);
      router.replace(`/compare`, {
        search: `?candidates=${[...leaders, { id: resource.id }]
          .map((item) => item.id)
          .join(",")}`,
      });
    },
    [leaders]
  );

  const getLeaders = useCallback(async (leaderId: number) => {
    setIsLoading(true);
    try {
      const { data } = await ApiService.getLeader(leaderId + "");
      setLeaders((leaders) => {
        return [...leaders, data.data];
      });
    } catch {
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (leaders.length > 5) {
      setLeaders(leaders.slice(0, 5));
      alert("You can only compare 5");
    }
  }, [leaders]);

  useEffect(() => {
    const leaders = new Set(
      //@ts-expect-error
      (candidates?.split(",") || [])
        .filter((item: string | number) => {
          return !isNaN(+item);
        })
        .filter(Boolean)
    );
    if (!leaders.size) return;

    let leaderId;
    (async function () {
      for (const leader of Array.from(leaders)) {
        leaderId = leader;
        //@ts-expect-error
        await getLeaders(leader);
      }
      setRecommendedLeaderId(leaderId as number);
    })().catch(() => {
      alert("Unable to fetch.");
    });
  }, [candidates]);

  const handleOnRemove = useCallback((index: number) => {
    return (data: ILeader) => {
      setLeaders((leaders) => {
        const nextLeaders = leaders.filter((item) => item.id !== data.id);
        router.replace(`/compare`, {
          search: `candidates=${nextLeaders.map((item) => item.id).join(",")}`,
        });
        setRecommendedLeaderId(nextLeaders[nextLeaders.length - 1]?.id);
        return nextLeaders;
      });
    };
  }, []);

  const uniqueIdentifier = useMemo(() => {
    return leaders?.length
      ? sortBy(leaders.map((item) => item.id)).join("-")
      : undefined;
  }, [leaders]);
  return (
    <>
      <DynamicSEO
        // @ts-expect-error
        type="Compare"
        data={{
          name: "Leaders, Parties, Governments, Wards, Municipals",
          localName: "Compare",
          description: "Compare your leaders",
        }}
      />
      <Stack>
        <LoadingOverlay visible={isLoading} />
        <FeaturedSlider
          fullWidthOnSP
          data={leaders}
          carouselProps={{
            slideSize: {
              base: "100%",
              md: "30%",
            },
          }}
          title={t("common:compare_leaders", {
            defaultValue: "Compare leaders",
          })}
          renderItems={(leader, index) => {
            return (
              <Box
                className="animate__animated animate__fadeIn"
                key={index}
                h={"50rem"}
              >
                <LeaderCompare
                  leader={leader as any}
                  onRemove={handleOnRemove(index)}
                />
              </Box>
            );
          }}
          renderAfterData={() => {
            return (
              <>
                {isLoading ? <Skeleton w={"25rem"} h="50rem" /> : null}
                <Carousel.Slide>
                  <Stack h="50rem" justify="center" w={"22rem"} align="center">
                    <Recommendations
                      disableSeeMore
                      entityId={recommendedLeaderId + ""}
                      entityType="leaders"
                      // @ts-expect-error
                      renderChild={(data: unknown[], options) => {
                        if (!data?.length) return;
                        if (options.isLoading)
                          return <Skeleton w={"100%"} h={"3rem"} />;
                        return (
                          <Avatar.Group>
                            {data.slice(0, 5).map((item) => (
                              <Avatar
                                onClick={() => {
                                  document
                                    .getElementById("recommendations")
                                    ?.scrollIntoView({
                                      behavior: "smooth",
                                    });
                                }}
                                size={42}
                                src={getImageUrlWithFallback(
                                  // @ts-expect-error
                                  item.img,
                                  // @ts-expect-error
                                  item.ecCandidateID
                                )}
                              />
                            ))}
                            <Avatar>+{data.length}</Avatar>
                          </Avatar.Group>
                        );
                      }}
                    />
                    <Paper
                      w={"100%"}
                      className="min-h-50 justify-center items-center flex"
                      h={"20rem"}
                      radius={"md"}
                      shadow="sm"
                      p={"md"}
                    >
                      <Stack justify="center" w={"100%"} align="center">
                        <IconPlus size={42} color="gray" />
                        <SearchBox
                          entities={[EntityTypeEnum.Leader]}
                          //@ts-expect-error
                          onSelect={handleLeaderSelect}
                          inputProps={{
                            //@ts-expect-error
                            placeholder: t("common:add_leader_to_compare", {
                              defaultValue: "Add leader to compare",
                            }),
                          }}
                        />
                      </Stack>
                    </Paper>
                  </Stack>
                </Carousel.Slide>
              </>
            );
          }}
        />
        {recommendedLeaderId ? (
          <Box id="recommendations">
            <Recommendations
              // @ts-expect-error
              title={
                <Group>
                  {t("common:recommendations_for", {
                    defaultValue: "Recommendations for",
                  })}{" "}
                  <Select
                    value={recommendedLeaderId + ""}
                    onChange={(_value, option) =>
                      setRecommendedLeaderId(+option.value)
                    }
                    data={leaders.map((item) => ({
                      value: item.id + "",
                      label: `${item.localName} (${item.name})`,
                    }))}
                  />
                </Group>
              }
              disableSeeMore
              entityId={recommendedLeaderId + ""}
              entityType="leaders"
              renderDescription={(leader) => {
                return (
                  <Stack w={"100%"} justify="center" gap={2}>
                    <RecommendationBadges reasons={[leader.topic]} />
                    <br />
                    <Button
                      variant="light"
                      size="sm"
                      fullWidth
                      onClick={() => {
                        handleLeaderSelect({ id: leader.id });
                        scrollTo(0, 0);
                      }}
                    >
                      {" "}
                      {t("common:add_to_compare", {
                        defaultValue: "Add to compare",
                      })}
                    </Button>
                  </Stack>
                );
              }}
            />
          </Box>
        ) : null}

        {uniqueIdentifier ? (
          <SegmentedTab
            disableRouting
            resources={"compare"}
            entityId={"" as string}
            defaultValue="/"
            data={[
              {
                label: "reviews",
                value: "/",
                content: (
                  <Ratings
                    entityId={uniqueIdentifier as string}
                    entityType={EntityTypeEnum.COMPARE}
                  />
                ),
              },
              {
                label: "discussions",
                value: "/discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={EntityTypeEnum.COMPARE + uniqueIdentifier}
                  />
                ),
              },
            ]}
          />
        ) : null}
      </Stack>
    </>
  );
}
