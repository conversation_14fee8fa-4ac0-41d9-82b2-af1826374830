import { useTranslation } from "next-i18next";
import { Box, Grid, Group, Paper, Progress, Stack } from "@mantine/core";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import Link from "next/link";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { EntityInfo } from "@/components/EntityInfo";
import { IProject } from "@/interfaces/IProject";
import TenureBadge from "@/components/TenureBadge";
import ProjectProfile from "@/components/ProjectProfile";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Projects = () => {
  const { t } = useTranslation();

  return (
    <>
      <DefaultIndexPage
        enableUrlChangeOnPageChange
        pageTitle={t("common:projects", { defaultValue: "Projects" })}
        entityType={EntityTypeEnum.Project}
        resource={"projects"}
        skeletonType="card"
        renderItems={(items: IProject[]) => {
          return (
            <>
              {items.map((project) => (
                <Grid.Col
                  key={project.id}
                  span={{ base: 12, sm: 6, md: 4, lg: 3 }}
                >
                  <ProjectProfile data={project} />
                </Grid.Col>
              ))}
            </>
          );
        }}
      />
    </>
  );
};
export default Projects;
