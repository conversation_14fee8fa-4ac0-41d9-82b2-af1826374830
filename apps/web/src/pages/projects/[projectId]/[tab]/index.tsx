import { useTranslation } from "next-i18next";
import { BackgroundImage, Box, Flex, Stack } from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { EntityInfo } from "@/components/EntityInfo";
import DisqusComment from "@/components/DisqusComment";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getContentMenus } from "@/containers/utils";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import { useProfile } from "@/store";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import { useMemo } from "react";
import { IMedia } from "@/interfaces/IMedia";
import SummaryMD from "@/components/SummaryMD";
import { IProject } from "@/interfaces/IProject";
import ProjectCard from "@/components/ProjectCard";
import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import RenderResourceType from "@/containers/RenderResourceType";
import { FeaturedSlider } from "@/components/FeatutredSlider";

//@ts-expect-error
export const getServerSideProps: GetServerSideProps<{
  data?: IMedia;
}> = async (context) => {
  const projectId = context?.params?.projectId;
  const translation = await loadTranslation(context.locale!, "common");
  if (!projectId)
    return {
      props: {
        data: null,
      },
    };
  const response = await ApiService.resource.getById<any>(
    "projects",
    projectId + ""
  );

  return {
    props: {
      data: response?.data,
      rating: response?.data?.rating,
      ...translation,
    },
  };
};

const ProjectDetail = (props: {
  data: IProject & { rating: IReviewAnalysis };
}) => {
  const router = useRouter();
  const { projectId } = router.query;
  const profile = useProfile();
  useDynamicEntityStorage("projects", projectId + "");
  const { t, i18n } = useTranslation();

  return (
    <>
      <DynamicSEO data={props.data} type="projects" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:projects"),
            href: "/projects",
          },
          {
            label: props.data?.name,
            href: `/projects/${projectId}`,
          },
        ]}
      />{" "}
      <Flex w={"100%"} direction="column" gap={10} pos={"relative"}>
        <Flex direction={"column"} w={"100%"} gap={10}>
          {/* @ts-expect-error */}
          <ProjectCard project={props.data} />
          {/* <EntityInfo
            name={props?.data.name}
            rate={+props?.data?.rating?.average}
            avatar={props?.data?.image}
            id={projectId as string}
            partyId={projectId as string}
          /> */}

          <SegmentedTab
            resources="projects"
            entityId={projectId as string}
            defaultValue="projects"
            data={[
              {
                label: "home",
                value: "home",
                content: (
                  <Stack>
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                      readMoreLink={`/projects/${projectId}/summary`}
                    />
                    {/* @ts-expect-error */}
                    <ContentStatsCards stats={props?.data?.stats || {}} />
                    <FeaturedSlider
                      title={t("common:related_to_this_project", {
                        defaultValue: "Related to this project",
                      })}
                      // @ts-expect-error
                      data={props.data?.projectsBy || []}
                      renderItems={(item) => {
                        return (
                          <RenderResourceType
                            // @ts-expect-error
                            resourceType={item.resourceType}
                            // @ts-expect-error
                            resource={item.resource}
                          />
                        );
                      }}
                    />

                    <Recommendations
                      fullWidthOnSP
                      disableSeeMore
                      entityType="projects"
                      entityId={projectId + ""}
                    />
                    <RecentlySelfViewed
                      fullWidthOnSP
                      lists={["projects"]}
                      entityId={projectId as string}
                    />
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings
                    entityId={projectId as string}
                    entityType={"PROJECT"}
                  />
                ),
              },
              {
                label: "summary",
                value: "summary",
                content: (
                  <SummaryMD
                    summary={props.data?.summary || ""}
                    summaryNP={props.data?.summaryNP || ""}
                  />
                ),
              },

              ...getContentMenus(
                projectId as string,
                EntityTypeEnum.Project,
                //@ts-expect-error
                props.data.stats
              ),

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"media" + projectId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default ProjectDetail;
