// pages/feed/index.tsx

import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { useQuery } from "react-query";
import {
  Stack,
  Title,
  Alert,
  Loader,
  Center,
  Skeleton,
  SimpleGrid,
  Grid,
  Badge,
} from "@mantine/core";
import Head from "next/head";
import { startCase } from "lodash";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import PartyProfile from "@/components/PartyProfile";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import WardProfile from "@/components/WardProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
import ElectionProfile from "@/components/ElectionProfile";
import ParliamentProfile from "@/components/ParliamentProfile";
import ContentProfile from "@/components/ContentProfile";
import ElectionSubProfile from "@/components/ElectionSubProfile";
import RatingProfile from "@/components/RatingProfile";
import { ApiService } from "../../../api";
import InfiniteScroll from "react-infinite-scroll-component";
import React, { useEffect, useMemo, useRef } from "react";
import { RenderSocialMediaContent } from "@/containers/SocialMediaContent";
import MediaProfile from "@/components/MediaProfile";
import { useDebounce } from "use-debounce";
import { ILeader } from "@/interfaces/ILeader";
import ProjectProfile from "@/components/ProjectProfile";
import DepartmentProfile from "@/components/DepartmentProfile";
import MunicipalProfile from "@/components/MunicipalProfile";
import withDynamicProfile from "@/containers/withDnamicProfile";

const componentMap = {
  [EntityTypeEnum.Leader]: [
    LeaderHeroProfile,
    () => ({
      renderBeforeAvatar({ data }: { data: ILeader }) {
        if (data?.candidacyType)
          return <Badge>{data?.candidacyType.localName}</Badge>;
        return null;
      },
    }),
  ],
  [EntityTypeEnum.Party]: [PartyProfile],
  [EntityTypeEnum.Ward]: [WardProfile],
  [EntityTypeEnum.Municipal]: [MunicipalProfile],
  [EntityTypeEnum.Government]: [
    withDynamicProfile(GovernmentProfile),
    (item: any, { category }: { category: string }) => {
      return {
        profileType:
          category === "hotCabinetMembers" || category === "repeatedLeaders"
            ? "CabinetMember"
            : undefined,
        data:
          category === "hotCabinetMembers"
            ? {
                leaders: item,
                startedAt: item.governmentStartedAt,
                endAt: item.governmentEndAt,
                department: {
                  name: item.departmentName,
                },
              }
            : item,
      };
    },
  ],
  [EntityTypeEnum.Election]: [ElectionProfile],
  [EntityTypeEnum.Parliament]: [ParliamentProfile],
  [EntityTypeEnum.Content]: [ContentProfile],
  [EntityTypeEnum.Rating]: [RatingProfile],
  [EntityTypeEnum.ElectionSub]: [ElectionSubProfile],
  [EntityTypeEnum.SocialMediaContent]: [RenderSocialMediaContent],
  [EntityTypeEnum.Recommendations]: [LeaderHeroProfile],
  [EntityTypeEnum.Media]: [MediaProfile],
  [EntityTypeEnum.Project]: [ProjectProfile],
  [EntityTypeEnum.Department]: [
    withDynamicProfile(DepartmentProfile),
    (item: any, { category }: { category: string }) => {
      return {
        profileType:
          category === "hotCabinetMembers" || category === "repeatedLeaders"
            ? "CabinetMember"
            : undefined,
        data:
          category === "repeatedLeaders"
            ? {
                leaders: item,
                startedAt: item.governmentStartedAt,
                endAt: item.governmentEndAt,
                role: `${item.appointment_count} times`,
              }
            : item,
      };
    },
  ],
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  return {
    props: {
      ...translation,
    },
  };
};

const mapEntityToAPICall = (params?: { entityType: string }) => ({
  [EntityTypeEnum.Leader]: (query: any) =>
    ApiService.getAnalytics("leaders", query),
  [EntityTypeEnum.Party]: (query: any) =>
    ApiService.getAnalytics("parties", query),
  [EntityTypeEnum.Ward]: (query: any) =>
    ApiService.getAnalytics("geo-wards", query),
  [EntityTypeEnum.Municipal]: (query: any) =>
    ApiService.getAnalytics("geo-municipals", query),
  [EntityTypeEnum.Government]: (query: any) =>
    ApiService.getAnalytics("governments", query),
  [EntityTypeEnum.Election]: (query: any) =>
    ApiService.getAnalytics("elections", query),
  [EntityTypeEnum.Parliament]: (query: any) =>
    ApiService.getAnalytics("parliaments", query),
  [EntityTypeEnum.Content]: (query: any) =>
    ApiService.getAnalytics("contents", query),
  [EntityTypeEnum.Rating]: (query: any) =>
    ApiService.getAnalytics("ratings", query),
  [EntityTypeEnum.ElectionSub]: (query: any) =>
    ApiService.getAnalytics("elections/sub", query),
  [EntityTypeEnum.SocialMediaContent]: (query: any) =>
    ApiService.getAnalytics("socialcontentmanager", query),
  [EntityTypeEnum.Recommendations]: async (query: any) => {
    const data = await ApiService.resource.getAll("recommendations", {
      entityType: params?.entityType || "leaders",
      entityId: "",
    });

    return data;
  },
  [EntityTypeEnum.Media]: (query: any) =>
    ApiService.getAnalytics("medias", query),
  [EntityTypeEnum.Project]: (query: any) =>
    ApiService.getAnalytics("projects", query),
  [EntityTypeEnum.Department]: (query: any) =>
    ApiService.getAnalytics("departments", query),
});

const nonPaginationCategories = [
  "anniversaryParties",
  "birthdayPost",
  "anniversaryGovernments",
];
const singleItemGrid = [
  "knowYourLocalRepresentatives",
  "knowYourFederalRepresentatives",
  "knowYourLocalWardRepresentatives",
  "knowYourProvincialRepresentatives",
  "recommendations",
  "hotCabinetMembers",
  ...nonPaginationCategories,
];

export default function FeedPage() {
  const dedupIdsSet = useRef(new Set());

  const router = useRouter();
  const { t } = useTranslation();
  const { type, category } = router.query;

  const entityType = type as EntityTypeEnum;
  const categoryKey = category as string;
  //@ts-expect-error
  const [Component, Props] = componentMap[entityType];

  const pageSize = 20;
  const [page, setPage] = React.useState(1);
  const [data, setData] = React.useState<any[]>([]);
  const [total, setTotal] = React.useState(0);

  const fetchKey = useMemo(() => {
    return `feed-${entityType}-${categoryKey}-${page}-${pageSize}`;
  }, [entityType, categoryKey, page, pageSize]);
  const debouncedFetchKey = useDebounce(fetchKey, 1000);

  const query = useQuery({
    queryKey: debouncedFetchKey,
    queryFn: async () => {
      //@ts-expect-error
      const { data } = await mapEntityToAPICall({ entityType: categoryKey })[
        entityType
      ]({
        category,
        top: categoryKey,
        page,
        limit: pageSize,
      });
      setTotal(
        data?.data?.[categoryKey]?.totalItems ||
          data?.recommendations?.totalItems ||
          0
      );
      return (
        data?.data?.[categoryKey]?.items ||
        data?.data?.[categoryKey] ||
        data?.recommendations?.items ||
        []
      );
    },
    retry: 3,
    retryDelay: 1000,
    enabled: !!entityType && !!categoryKey,
  });
  const debouncedIsLoading = query.isLoading;

  const title = t(`common:${categoryKey}}`, {
    defaultValue: startCase(`${categoryKey}`),
  });
  const handleLoadMore = (...args: any[]) => {
    if (debouncedIsLoading) return;
    setPage((p) => p + 1);
    // TODO: Implement load more
  };

  useEffect(() => {
    if (query.data) {
      const deduped = query.data.filter((item: any) => {
        if (!item) return;
        if (dedupIdsSet.current.has(item.id)) return false;
        dedupIdsSet.current.add(item.id);
        return true;
      });
      setData((d) => [...d, ...deduped]);
    }
  }, [query.data]);

  const hasMore = useMemo(() => {
    if (type === EntityTypeEnum.SocialMediaContent) {
      if (nonPaginationCategories.includes(category as string)) return false;
      return true;
    }
    return data?.length < total;
  }, [data, total]);

  const gridSpanMemo = useMemo(() => {
    return {
      base:
        entityType === EntityTypeEnum.Leader ||
        (entityType === EntityTypeEnum.Recommendations &&
          category === "leaders") ||
        singleItemGrid.includes(category as string)
          ? 6
          : 12,
      sm: 6,
      md: 5,
      lg:
        entityType === EntityTypeEnum.Leader ||
        entityType === EntityTypeEnum.Recommendations ||
        singleItemGrid.includes(category as string)
          ? 2.4
          : 4,
    };
  }, [entityType]);
  const formattedEntityType = entityType.toLowerCase();

  return (
    <>
      <Head>
        <title>
          {title} | NepalTracks | Nepal’s Ultimate Political Tracker
        </title>
      </Head>

      <Stack p="md">
        <Title order={3}>
          {t(`common:${category}_${formattedEntityType}`, {
            defaultValue: startCase(`${category}_${formattedEntityType}`),
          })}
        </Title>

        {query.isLoading && !data.length ? (
          <Center py="xl">
            <Loader />
          </Center>
        ) : query.error && !data.length ? (
          <Alert color="red">Failed to load feed</Alert>
        ) : !data?.length ? (
          <Alert color="yellow">No items found for this feed</Alert>
        ) : (
          <InfiniteScroll
            dataLength={data?.length} //This is important field to render the next data
            next={handleLoadMore}
            hasMore={hasMore}
            loader={null}
            // endMessage={
            //   <p style={{ textAlign: "center" }}>
            //     <b>Yay! You have seen it all</b>
            //   </p>
            // }
            // below props only if you need pull down functionality
            refreshFunction={handleLoadMore}
            pullDownToRefresh
            pullDownToRefreshThreshold={50}
            pullDownToRefreshContent={
              <h3 style={{ textAlign: "center" }}>
                &#8595; Pull down to refresh
              </h3>
            }
            releaseToRefreshContent={
              <h3 style={{ textAlign: "center" }}>
                &#8593; Release to refresh
              </h3>
            }
          >
            <Grid className="overflow-x-hidden" columns={12} p={"xs"}>
              {data.map((item: any) => (
                <Grid.Col span={gridSpanMemo} key={item.id}>
                  <Component
                    data={item}
                    id={category}
                    {...(Props?.(item, { category, entityType, categoryKey }) ||
                      {})}
                  />
                </Grid.Col>
              ))}
              {debouncedIsLoading &&
                Array.from({ length: 10 }).map((_, index) => (
                  <Grid.Col span={gridSpanMemo} key={index}>
                    <Skeleton h={300} />
                  </Grid.Col>
                ))}
            </Grid>
          </InfiniteScroll>
        )}
      </Stack>
    </>
  );
}
