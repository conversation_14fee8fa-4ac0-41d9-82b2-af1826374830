import { useTranslation } from "next-i18next";
import { Accordion, Flex, Group, Stack } from "@mantine/core";
import { GetServerSideProps } from "next";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { IParty } from "@/interfaces/IParty";
import { EntityInfo } from "@/components/EntityInfo";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getImageUrlWithFallback } from "@/utils";
import DisqusComment from "@/components/DisqusComment";
import { loadTranslation } from "@/i18n";
import { Avatar } from "@mantine/core";
import Link from "next/link";
import { useState } from "react";
import { getContentMenus } from "@/containers/utils";
import { UserInfoAction } from "@/components/UserInfoAction";
import { DynamicSEO } from "@/containers/DynamicSEO";
import { ApiService } from "../../../../api";
import AsyncFeatureSlider from "@/containers/AsyncFeatureSlider";
import { UserAnalyticsType } from "@/pages/app/my-representatives";
import PartyBadge from "@/components/PartyBadge";
import { IMunicipals, IWard } from "@/interfaces/IElectionSubResponse";
import { PartyResults } from "@/components/PartyResults";
import { ElectionsList } from "@/containers/ElectionsList";
import AppBreadCrumb from "@/components/AppBreadCrumb";

export const getServerSideProps: GetServerSideProps<{
  data?: IParty;
}> = async (context) => {
  const { id: governmentId } = context?.params as {
    level: string;
    id: string;
  };
  if (!governmentId)
    return {
      props: {
        data: null,
      },
    };
  const translation = await loadTranslation(context.locale!, "common");
  const response = await ApiService.resource.getById<any>(
    `geo-wards`,
    governmentId + ""
  );
  return {
    props: {
      ...translation,
      data: response?.data,
    },
  };
};
const PartyDetail = (props: { data: IWard; rating: IReviewAnalysis }) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { data: ward } = props;

  const { id: governmentId, level } = router.query;
  const [selectedWard, setSelectedWard] = useState(0);
  const formattedName = `${props?.data?.municipals?.name}, Ward ${props?.data?.name}`;
  const formattedLocalName = `${props?.data?.municipals?.localName}, वडा ${props?.data?.localName}`;
  return (
    <>
      <DynamicSEO
        data={{
          ...props.data,
          name: formattedName,
          localName: formattedLocalName,
        }}
        type="wards"
      />
      <AppBreadCrumb
        links={[
          {
            label: t("common:local_government"),
            href: "/governments/local",
          },
          {
            label: ward?.municipals?.localName,
            href: `/municipals/${ward?.municipalId}/home`,
          },
        ]}
      />
      <Flex w={"100%"} direction="column" gap={10}>
        <EntityInfo
          name={formattedLocalName}
          rate={+props?.rating?.average}
          avatar="https://c8.alamy.com/comp/PYEMPE/symbol-of-nepal-national-emblem-PYEMPE.jpg"
          // avatar={getImageUrlWithFallback(null, props.data?.head?.ecCandidateID)}
          id={governmentId as string}
          partyId={governmentId as string}
          address={
            <Group>
              <Link href={`/municipals/${ward.municipalId}/home`}>
                {ward.municipals?.localName}
              </Link>
              {ward.districts?.localName}, {ward.states?.localName}
            </Group>
          }
          title={<Group> {t("common:ward", { defaultValue: "Ward" })}</Group>}
        />

        <Flex direction={"column"} w={"100%"} gap={10}>
          <SegmentedTab
            resources={"wards"}
            entityId={governmentId as string}
            defaultValue="home"
            data={[
              {
                label: "overview",
                value: "home",
                content: (
                  <Stack>
                    <AsyncFeatureSlider
                      entityId={governmentId as string}
                      title={t("common:representatives_of", {
                        entity: formattedLocalName,
                        defaultValue: "Representatives of {{entity}}",
                      })}
                      entityType="municipals"
                      resourceUrl={
                        "geo-municipals/" + governmentId + "/representatives"
                      }
                      renderItems={(item: UserAnalyticsType) => {
                        return (
                          <UserInfoAction
                            description={
                              <Stack>
                                <Link
                                  className="text-inherit "
                                  href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                                >
                                  {item.candidacyType?.localName}
                                </Link>
                                <PartyBadge
                                  partyId={item.parties.id}
                                  partyImage={item.parties.logo}
                                  partyName={item.parties.localName}
                                />
                              </Stack>
                            }
                            img={getImageUrlWithFallback(
                              item.leaders.img,
                              item.leaders.ecCandidateID
                            )}
                            name={
                              <Link
                                href={`/leaders/${item.leaders.id}`}
                                className="no-underline text-inherit"
                              >
                                {item.leaders?.localName}
                              </Link>
                            }
                            key={item.leaders.id}
                          />
                        );
                      }}
                    />

                    <AsyncFeatureSlider
                      entityId={governmentId as string}
                      title={""}
                      entityType="municipals"
                      resourceUrl={
                        "geo-municipals/" +
                        props.data?.municipalId +
                        "/wards/" +
                        props.data.name +
                        "/representatives"
                      }
                      renderItems={(item: UserAnalyticsType) => {
                        return (
                          <UserInfoAction
                            description={
                              <Stack>
                                <Link
                                  className="text-inherit "
                                  href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}/home`}
                                >
                                  {item.candidacyType?.localName}
                                </Link>
                                <PartyBadge
                                  partyId={item.parties.id}
                                  partyImage={item.parties.logo}
                                  partyName={item.parties.localName}
                                />
                              </Stack>
                            }
                            img={getImageUrlWithFallback(
                              item.leaders.img,
                              item.leaders.ecCandidateID
                            )}
                            name={
                              <Link
                                href={`/leaders/${item.leaders.id}`}
                                className="no-underline text-inherit"
                              >
                                {item.leaders?.localName}
                              </Link>
                            }
                            key={item.leaders.id}
                          />
                        );
                      }}
                      renderAfterSlider={(data) => {
                        return <PartyResults data={data || []} />;
                      }}
                      onDataLoad={(data) => {
                        document
                          .getElementById(selectedWard + "-ward")
                          ?.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                          });
                      }}
                    />

                    {/* <ContentStatsCards
                      stats={governmentOverview?.stats || {}}
                    /> */}
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings
                    entityId={governmentId as string}
                    entityType={EntityTypeEnum.Ward}
                  />
                ),
              },
              {
                label: "elections",
                value: "elections",
                content: (
                  <ElectionsList
                    resourceUrl={
                      "geo-municipals/" +
                      props.data?.municipalId +
                      "/wards/" +
                      props.data.name +
                      "/elections"
                    }
                  />
                ),
              },
              ...getContentMenus(governmentId as string, EntityTypeEnum.Ward),

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"Ward" + governmentId}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default PartyDetail;
