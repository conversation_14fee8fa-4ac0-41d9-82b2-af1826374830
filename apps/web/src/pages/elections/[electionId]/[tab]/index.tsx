import { useTranslation } from "next-i18next";
import { EntityInfo } from "@/components/EntityInfo";
import {
  Avatar,
  Badge,
  Box,
  Breadcrumbs,
  Card,
  Flex,
  Group,
  Stack,
  Text,
} from "@mantine/core";
import { useRouter } from "next/router";
import Ratings from "@/containers/Ratings";
import SegmentedTab from "@/components/SegmentedTab";
import { IconChartBar, IconHome } from "@tabler/icons-react";
import { ApiService } from "../../../../../api";
import Link from "next/link";
import { GetServerSideProps } from "next";
import DisqusComment from "@/components/DisqusComment";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { ILeader } from "@/interfaces/ILeader";
import { HeroCard } from "@/components/HeroCard";
import { formatNumber, getImageUrlWithFallback, truncateString } from "@/utils";
import { IParty } from "@/interfaces/IParty";
import ElectionResultsContents from "@/containers/ElectionResultContents";
import { VoteCountBox } from "@/components/VoteCountBox";
import { PartiesTab } from "@/containers/PartiesTab";
import ElectionPartiesTab from "@/containers/ElectionPartiesTab";
import { UserInfoAction } from "@/components/UserInfoAction";
import LeaderProfile from "@/components/LeaderProfile";
import PartyProfile from "@/components/PartyProfile";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import ElectionDashboard from "@/components/ElectionDetailDashboard";
import RequestPaginatedTable from "@/containers/RequestPaginatedTable";
import ElectionMostVoted from "@/containers/ElectionMostVoted";
import ElectionRegionalReportAsync from "@/containers/ElectionRegionalReportAsync";
import DistrictCard from "@/components/DistrictCard";
import { FaArrowDown, FaArrowUp } from "react-icons/fa";
import { SectionTitle } from "@/components/SectionTitle";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  const { data } = await ApiService.resource.getAll(
    `elections/${context.query.electionId}`,
    {}
  );
  //@ts-expect-error
  data.name = data.election.name;
  return {
    props: {
      ...translation,
      elections: data,
    },
  };
};

const Elections = (props: { elections: any }) => {
  const router = useRouter();
  const { electionId } = router.query;
  const { t } = useTranslation();

  return (
    <>
      <DynamicSEO data={props.elections} type="elections" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:elections"),
            href: "/elections",
          },
          {
            label: props.elections?.election?.name,
            href: `/elections/${electionId}`,
          },
        ]}
      />
      <Flex w={"100%"} direction={"column"} gap={10}>
        <Box>
          <EntityInfo
            partyId=""
            linkComponent={Link}
            name={<Group>{props.elections?.election?.name}</Group>}
            avatar={"/images/others/ec-nepal.png"}
            id={props.elections?.election.id as string}
          />
        </Box>

        <Flex direction={"column"} w={"100%"} gap={10}>
          <SegmentedTab
            onTabChange={(tab, router) => {
              router.push(`/elections/${props.elections.election.id}/${tab}`);
            }}
            entityId={electionId as string}
            resources={`elections/${electionId}`}
            defaultValue={"home"}
            data={[
              {
                label: "Home",
                value: "home",
                content: (
                  <Stack>
                    <FeaturedSlider<ILeader>
                      title={t("common:most_voted_leaders", {
                        defaultValue: "Most voted leaders",
                      })}
                      data={props.elections.mostVotedLeaders}
                      //@ts-expect-error
                      renderItems={(item: {
                        voteCount: number;
                        leaders: ILeader;
                      }) => {
                        return (
                          <LeaderHeroProfile
                            data={item.leaders}
                            renderDescription={() => (
                              <Link
                                //@ts-expect-error
                                href={`/elections/${props.elections.election.id}/sub/${item.elCode}/${item.candidacyTypeId}`}
                              >
                                <VoteCountBox count={item.voteCount} />
                              </Link>
                            )}
                          />
                        );
                      }}
                    />
                    <FeaturedSlider<ILeader>
                      title={t("common:most_win_parties", {
                        defaultValue: "Most win parties",
                      })}
                      data={props.elections.mostWin}
                      //@ts-expect-error
                      renderItems={(item: { value: number; party: IParty }) => {
                        return (
                          <PartyProfile
                            data={item.party}
                            renderInformation={() => (
                              <Link
                                //@ts-expect-error
                                href={`/elections/${props.elections.election.id}/sub/${item.elCode}/${item.candidacyTypeId}`}
                              >
                                <VoteCountBox count={item.value} />
                              </Link>
                            )}
                          />
                        );
                      }}
                    />
                    <FeaturedSlider<ILeader>
                      title={t("common:most_voted_parties", {
                        defaultValue: "Most voted parties",
                      })}
                      data={props.elections.topPartiesWithDetails}
                      //@ts-expect-error
                      renderItems={(item: { value: number; party: IParty }) => {
                        return (
                          <PartyProfile
                            data={item.party}
                            renderInformation={() => (
                              <Link
                                //@ts-expect-error
                                href={`/elections/${props.elections.election.id}/sub/${item.elCode}/${item.candidacyTypeId}`}
                              >
                                <VoteCountBox count={item.value} />
                              </Link>
                            )}
                          />
                        );
                      }}
                    />
                    {/* <ElectionMostVoted
                          key={item.id}
                          electionId={item.id + ""}
                          partyId={partyId + ""}
                        /> */}
                    {/* <ElectionRegionalReportAsync
                          entityId={partyId + ""}
                          entityType="parties"
                          resourceUrl={`parties/analytics?partyId=${partyId}&electionId=${item.id}`}
                          // @ts-expect-error
                          renderItems={(d, { rank, region }) => {
                            return (
                              <DistrictCard
                                title={
                                  rank === "top"
                                    ? t("common:top_district", "Top District")
                                    : t(
                                        "common:bottom_district",
                                        "Bottom District"
                                      )
                                }
                                district={d}
                                icon={
                                  rank === "top" ? (
                                    //@ts-expect-error
                                    <FaArrowUp className="text-green-600" />
                                  ) : (
                                    //@ts-expect-error
                                    <FaArrowDown />
                                  )
                                }
                              />
                            );
                          }}
                          title={item.name}
                        /> */}

                    <ElectionPartiesTab
                      hideTable
                      entityId={electionId as string}
                      resources="elections"
                      noTreeMap
                      onlyWinner
                      displayCharts
                    />
                    {electionId && (
                      <ElectionDashboard
                        electionId={electionId as unknown as number}
                      />
                    )}
                    {props.elections.mostWin.map((item: any) => (
                      <Card key={item.id}>
                        <SectionTitle>
                          <Group gap={5}>
                            <Avatar
                              src={item.party.logo}
                              size={32}
                              radius="xl"
                            />
                            <Text tt="uppercase" fw={700}>
                              Regional Report of {item.party.localName}
                            </Text>
                          </Group>
                        </SectionTitle>
                        <ElectionRegionalReportAsync
                          key={item.id}
                          entityId={item.id + ""}
                          entityType="parties"
                          resourceUrl={`parties/analytics?partyId=${item.partyId}&electionId=${props.elections.election.id}`}
                          // @ts-expect-error
                          renderItems={(d, { rank, region }) => {
                            return (
                              <DistrictCard
                                title={
                                  rank === "top"
                                    ? t("common:top_district", "Top District")
                                    : t(
                                        "common:bottom_district",
                                        "Bottom District"
                                      )
                                }
                                district={d}
                                icon={
                                  rank === "top" ? (
                                    //@ts-expect-error
                                    <FaArrowUp className="text-green-600" />
                                  ) : (
                                    //@ts-expect-error
                                    <FaArrowDown />
                                  )
                                }
                              />
                            );
                          }}
                          title={item.name}
                        />
                      </Card>
                    ))}
                  </Stack>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings entityId={`${electionId}`} entityType={"ELECTION"} />
                ),
              },
              {
                label: "parties",
                value: "parties",
                content: (
                  <ElectionPartiesTab
                    entityId={electionId as string}
                    resources="elections"
                  />
                ),
              },
              {
                label: "results",
                value: "results",
                content: (
                  <Stack>
                    <FeaturedSlider<ILeader>
                      title="Most voted leaders"
                      data={props.elections.mostVotedLeaders}
                      //@ts-expect-error
                      renderItems={(item: {
                        voteCount: number;
                        leaders: ILeader;
                      }) => {
                        return (
                          <UserInfoAction
                            img={getImageUrlWithFallback(
                              item.leaders.img,
                              item.leaders.ecCandidateID
                            )}
                            name={
                              <Link
                                href={`/leaders/${item.leaders.id}`}
                                className="text-inherit no-underline"
                              >
                                {item.leaders.localName}
                              </Link>
                            }
                            description={
                              <VoteCountBox count={item.voteCount} />
                            }
                          />
                        );
                      }}
                    />
                    <ElectionResultsContents
                      url={`elections/${props.elections.election.id}/candidates`}
                      electionId={props.elections.election.id}
                    />
                  </Stack>
                ),
              },

              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"election" + props.elections.election.id}
                  />
                ),
              },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default Elections;
