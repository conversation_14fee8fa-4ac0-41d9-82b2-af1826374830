import { useTranslation } from "next-i18next";
import { EntityInfo } from "@/components/EntityInfo";
import {
  Alert,
  Avatar,
  Blockquote,
  Box,
  Flex,
  Group,
  ScrollArea,
  Stack,
  Text,
} from "@mantine/core";
import { ApiService } from "../../../../../../../api";
import { useRouter } from "next/router";
import { IElectionSubResponse } from "@/interfaces/IElectionSubResponse";
import Ratings from "@/containers/Ratings";
import SegmentedTab from "@/components/SegmentedTab";
import { FeaturedEntitiesCarousel } from "@/components/FeaturedEntitiesCarousel";
import ElectionCandidateHeroCard from "@/components/ElectionCandidateHeroCard";
import Link from "next/link";
import {
  formatConstituencyAddress,
  formatDate,
  getImageUrlWithFallback,
  removeUnnecessaryDashes,
} from "@/utils";
import { IElectionResult } from "@/interfaces/IElections";
import { GroupedStats } from "@/components/GroupedStats";
import { useMemo } from "react";
import SubElectionVisualization from "@/components/SubElectionVisualization";
import { ElectionResultTable } from "@/components/ElectionResultTable";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { UserInfoAction } from "@/components/UserInfoAction";
import PartyBadge from "@/components/PartyBadge";
import { PartyResults } from "@/components/PartyResults";
import DisqusComment from "@/components/DisqusComment";
import ContentsContainer from "@/containers/ContentsContainer";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import LeaderElectionHistory from "@/components/LeaderElectionHistory";
import DistanceFromNow from "@/components/DistanceFromNow";
import { LeaderElectionHistoryChart } from "@/components/LeaderElectionHistoryGraph";
import { SectionTitle } from "@/components/SectionTitle";
import { VoteCountBox } from "@/components/VoteCountBox";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  const { electionId, elCode, positionId, tab } = context.query;
  const response = await ApiService.resource.getById(
    `elections/${electionId}/code/${elCode}/position`,
    positionId as string
  );
  //@ts-ignore
  const candidacy = response.data?.candidacyType?.localName || "Loading...";
  //@ts-ignore
  const municipal = response.data?.municipals?.localName;
  //@ts-ignore
  const district = response.data?.districts?.localName || "";
  //@ts-ignore
  const area = response.data?.allResults?.[0]?.area || "";
  //@ts-ignore
  const ward = response.data?.ward?.id ? `- ${response.data.ward.id}` : "";

  //@ts-expect-error
  response.data.name = formatConstituencyAddress({
    //@ts-ignore
    municipal: response.data?.municipals,
    //@ts-ignore
    district: response.data?.districts,
    //@ts-ignore
    area: response.data?.allResults?.[0]?.area,
    //@ts-ignore
    ward: response.data?.ward,
    //@ts-ignore
    candidacyType: response.data?.candidacyType,
  });
  return {
    props: {
      ...translation,
      //@ts-ignore
      response: response?.data as IElectionSubResponse,
    },
  };
};
function getElectionHistoryMessage(data: any): string {
  const leaderName = data.leaders?.localName || "This leader";
  const elections = data.previousElections || [];
  const current = data;

  const formatElectionEntry = (entry: any) => {
    const year = entry.elections?.name || "Unknown Election";
    const province = entry.states?.localName || "Unknown Province";
    const district = entry.districts?.localName || "Unknown District";
    const area = entry.area ? `-${entry.area}` : "";
    const votes = entry.voteCount ? ` with ${entry.voteCount} votes` : "";
    return `elected from ${district}${area}, ${province} in the ${year}${votes}`;
  };

  const messages = elections
    .filter((e: any) => e.isElected)
    .map((entry: any) => formatElectionEntry(entry));

  // Add current election if elected
  // if (current.isElected) {
  //   messages.push(formatElectionEntry(current));
  // }

  if (messages.length === 0) {
    return elections.length
      ? `${leaderName} has contested in ${elections.length} previous election${
          elections.length > 1 ? "s" : ""
        } but was not elected.`
      : `${leaderName} has not contested in any previous elections.`;
  }

  return `${leaderName} was also ${messages.join(" and then ")}.`;
}

const SubElCode = (props: { response: IElectionSubResponse }) => {
  const router = useRouter();
  const { t } = useTranslation();
  const { electionId, elCode, positionId, tab } = router.query;
  const resourceQuery = {
    data: props.response,
    isLoading: false,
  };
  const featuredItems = resourceQuery?.data?.allResults.slice(0, 6);
  const totalCandidates = useMemo(
    () => resourceQuery.data?.allResults.length || 0,
    [resourceQuery.data?.allResults]
  );
  const partiesCount = useMemo(
    () =>
      resourceQuery.data?.allResults.filter(
        (item) => item.parties.localName !== "स्वतन्त्र"
      ).length,
    [resourceQuery.data]
  );
  const independentCount = useMemo(
    () => totalCandidates - (partiesCount || 0),
    [partiesCount]
  );

  console.log(resourceQuery);
  return (
    <>
      <DynamicSEO data={props.response} type="parliaments" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:elections"),
            href: "/elections",
          },
          {
            label: resourceQuery.data?.elections?.name,
            href: `/elections/${electionId}`,
          },
        ]}
      />{" "}
      <Flex w={"100%"} direction={"column"} gap={10}>
        <Box>
          <EntityInfo
            linkComponent={Link}
            partyId="2"
            name={
              <Group>
                {/* @ts-ignore */}
                {resourceQuery?.data?.name}
              </Group>
            }
            title={
              <Group gap={1}>
                <Text>{formatDate(resourceQuery.data.elections.year)}</Text>
                <DistanceFromNow
                  startedAt={resourceQuery.data.elections.year}
                />
              </Group>
            }
            resources={`elections/${electionId}/sub/${elCode}`}
            rate={resourceQuery.data?.ratings?.average}
            avatar={"/images/others/ec-nepal.png"}
            // gender={props.data.gender}
            id={positionId as string}
            address={<Stack>{resourceQuery.data?.elections.name}</Stack>}
            // partyId={props.data.partyLeaders[0].party.id + ""}
            // title={
            //     <>
            //         {props.data.partyLeaders[0].party.localName}
            //         {" "}
            //         {/* <Badge color="gray">{leader.PartyLeader[0].role}</Badge> */}
            //     </>
            // }
            renderRightSection={() => (
              <>
                <GroupedStats
                  data={[
                    {
                      title: "Votes",
                      stats:
                        resourceQuery.data?.stats.totalVote.toLocaleString() +
                        "+",
                    },
                    { title: "Candidates", stats: totalCandidates + "" },
                    { title: "Parties", stats: partiesCount + "" },
                    { title: "Independents", stats: independentCount + "" },
                  ]}
                />
              </>
            )}
            renderInformation={() => <></>}
          />
        </Box>
        <FeaturedEntitiesCarousel<IElectionResult>
          items={featuredItems || []}
          renderCarouselItem={(item) => (
            <ElectionCandidateHeroCard
              previousElections={item.previousElections}
              candidateId={item.leaderId}
              count={item.voteCount}
              isElected={item.isElected}
              name={item.leaders?.localName}
              remarks={item.remarks}
              image={
                item.leaders?.img ||
                `/images/leaders/${item.leaders?.ecCandidateID}.jpg`
              }
              partyImage={item.parties?.logo}
              partyName={item.parties?.localName}
              partyId={item.partyId}
              percantage={
                resourceQuery.data?.stats?.percentages[item.leaderId] || 0
              }
            />
          )}
        />
        <FeaturedSlider<IElectionResult>
          title={t("common:won_same_rea", {
            defaultValue: "Also won in same area",
          })}
          data={resourceQuery.data?.previousElectionsSameArea || []}
          renderItems={({
            leaders,
            parties,
            districts,
            area,
            candidacyType,
            elections,
            voteCount,
            isElected,
            ...el
          }) => {
            return (
              <UserInfoAction
                renderBeforeAvatar={() => <VoteCountBox count={voteCount} />}
                description={
                  <Link
                    className=" text-inherit"
                    href={`/elections/${el.electionId}/sub/${el.elCode}/${el.candidacyTypeId}/home`}
                  >
                    <Stack>
                      {removeUnnecessaryDashes(
                        `${districts?.localName || ""} - ${
                          el?.municipals?.localName || ""
                        } - ${el?.ward?.id || ""} - ${area || ""}`
                      )}
                      <PartyBadge
                        partyId={parties.id}
                        partyImage={parties.logo}
                        partyName={parties.localName}
                      />
                      <Text>{elections.name}</Text>
                    </Stack>
                  </Link>
                }
                img={getImageUrlWithFallback(
                  leaders?.img,
                  leaders?.ecCandidateID || ""
                )}
                name={
                  <Link
                    href={`/leaders/${leaders?.id}`}
                    className="no-underline text-inherit"
                  >
                    {leaders?.localName} ( {candidacyType?.localName})
                  </Link>
                }
                key={leaders?.id}
              />
            );
          }}
        />
        <FeaturedSlider<IElectionResult>
          title={t("common:nearby_elections", {
            defaultValue: "Nearby elections",
          })}
          data={resourceQuery.data?.nearbyResults || []}
          renderItems={({
            leaders,
            parties,
            districts,
            area,
            candidacyType,
            ...el
          }) => {
            return (
              <UserInfoAction
                description={
                  <Link
                    className=" text-inherit"
                    href={`/elections/${el.electionId}/sub/${el.elCode}/${el.candidacyTypeId}/home`}
                  >
                    <Stack>
                      {removeUnnecessaryDashes(
                        `${districts?.localName || ""} - ${
                          el?.municipals?.localName || ""
                        } - ${el?.ward?.id || ""} - ${area || ""}`
                      )}
                      <PartyBadge
                        partyId={parties.id}
                        partyImage={parties.logo}
                        partyName={parties.localName}
                      />
                    </Stack>
                  </Link>
                }
                img={getImageUrlWithFallback(
                  leaders?.img,
                  leaders?.ecCandidateID || ""
                )}
                name={
                  <Link
                    href={`/leaders/${leaders?.id}`}
                    className="no-underline text-inherit"
                  >
                    {leaders?.localName} ( {candidacyType?.localName})
                  </Link>
                }
                key={leaders?.id}
              />
            );
          }}
        />
        <FeaturedSlider<IElectionResult>
          title={t("common:related_elections", {
            defaultValue: "Related elections",
          })}
          data={resourceQuery.data?.relatedElections || []}
          renderItems={({
            leaders,
            parties,
            districts,
            area,
            candidacyType,
            ...el
          }) => {
            return (
              <EntityInfo
                id="home"
                partyId="home"
                cardProps={{ mih: "120px" }}
                resources={`elections/${el.electionId}/sub/${el.elCode}/${el.candidacyTypeId}`}
                name={
                  <>
                    {removeUnnecessaryDashes(`${districts?.localName || ""} - ${
                      el?.municipals?.localName || ""
                    } - ${" "}
                  ${el?.ward?.id || ""}`)}
                  </>
                }
                avatar="/images/others/ec-nepal.png"
                // avatar={getImageUrlWithFallback(null, props.data?.head?.ecCandidateID)}
                // address={member.department.description}
                title={<Group>{candidacyType?.localName}</Group>}
              />
              // <UserInfoAction
              //   description={
              //     <Link
              //       className="no-underline text-inherit"
              //       href={`/elections/${el.electionId}/sub/${el.elCode}/${el.candidacyTypeId}/home`}
              //     >
              //       <Stack>
              //         {districts?.localName} - {el?.municipals?.localName} -{" "}
              //         {el?.ward?.id} -{area}
              //         <PartyBadge
              //           partyId={parties.id}
              //           partyImage={parties.logo}
              //           partyName={parties.localName}
              //         />
              //       </Stack>
              //     </Link>
              //   }
              //   img={getImageUrlWithFallback(
              //     leaders?.img,
              //     leaders?.ecCandidateID || ""
              //   )}
              //   name={
              //     <Link
              //       href={`/leaders/${leaders?.id}`}
              //       className="no-underline text-inherit"
              //     >
              //       {leaders?.localName} ( {candidacyType?.localName})
              //     </Link>
              //   }
              //   key={leaders?.id}
              // />
            );
          }}
        />

        <FeaturedSlider<IElectionResult>
          carouselProps={{
            slideSize: { base: "100%", sm: "50%", md: "30%" },
            slideGap: "md",
          }}
          title="Facts & History"
          data={
            resourceQuery?.data?.allResults.filter(
              (item) => item.previousElections?.length
            ) || []
          }
          renderItems={(item) => (
            <Stack p={"sm"}>
              <Blockquote
                iconSize={24}
                p={"md"}
                color="blue"
                icon={
                  <Avatar
                    size={24}
                    src={getImageUrlWithFallback(
                      item.leaders?.img,
                      //@ts-expect-error
                      item.leaders?.ecCandidateID
                    )}
                  />
                }
                mt="xl"
              >
                {getElectionHistoryMessage(item)}
              </Blockquote>
              {/* @ts-ignore */}
              <LeaderElectionHistory leader={item} />
            </Stack>
          )}
        />

        {(
          resourceQuery?.data?.allResults.filter(
            //@ts-expect-error
            (item) => item.previousElections?.length > 1
          ) || []
        ).map((elections) => {
          return (
            <Stack>
              <LeaderElectionHistoryChart
                entityName={elections.leaders?.localName}
                elections={elections.previousElections || []}
              />
            </Stack>
          );
        })}

        <Flex direction={"column"} w={"100%"} gap={10}>
          <SegmentedTab
            entityId={positionId as string}
            resources={`elections/${electionId}/sub/${elCode}`}
            defaultValue={(tab as string) || "home"}
            data={[
              {
                label: "home",
                value: "home",
                content: (
                  <ScrollArea>
                    <SubElectionVisualization
                      data={resourceQuery?.data || {}}
                    />
                    <PartyResults data={resourceQuery.data?.allResults || []} />
                  </ScrollArea>
                ),
              },
              {
                label: "reviews",
                value: "ratings",
                content: (
                  <Ratings
                    entityId={elCode as string}
                    entityType={"ELECTION_SUB"}
                  />
                ),
              },
              {
                label: "results",
                value: "results",
                content: (
                  <ScrollArea>
                    {/* @ts-expect-error */}
                    <ElectionResultTable
                      data={resourceQuery.data?.allResults || []}
                    />
                  </ScrollArea>
                ),
              },

              {
                label: "news_controversies",
                value: "scandals",
                content: (
                  <ContentsContainer
                    resourceId={`${elCode}` as string}
                    resourceType={EntityTypeEnum.ElectionSub}
                    contentType={["SCANDAL", "CONTROVERSIES", "NEWS"]}
                    title={t("common:news_controversies", {
                      defaultValue: "News & Controversies",
                    })}
                  />
                ),
              },
              {
                label: "discussions",
                value: "discussions",
                content: (
                  <DisqusComment
                    url={
                      (typeof window !== "undefined" &&
                        window?.location?.href) ||
                      ""
                    }
                    identifier={"leader" + elCode}
                  />
                ),
              },
              // { label: "News", value: "news", content: <PoliticalJourney /> },
            ]}
          />
        </Flex>
      </Flex>
    </>
  );
};
export default SubElCode;
