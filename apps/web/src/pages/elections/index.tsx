import { useTranslation } from "next-i18next";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../api";
import { IElections } from "@/interfaces/IElectionSubResponse";
import {
  Accordion,
  Anchor,
  Avatar,
  Badge,
  Breadcrumbs,
  Group,
  SimpleGrid,
  Stack,
  Text,
} from "@mantine/core";
import { EntityInfo } from "@/components/EntityInfo";
import Link from "next/link";
import { formatDate } from "@/utils";
import { loadTranslation } from "@/i18n";
import { IconHome } from "@tabler/icons-react";
import ElectionSubProfile from "@/components/ElectionProfile";
import { DynamicSEO } from "@/containers/DynamicSEO";
import Head from "next/head";
import { SectionTitle } from "@/components/SectionTitle";
import { useEffect, useState } from "react";
import { useQuery } from "react-query";
import { ElectionsOverview } from "@/components/ElectionIndexOverview";
import { SekeletonFeatureSlider } from "@/components/SekeletonFeatureSlider";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { IElectionOverview } from "@/interfaces/IElectionOverview";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import { VoteCountBox } from "@/components/VoteCountBox";
import DistanceFromNow from "@/components/DistanceFromNow";
import PartyProfile from "@/components/PartyProfile";
import ElectionResultsContents from "@/containers/ElectionResultContents";
import SegmentedTab from "@/components/SegmentedTab";
import LeaderTrendChart from "@/components/LeaderTrendChart";
import { useRouter } from "next/router";
import ElectionTrendComparer from "@/containers/ElectionTrendComparer";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const { data } = await ApiService.resource.getAll("elections", {});
  const translation = await loadTranslation(context.locale!, "common");
  return {
    props: {
      elections: data,
      ...translation,
    },
  };
};
const Elections = (props: { elections: IElections[] }) => {
  const { t } = useTranslation();
  const [data, setData] = useState<IElectionOverview | null>(null);
  const router = useRouter();
  const [tab, setTab] = useState((router.query.tab as string) || "home");
  const [loading, setLoading] = useState(true);

  useQuery(
    ["electionsOverview"],
    async () => {
      const response = await ApiService.resource.getAll(
        `elections/overview`,
        {}
      );
      return response.data;
    },
    {
      onSuccess(data) {
        // @ts-expect-error
        setData(data);
        setLoading(false);
      },
    }
  );
  useEffect(() => {
    router.push(`/elections?tab=${tab}`);
  }, [tab]);
  useEffect(() => {
    setTab((router.query.tab as string) || "home");
  }, [router.query.tab]);

  return (
    <Stack>
      <Head>
        <title>Elections | NepalTracks.com</title>
      </Head>
      <SegmentedTab
        // disableRouting
        onTabChange={(tab, router) => {
          setTab(tab);
        }}
        entityId={""}
        resources={`elections`}
        defaultValue={tab || "home"}
        data={[
          {
            label: "Home",
            value: "home",
            content: (
              <Stack>
                <SimpleGrid cols={{ sm: 2, md: 3, xs: 1, lg: 4 }}>
                  {props.elections.map((election, index) => {
                    return <ElectionSubProfile key={index} data={election} />;
                  })}
                </SimpleGrid>
                {loading ? (
                  <Stack>
                    <SekeletonFeatureSlider />
                    <SekeletonFeatureSlider />
                    <SekeletonFeatureSlider />
                    <SekeletonFeatureSlider />
                  </Stack>
                ) : (
                  <>
                    {/* @ts-expect-error */}
                    <ElectionsOverview data={data} />
                  </>
                )}
              </Stack>
            ),
          },
          {
            label: "results",
            value: "results",
            content: (
              <Stack>
                <ElectionResultsContents
                  enableElection
                  url={`elections/candidates`}
                  electionId={1 + ""}
                />
              </Stack>
            ),
          },
          {
            label: "Trends",
            value: "trends",
            content: (
              <Stack>
                <ElectionTrendComparer />
              </Stack>
            ),
          },
        ]}
      />
    </Stack>
  );
};
export default Elections;
