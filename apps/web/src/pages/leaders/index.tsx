import { useTranslation } from "next-i18next";
import { ILeader } from "@/interfaces/ILeader";
import { Badge, Grid, Group, Text } from "@mantine/core";
import { getAgeFromDate, getImageUrlWithFallback } from "@/utils";
import DefaultIndexPage from "@/containers/DefaultIndexPage";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import Link from "next/link";
import { UserInfoAction } from "@/components/UserInfoAction";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};
const Leaders = () => {
  const { t } = useTranslation();

  return (
    <>
      <DefaultIndexPage
        enableUrlChangeOnPageChange
        pageTitle={t("common:leaders")}
        entityType={EntityTypeEnum.Leader}
        resource={"leaders"}
        skeletonType="card"
        renderItems={(items: ILeader[]) => {
          return (
            <>
              {items.map((leader) => (
                <Grid.Col
                  key={leader.id}
                  span={{ base: 12, sm: 6, md: 3, lg: 3 }}
                  className="shadow-sm"
                >
                  <Link
                    href={`/leaders/${leader.id}`}
                    className="text-inherit no-underline "
                  >
                    {/* <LeaderHeroProfile data={leader} /> */}
                    <UserInfoAction
                      showRate
                      rating={leader.rating?.average}
                      img={getImageUrlWithFallback(
                        leader.img,
                        leader.ecCandidateID
                      )}
                      name={
                        <Text>
                          {leader.localName}{" "}
                          <Badge color="gray">
                            {getAgeFromDate(leader.birthDate)}
                          </Badge>
                        </Text>
                      }
                      description={<Group>{leader.address}</Group>}
                    />
                  </Link>
                  {/* <HeroCard
                    category={leader.description}
                    rating={leader.rating?.average}
                    image={getImageUrlWithFallback(
                      leader.img,
                      leader.ecCandidateID
                    )}
                    title={
                      <Link
                        href={`/leaders/${leader.id}`}
                        style={{ color: "white", textDecoration: "none" }}
                      >
                        {leader.localName}
                      </Link>
                    }
                  /> */}
                </Grid.Col>
              ))}
            </>
          );
        }}
      />
    </>
  );
};
export default Leaders;
