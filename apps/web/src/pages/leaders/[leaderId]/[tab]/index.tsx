import { useTranslation } from "next-i18next";
import { ILeader } from "@/interfaces/ILeader";
import { Accordion, Badge, Box, Flex, Group, Stack, Text } from "@mantine/core";
import { GetServerSideProps } from "next";
import { ApiService } from "../../../../../api";
import SegmentedTab from "@/components/SegmentedTab";
import Ratings from "@/containers/Ratings";
import { useRouter } from "next/router";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { ElectionsTab } from "@/containers/ElectionsTab";
import {
  formatDate,
  getImageUrlWithFallback,
  getPublicSiteUrl,
  truncateString,
} from "@/utils";
import { PartiesTab } from "@/containers/PartiesTab";
import { EntityInfo } from "@/components/EntityInfo";
import Head from "next/head";
import ProfileCard from "@/components/ProfileCard";
import { startCase } from "lodash";
import DisqusComment from "@/components/DisqusComment";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { getContentMenus } from "@/containers/utils";
import ContentStatsCards from "@/containers/Sections/ContentStatsCards";
import SummaryMD from "@/components/SummaryMD";
import { useProfile } from "@/store";
import useDynamicEntityStorage from "@/hooks/useDynamicEntityStorage";
import RecentlySelfViewed from "@/containers/RecentlySelfViewed";
import Recommendations from "@/containers/Recommendations";
import ElectionOpponents from "@/containers/ElectionOpponents";
import { loadTranslation } from "@/i18n";
import { NextSeo, ArticleJsonLd, ProductJsonLd } from "next-seo";
import { DynamicSEO } from "@/containers/DynamicSEO";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import { useMemo } from "react";
import LeaderTenures from "@/components/LeaderTenures";
import {
  Bar,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { PopularityChart } from "@/components/PopularityChart";
import { SearchResultListService } from "@/containers/SearchResultListService";
import { SectionTitle } from "@/components/SectionTitle";
import RenderResourceType from "@/containers/RenderResourceType";
import ProjectProfile from "@/components/ProjectProfile";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import SocialLinks from "@/components/SocialLinks";
import Link from "next/link";
import PollContainer from "@/containers/PollContainer";

export const getServerSideProps: GetServerSideProps<{
  data?: ILeader;
}> = async (context) => {
  try {
    const leaderId = context?.params?.leaderId;
    if (!leaderId)
      return {
        props: {
          data: null,
        },
      };
    const translation = await loadTranslation(context.locale!, "common");
    const leader = await ApiService.getLeader(leaderId + "");
    return {
      props: {
        ...translation,
        data: leader.data?.data,
      },
    };
  } catch (error) {
    return {
      redirect: {
        destination: "/404",
        permanent: false,
      },
    };
  }
};

const LeaderDetail = (props: {
  data: ILeader & { rating: IReviewAnalysis };
}) => {
  const router = useRouter();
  const { leaderId } = router.query;
  const profile = useProfile();
  useDynamicEntityStorage("leaders", leaderId + "");
  const { t, i18n } = useTranslation();

  const metadata = useMemo(() => {
    return (
      (typeof props?.data?.metadata === "string"
        ? JSON.parse(props?.data?.metadata)
        : props?.data?.metadata) || {}
    );
  }, [props.data.metadata]);
  return (
    <>
      <DynamicSEO data={props.data} type="leaders" />
      <AppBreadCrumb
        links={[
          {
            label: t("common:leaders"),
            href: "/leaders",
          },
          {
            label: props.data?.localName,
            href: `/leaders/${leaderId}`,
          },
        ]}
      />{" "}
      <Flex w={"100%"} direction="column" gap={10}>
        <Flex direction={{ md: "row", base: "column" }} w={"100%"} gap={10}>
          <div className="w-full md:w-[30%]">
            <ProfileCard
              img={getImageUrlWithFallback(
                props.data?.img,
                props.data.ecCandidateID
              )}
              name={props.data?.localName}
              rating={+props.data?.rating?.average}
              gender={props.data?.gender}
              birthDate={props.data?.birthDate}
              id={props.data?.id}
              fbPage={props.data?.fbPage}
              twitterPage={props.data?.twitterPage}
              youtubePage={props.data?.youtubePage}
              renderInformation={() => {
                return (
                  <Box>
                    {!router.asPath.includes("ratings") ? (
                      <Box mt={-20}>
                        <Link
                          href={`/leaders/${props.data.id}/ratings`}
                          className="no-underline text-inherit"
                        >
                          <Ratings
                            entityId={props.data.id + ""}
                            entityType={EntityTypeEnum.Leader}
                            onlyAnalysis
                          />
                        </Link>
                      </Box>
                    ) : (
                      <>
                        <Stack gap={-10}>
                          <Text size={"sm"} fw={600}>
                            {t("common:description", {
                              defaultValue: "Description",
                            })}
                          </Text>
                          <Text size={"sm"} c={"dimmed"}>
                            {props.data.description || "N/A"}
                          </Text>
                        </Stack>
                        <Stack gap={-10}>
                          <Text size={"sm"} fw={600}>
                            {t("common:qualification", {
                              defaultValue: "Qualification",
                            })}
                          </Text>
                          <Text size={"sm"} c={"dimmed"}>
                            {props.data.qualification || "N/A"}
                          </Text>
                        </Stack>
                        <Stack gap={-10}>
                          <Text size={"sm"} fw={600}>
                            {t("common:experience", {
                              defaultValue: "Experience",
                            })}
                          </Text>
                          <Text size={"sm"} c={"dimmed"}>
                            {props.data.experience || "N/A"}
                          </Text>
                        </Stack>
                      </>
                    )}
                  </Box>
                );
              }}
            />
          </div>
          <Box w={{ md: "70%", base: "100%" }}>
            <SegmentedTab
              entityId={leaderId as string}
              variant="default"
              resources="leaders"
              defaultValue="home"
              data={[
                {
                  label: "home",
                  value: "home",
                  content: (
                    <Stack>
                      <ContentStatsCards
                        //@ts-expect-error
                        stats={props.data?.stats || {}}
                        resourceId={leaderId as string}
                        resourceType="leaders"
                      />

                      <SummaryMD
                        summary={props.data?.summary || ""}
                        summaryNP={props.data?.summaryNP || ""}
                        readMoreLink={`/leaders/${leaderId}/summary`}
                      />

                      <PopularityChart
                        resourceId={props.data.id + ""}
                        resourceType={EntityTypeEnum.Leader}
                      />

                      <SectionTitle>
                        {t("common:leadership_tenures", "Leadership Tenures")}
                      </SectionTitle>
                      <LeaderTenures leader={props.data} />

                      <SectionTitle>
                        {t("common:elections", "Elections")}
                      </SectionTitle>
                      <ElectionsTab
                        entityId={leaderId as string}
                        resources="leaders"
                      />
                      <ElectionOpponents
                        entityType="leaders"
                        entityId={leaderId + ""}
                      />

                      <FeaturedSlider
                        fullWidthOnSP
                        title={t("common:related_projects", {
                          defaultValue: "Related Projects",
                        })}
                        data={props.data?.projects || []}
                        renderItems={(item) => {
                          return <ProjectProfile data={item} />;
                        }}
                      />

                      <Recommendations
                        entityType="leaders"
                        entityId={leaderId + ""}
                      />

                      <RecentlySelfViewed
                        lists={["leaders"]}
                        entityId={leaderId as string}
                      />
                      <SearchResultListService
                        title={t("common:related_news_media", {
                          defaultValue: "Related News & Media",
                        })}
                        query={props.data.localName}
                        resourceId={props.data.id}
                        resourceType="LEADER"
                        contentType={["NEWS", "SCANDAL", "CONTROVERSIES"]}
                        renderNoResults={() => <></>}
                      />
                    </Stack>
                  ),
                },
                {
                  label: "reviews",
                  value: "ratings",
                  content: (
                    <Ratings
                      entityId={leaderId as string}
                      entityType={"Leader"}
                    />
                  ),
                },

                {
                  label: "Polls",
                  value: "polls",
                  content: (
                    <PollContainer
                      entityId={leaderId as string}
                      entityType={"Leader"}
                    />
                  ),
                },

                {
                  label: "parties",
                  value: "parties",
                  content: (
                    <PartiesTab
                      entityId={leaderId as string}
                      resources="leaders"
                    />
                  ),
                },

                ...getContentMenus(
                  leaderId as string,
                  EntityTypeEnum.Leader,
                  //@ts-expect-error
                  props.data.stats
                ),
                {
                  label: "elections",
                  value: "elections",
                  content: (
                    <ElectionsTab
                      entityId={leaderId as string}
                      resources="leaders"
                    />
                  ),
                },
                {
                  label: "tenures",
                  value: "tenures",
                  content: <LeaderTenures leader={props.data} />,
                },
                {
                  label: "summary",
                  value: "summary",
                  content: (
                    <SummaryMD
                      summary={props.data?.summary || ""}
                      summaryNP={props.data?.summaryNP || ""}
                    />
                  ),
                },
                {
                  label: "discussions",
                  value: "discussions",
                  content: (
                    <DisqusComment
                      url={
                        (typeof window !== "undefined" &&
                          window?.location?.href) ||
                        ""
                      }
                      identifier={"leader" + leaderId}
                    />
                  ),
                },
              ]}
            />
          </Box>
        </Flex>
      </Flex>
    </>
  );
};
export default LeaderDetail;
