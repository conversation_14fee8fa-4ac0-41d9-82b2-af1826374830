import { DynamicSEO } from "@/containers/DynamicSEO";
import { loadTranslation } from "@/i18n";
import { GetServerSideProps } from "next";
import {
  Container,
  Paper,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { ApiService } from "../../../../api";
import Ratings from "@/containers/Ratings";
import RatingProfile from "@/components/RatingProfile";
import DisqusComment from "@/components/DisqusComment";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");
  const { data } = await ApiService.resource.getAll(
    `ratings/hash/${context.params?.reviewId}`,
    {}
  );
  if (!data) {
    return {
      redirect: {
        destination: "/404",
        permanent: false,
      },
    };
  }

  return {
    props: {
      ...translation,
      item: data,
    },
  };
};

const Reviews = ({ item }: { item: any }) => {
  const { colorScheme } = useMantineColorScheme();
  const theme = useMantineTheme();
  const { resourceType, contentType } = item;
  return (
    <>
      <DynamicSEO
        forcePageTitle={"Review"}
        data={{ ...item, name: item.title || item.resource?.name }}
        type="contents"
        slug={`contents/${resourceType}/${contentType}`}
      />
      <Container size={"lg"}>
        <Paper
          p={"xs"}
          h={"100%"}
          bg={
            colorScheme === "dark" ? theme.colors.dark[6] : theme.colors.gray[1]
          }
        >
          <RatingProfile data={item} />
        </Paper>
        <DisqusComment
          url={(typeof window !== "undefined" && window?.location?.href) || ""}
          identifier={"review" + item.hash}
        />
      </Container>
    </>
  );
};
export default Reviews;
