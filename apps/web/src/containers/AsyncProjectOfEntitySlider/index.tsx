import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { startCase } from "lodash";
import { Accordion, Stack, Title } from "@mantine/core";
import React, { useEffect, useMemo } from "react";
import { formatNumber } from "@/utils";

const AsyncProjectOfEntitySlider = (props: {
  lists?: string[];
  entityType: string;
  entityId: string;
  resourceUrl: string;
  title: string | React.ReactNode;
  renderItems: any;
  renderAfterSlider?: (data: any) => React.ReactNode;
  onDataLoad?: (data: any) => void;
  transformResponse?: (data: unknown) => any;
}) => {
  const { t } = useTranslation();
  const query = useQuery(
    [props.resourceUrl, props.entityType, props.entityId],
    async () => {
      const response = await ApiService.resource.getAll(props.resourceUrl, {});
      return response?.data || [];
    }
  );

  const data = useMemo(() => {
    //@ts-expect-error
    return props.transformResponse?.(query.data) || query.data?.projects || [];
  }, [query.data]);

  useEffect(() => {
    if (query.data && props.onDataLoad) {
      props.onDataLoad(data);
    }
  }, [data]);

  return (
    <Stack>
      <FeaturedSlider
        isLoading={query.isLoading}
        data={data}
        title={props.title}
        renderItems={props.renderItems}
      />
      {props.renderAfterSlider?.(data)}
    </Stack>
  );
};
export default AsyncProjectOfEntitySlider;
