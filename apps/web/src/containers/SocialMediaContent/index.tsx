import { useTranslation } from "next-i18next";
import { QueryErrorResetBoundary, useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import PartyProfile from "@/components/PartyProfile";
import { IParty } from "@/interfaces/IParty";
import { startCase } from "lodash";
import { Badge, Group, Stack } from "@mantine/core";
import { RecommendationBadges } from "../RecommendationBadges";
import { EntityInfo } from "@/components/EntityInfo";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  truncateString,
} from "@/utils";
import Link from "next/link";
import { ISocialMediaContentResponse } from "@/interfaces/ISocialMediaContentResponse";
import ElectionSubProfile from "@/components/ElectionSubProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
import { IElectionResult } from "@/interfaces/IElections";
import { IGovernment } from "@/interfaces/IGovernment";
import { IconCake } from "@tabler/icons-react";
import { subYears } from "date-fns";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";

const initialData = {
  birthdayPost: [],
  anniversaryGovernments: [],
  anniversaryParties: [],
  knowYourLocalRepresentatives: [],
  knowYourFederalRepresentatives: [],
  knowYourProvincialRepresentatives: [],
  knowYourLocalWardRepresentatives: [],
  randomElectionResults: [],
};

export const RenderSocialMediaContent = ({
  id,
  data: item,
  additionalInformationEnabled,
}: {
  id: string;
  data: any;
  additionalInformationEnabled?: boolean;
}) => {
  const { t } = useTranslation();

  if (id === "randomElectionResults") {
    return <ElectionSubProfile data={item as IElectionResult} />;
  }
  if (id === "anniversaryParties") {
    return (
      <PartyProfile
        data={item as IParty}
        renderInformation={({ age }) => (
          <Badge
            radius={"sm"}
            variant="light"
            leftSection={<IconCake size={14} />}
          >
            {t("common:anniversaryPostMessage", {
              defaultValue: "Celebrating anniversary",
              age: age,
            })}
          </Badge>
        )}
      />
    );
  }
  if (id === "birthdayPost") {
    return (
      <LeaderHeroProfile
        data={item as ILeader}
        isCakeDay
        additionalInformationEnabled
      />
    );
  }
  if (id === "anniversaryGovernments") {
    return (
      <GovernmentProfile
        renderInformation={({ age }) => (
          <Badge
            radius={"sm"}
            variant="light"
            leftSection={<IconCake size={14} />}
          >
            {t("common:anniversaryPostMessage", {
              defaultValue: "Celebrating anniversary",
              age: age,
            })}
          </Badge>
        )}
        data={item as IGovernment}
      />
    );
  }
  if (id === "knowYourLocalRepresentatives") {
    return (
      <ElectionSubProfile
        data={item as IElectionResult}
        profileType="Leader"
        additionalInformationEnabled={additionalInformationEnabled}
      />
    );
  }
  if (id === "knowYourFederalRepresentatives") {
    return (
      <ElectionSubProfile
        data={item as IElectionResult}
        profileType="Leader"
        additionalInformationEnabled={additionalInformationEnabled}
      />
    );
  }
  if (id === "knowYourProvincialRepresentatives") {
    return (
      <ElectionSubProfile
        data={item as IElectionResult}
        profileType="Leader"
        additionalInformationEnabled={additionalInformationEnabled}
      />
    );
  }
  if (id === "knowYourLocalWardRepresentatives") {
    return (
      <ElectionSubProfile
        data={item as IElectionResult}
        profileType="Leader"
        additionalInformationEnabled={additionalInformationEnabled}
      />
    );
  }
  return null;
};
const SocialMediaContent = (props: {
  fullWidthOnSP?: boolean;
  additionalInformationEnabled?: boolean;
}) => {
  const { t } = useTranslation();
  const query = useQuery(
    ["socialcontentmanager"],
    async () => {
      const response = await ApiService.getAnalytics(
        "socialcontentmanager",
        {}
      );
      return (response?.data || {}) as ISocialMediaContentResponse;
    },
    {
      initialData,
      keepPreviousData: true,
    }
  );
  /* @ts-expect-error */
  const data = query.data?.data || initialData;
  return (
    <>
      {Object.keys(data).map((key) => (
        <div key={key} data-entity-type={key}>
          <QueryErrorResetBoundary key={key}>
            <FeaturedSlider
              isLoading={query.isLoading}
              fullWidthOnSP={
                key === "anniversaryParties" || key === "randomElectionResults"
              }
              key={key}
              data={data?.[key] || []}
              title={
                <Group className="justify-between md:justify-normal items-center">
                  {t(`common:${key}_socialmediacontent`, {
                    defaultValue: startCase(key),
                  })}
                  <Link
                    href={`/feed?type=${EntityTypeEnum.SocialMediaContent}&category=${key}`}
                    className="text-inherit text-xs mt-1"
                  >
                    {t("common:see_more", "See more")} {">>"}
                  </Link>
                </Group>
              }
              renderItems={(item) => {
                return (
                  <RenderSocialMediaContent
                    id={key}
                    data={item}
                    additionalInformationEnabled={
                      props.additionalInformationEnabled
                    }
                  />
                );
              }}
            />
          </QueryErrorResetBoundary>
        </div>
      ))}
    </>
  );
};
export default SocialMediaContent;
