import { useTranslation } from "next-i18next";
import { Avatar, Badge, Table, Group, Text, ScrollArea } from "@mantine/core";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { IElectionResult } from "@/interfaces/IElections";
import Link from "next/link";
import { format } from "date-fns";
import ElectionTrendComparer from "../ElectionTrendComparer";

interface ElectionsTabProps {
  entityId: string;
  resources: string;
}

export function ElectionsTab(props: ElectionsTabProps) {
  const { t } = useTranslation();
  const electionsQuery = useQuery(
    ["leaderselections", props.entityId, props.resources],
    async () => {
      const response = await ApiService.resource.getAll(
        `leaders/${props.entityId}/elections`,
        {}
      );
      return response.data as unknown as {
        election_results: IElectionResult[];
      };
    }
  );

  const rows = electionsQuery.data?.election_results?.map((item) => (
    <tr key={item.id}>
      <td>
        <Group gap="sm" p={"md"}>
          <Avatar size={"md"} src={"/images/others/ec-nepal.png"} />
          <div>
            <Link
              href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}`}
            >
              <Text fz="sm" fw={500}>
                {item.elections.name}
              </Text>
            </Link>

            <Text fz="xs" c="dimmed">
              {item.candidacyType.localName}
            </Text>
          </div>
        </Group>
      </td>
      <td>
        <Text fz="xs" c="dimmed">
          {format(new Date(item.elections.year), "yyyy/M/dd")}
        </Text>
      </td>
      <td>
        <Link
          style={{
            textDecoration: "none",
          }}
          href={`/parties/${item.parties.id}`}
        >
          <Text fz="xs" c="dimmed">
            {item.parties.code} ({item.parties.electionSymbol})
          </Text>
        </Link>
      </td>
      <td>
        <Text fz="xs" c="dimmed">
          {item.districts.localName} - {item.area}
        </Text>
      </td>
      <td>
        {" "}
        <Text fz="xs" c="dimmed">
          {item.voteCount}
        </Text>
      </td>
      <td>
        {item.isElected ? (
          <Badge size="lg" color="teal" radius="xl">
            {item.remarks}
          </Badge>
        ) : (
          <Badge color="gray" fullWidth>
            None
          </Badge>
        )}
      </td>
    </tr>
  ));
  return (
    <ScrollArea>
      <Table miw={800} verticalSpacing="sm">
        <Table.Thead>
          <Table.Tr>
            <Table.Th>{t("common:election")}</Table.Th>
            <Table.Th>{t("common:date")}</Table.Th>
            <Table.Th>{t("common:party")}</Table.Th>
            <Table.Th>{t("common:district")}</Table.Th>
            <Table.Th>{t("common:votes")}</Table.Th>
            <Table.Th>{t("common:result")}</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {rows}
          {electionsQuery.isLoading ? (
            <tr>
              <td colSpan={6}>
                <Text ta="center">Loading...</Text>
              </td>
            </tr>
          ) : null}
          {!electionsQuery.isLoading &&
          electionsQuery.data?.election_results?.length === 0 ? (
            <tr>
              <td colSpan={6}>
                <Text ta="center">No elections found</Text>
              </td>
            </tr>
          ) : null}
        </Table.Tbody>
      </Table>
      {electionsQuery.data && (
        <ElectionTrendComparer
          disableChips
          disableSearch
          leaders={[electionsQuery.data]}
        />
      )}
    </ScrollArea>
  );
}
