import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { startCase } from "lodash";
import { Accordion, Stack, Title } from "@mantine/core";
import React, { useEffect, useMemo } from "react";
import { formatNumber } from "@/utils";

const ElectionRegionalReportAsync = (props: {
  lists?: string[];
  entityType: string;
  entityId: string;
  resourceUrl: string;
  title: string | React.ReactNode;
  renderItems: any;
  renderAfterSlider?: (data: any) => React.ReactNode;
  onDataLoad?: (data: any) => void;
  transformResponse?: (data: unknown) => void;
}) => {
  const { t } = useTranslation();
  const query = useQuery(
    [props.resourceUrl, props.entityType, props.entityId],
    async () => {
      const response = await ApiService.resource.getAll(props.resourceUrl, {});
      return response?.data || [];
    }
  );

  const data = useMemo(() => {
    return (
      //@ts-expect-error
      props.transformResponse?.(query.data) || query.data?.regionWins || {}
    );
  }, [query.data]);

  useEffect(() => {
    if (query.data && props.onDataLoad) {
      props.onDataLoad(data);
    }
  }, [data]);

  return (
    <Stack>
      {Object.keys(data).map((region) => {
        if (!Object.keys(data[region])) return;
        return (
          <Accordion key={region} defaultValue={"top"}>
            {Object.keys(data[region]).map((rank) => {
              if (!data[region]?.[rank].length) return;
              return (
                <Accordion.Item value={rank} key={rank}>
                  <Accordion.Control>
                    <Title order={5}>
                      {rank === "top"
                        ? t("common:strong_regions", {
                            election: props.title,
                            defaultValue: `Strong regions in {{ election }}`,
                          })
                        : t("common:weak_regions", {
                            election: props.title,
                            defaultValue: `Weak regions in {{ election }}`,
                          })}
                    </Title>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <FeaturedSlider
                      isLoading={query.isLoading}
                      data={data[region]?.[rank] || []}
                      title={""}
                      renderItems={(d) =>
                        props.renderItems(d, { rank, region })
                      }
                      carouselProps={{
                        slideSize: {
                          base: "100%",
                          sm: "50",
                          md: "35%",
                        },
                      }}
                    />
                  </Accordion.Panel>
                </Accordion.Item>
              );
            })}
          </Accordion>
        );
      })}

      {props.renderAfterSlider?.(data)}
    </Stack>
  );
};
export default ElectionRegionalReportAsync;
