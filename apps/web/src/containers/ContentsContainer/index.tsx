import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import DefaultIndexPage from "../DefaultIndexPage";
import { IContent } from "@/interfaces/IContent";
import {
  Accordion,
  Badge,
  Group,
  Paper,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  formatDate,
} from "@/utils";
import { formatDistance } from "date-fns";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import SuperControl from "@/components/SuperControl";

export interface IContentsContainerProps {
  resourceId?: string;
  resourceType: EntityTypeEnum;
  contentType?: string[];
  title?: string;
  onDataLoad?: (data: any) => void;
  disablePageTitle?: boolean;
}
const ContentsContainer = ({
  resourceId,
  resourceType,
  contentType,
  disablePageTitle,
  ...props
}: IContentsContainerProps) => {
  const { t } = useTranslation();

  return (
    <DefaultIndexPage
      onDataLoad={props.onDataLoad}
      hideChips
      disablePageTitle={disablePageTitle}
      hideSearch
      pageTitle={props.title}
      entityType={resourceType}
      // renderAfterNoResults={() => (
      //   <SuperControl
      //     enableRedditPost
      //     noEdit
      //     entityType="contents"
      //     entityId={resourceId + ""}
      //     newData={{
      //       resourceId,
      //       resourceType,
      //       contentType: contentType?.[0],
      //     }}
      //   />
      // )}
      resource={`contents?${[
        resourceType && `resourceType=${resourceType}`,
        resourceId && `resourceId=${resourceId}`,
        contentType?.length && `contentType=${contentType.join(",")}`,
      ]
        .filter(Boolean)
        .join("&")}`}
      renderItems={(items: IContent[]) => {
        if (!items) return null;
        return (
          <Accordion w={"100%"}>
            {items.map((item, index) => {
              const Icon = (ContentTypeIconMap[
                item.contentType as ContentTypeIconMapType
              ] || ContentTypeIconMap.DEFAULT) as any;
              const color =
                ContentTypeColors[item.contentType as ContentTypeIconMapType] ||
                "gray";

              return (
                <Accordion.Item key={index} value={item.id + ""}>
                  <Accordion.Control icon={<Icon />}>
                    <Group>
                      <Group>
                        <Badge color={color}>
                          {t(`common:${item.contentType}`, {
                            defaultValue: item.contentType
                              .replace(/_/g, " ")
                              .toLowerCase(),
                          })}
                        </Badge>{" "}
                        <Badge color={color} variant="outline">
                          {/* {startCase(item.contentStatus)} */}
                          {t(`common:${item.contentStatus}`, {
                            defaultValue: item.contentStatus
                              .replace(/_/g, " ")
                              .toLowerCase(),
                          })}
                        </Badge>
                      </Group>
                      <Group>
                        <Title order={5}>{item.title}</Title>
                        <Text>
                          {/* @ts-expect-error */}
                          {item.resource?.localName || item.resource?.name}
                        </Text>
                      </Group>
                      <SuperControl
                        onlyRedditIcon
                        enableRedditPost
                        entityType="contents"
                        entityId={item.resourceId + ":" + item.id}
                        newData={{
                          resourceId: item.resourceId,
                          resourceType: EntityTypeEnum.Leader,
                          contentType: item.contentType,
                          parentContent: item.id,
                          contentStatus: item.contentStatus,
                          code: Date.now() + "",
                        }}
                      />
                    </Group>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Paper shadow="xs" p={"lg"}>
                      <Stack>
                        <Group>
                          <Text c="dimmed">
                            {formatDate(item.eventDate)}
                            {" - "}
                            {item.eventDate &&
                              formatDistance(
                                new Date(item.eventDate || ""),
                                new Date()
                              )}
                            {" ago"}
                          </Text>
                        </Group>
                        <Text>{item.content}</Text>
                        <Link
                          href={`/contents/${item.resourceType.toLowerCase()}/${item.contentType.toLowerCase()}/${
                            item.id
                          }`}
                        >
                          {t("common:read_more", {
                            defaultValue: "Read more",
                          })}
                        </Link>
                      </Stack>
                    </Paper>
                  </Accordion.Panel>
                </Accordion.Item>
              );
            })}
          </Accordion>
        );
      }}
      renderAfterItems={() => {
        return (
          <SuperControl
            enableRedditPost
            noEdit
            entityType="contents"
            entityId={resourceId + ""}
            newData={{
              resourceId,
              resourceType,
              contentType: contentType?.[0],
            }}
          />
        );
      }}
    ></DefaultIndexPage>
  );
};
export default ContentsContainer;
