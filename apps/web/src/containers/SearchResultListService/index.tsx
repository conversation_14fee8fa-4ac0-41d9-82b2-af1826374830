import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { useTranslation } from "next-i18next";
import { IContent } from "@/interfaces/IContent";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  getHostFromUrl,
  truncateString,
} from "@/utils";
import {
  Avatar,
  Card,
  Group,
  Image,
  List,
  Skeleton,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import { useEffect } from "react";
import { SectionTitle } from "@/components/SectionTitle";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";

export const renderContentIcon = (contentType: string) => {
  //@ts-expect-error
  const ContentTypeIcon = ContentTypeIconMap[contentType as any];
  return ContentTypeIcon ? (
    <ContentTypeIcon color={ContentTypeColors[contentType]} />
  ) : null;
};

export function SearchResultListService(props: {
  query: string;
  resourceId: number;
  resourceType: string;
  contentType: string[];
  context?: EntityTypeEnum;
  title?: string;
  renderNoResults?: () => React.ReactNode;
  onDataLoad?: () => void;
}) {
  const query = useQuery(
    [
      "search-result",
      props.query,
      props.resourceId,
      props.resourceType,
      props.contentType,
    ],
    async () => {
      const response = await ApiService.resource.getAll<IContent>(`contents`, {
        query: props.query,
        resourceId: props.resourceId,
        resourceType: props.resourceType,
        contentType: props.contentType.join(","),
        context: props.context,
      });
      props.onDataLoad?.();
      return response?.data;
    },
    {
      cacheTime: 1000 * 60 * 5,
      staleTime: 1000 * 60 * 5,
    }
  );

  useEffect(() => {
    if (query.data) {
      props.onDataLoad?.();
    }
  }, [query.data]);

  return (
    <div>
      {props.title && query.data?.items?.length ? (
        <SectionTitle>{props.title}</SectionTitle>
      ) : null}

      {query.isLoading && (
        <Stack gap={5} className="animate__animated animate__fadeIn">
          <Skeleton h={"40px"} />
          <Skeleton h={"40px"} />
          <Skeleton h={"40px"} />
          <Skeleton h={"40px"} />
        </Stack>
      )}
      <List spacing={"sm"}>
        {query.data?.items?.map((item: IContent) => (
          <List.Item
            className="animate__animated animate__fadeIn overflow-x-hidden p-1"
            key={item.id}
            icon={
              item.cmsLink && item.contentType !== "NEWS" ? (
                renderContentIcon(item.contentType)
              ) : (
                <Avatar
                  src={getHostFromUrl(item.cmsLink) + "/favicon.ico"}
                  alt={item.title}
                  size={"xs"}
                />
              )
            }
          >
            <Link
              href={`/contents/${item.resourceType.toLowerCase()}/${item.contentType.toLowerCase()}/${
                item.id
              }`}
              className=" no-underline text-inherit text-sm"
            >
              <Text fw={600} className="text-sm">
                {item.title}
              </Text>
            </Link>
            <Text size="sm" color="dimmed" lineClamp={1}>
              {item.content}
            </Text>
          </List.Item>
        ))}
        {query.data?.items?.length === 0 &&
          !query.isLoading &&
          (props.renderNoResults ? (
            props.renderNoResults()
          ) : (
            <Text size="sm" color="dimmed">
              No results found.
            </Text>
          ))}
      </List>
    </div>
  );
}
