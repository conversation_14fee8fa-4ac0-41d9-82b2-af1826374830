import { useTranslation } from "next-i18next";
import { Comment } from "@/components/Comment";
import {
  Box,
  Button,
  Card,
  Center,
  Divider,
  Group,
  Loader,
  Pagination,
  Paper,
  Rating,
  Stack,
  Text,
  Tabs,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useCallback, useMemo, useState } from "react";
import RatingModal from "../RatingModal";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { IReview } from "@/interfaces/IReview";
import { formatDistanceToNow } from "date-fns";
import ReviewAnalysis from "@/components/ReviewAnalysis";
import { useRouter } from "next/router";
import {
  IconMoodEmpty,
  IconStar,
  IconMessageCircle,
} from "@tabler/icons-react";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { notifications } from "@mantine/notifications";
import { useProfile } from "@/store";
import { PopularityChart } from "@/components/PopularityChart";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import SystemPollsContainer from "../SystemPollsContainer";

const DEFAULT_PAGE_SIZE = 10;

const PollsContainer = (props: {
  entityType: string;
  entityId: string;
  onlyAnalysis?: boolean;
}) => {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [rate, setRate] = useState(0);
  const [activeTab, setActiveTab] = useState<string | null>("polls");
  const router = useRouter();
  const profile = useProfile();
  const { openLoginModal } = useLeaderHover();
  const page = router.query.page as string;

  const handlePagination = useCallback((page: number) => {
    const nexturl = new URLSearchParams(
      router.asPath + "?" + window.location.search
    );
    nexturl.set("page", page + "");
    router.push({
      pathname: router.pathname,
      query: {
        ...router.query,
        page,
      },
    });
  }, []);

  return (
    <>
      <Stack gap={5} align="center" w={"100%"}>
        {/* Rating and Review Analysis Section */}
        <SystemPollsContainer
          entityType={props.entityType}
          entityId={props.entityId}
          showStatistics={true}
          showCreateButton={false}
        />
      </Stack>
    </>
  );
};
export default PollsContainer;
