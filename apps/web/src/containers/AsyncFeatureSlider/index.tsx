import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import PartyProfile from "@/components/PartyProfile";
import { IParty } from "@/interfaces/IParty";
import { startCase } from "lodash";
import { Badge, Group, Stack } from "@mantine/core";
import { RecommendationBadges } from "../RecommendationBadges";
import { EntityInfo } from "@/components/EntityInfo";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  truncateString,
} from "@/utils";
import Link from "next/link";
import React, { useEffect, useMemo } from "react";
import { IContent } from "@/interfaces/IContent";

const AsyncFeatureSlider = (props: {
  lists?: string[];
  entityType: string;
  entityId: string;
  resourceUrl: string;
  title: string | React.ReactNode;
  fullWidthOnSP?: boolean;
  renderItems: any;
  renderAfterSlider?: (data: any) => React.ReactNode;
  onDataLoad?: (data: any) => void;
  transformResponse?: (data: unknown) => any;
}) => {
  const { t } = useTranslation();
  const query = useQuery(
    [props.resourceUrl, props.entityType, props.entityId],
    async () => {
      const response = await ApiService.resource.getAll(props.resourceUrl, {});
      return response?.data || [];
    }
  );

  const data = useMemo(() => {
    return props.transformResponse?.(query.data) || query.data || [];
  }, [query.data]);

  useEffect(() => {
    if (query.data && props.onDataLoad) {
      props.onDataLoad(data);
    }
  }, [data]);

  return (
    <Stack>
      <FeaturedSlider
        fullWidthOnSP={props.fullWidthOnSP}
        isLoading={query.isLoading}
        data={data}
        title={props.title}
        renderItems={props.renderItems}
      />
      {props.renderAfterSlider?.(data)}
    </Stack>
  );
};
export default AsyncFeatureSlider;
