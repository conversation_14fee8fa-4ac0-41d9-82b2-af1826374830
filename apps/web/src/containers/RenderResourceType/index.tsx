import { IGovernment } from "@/interfaces/IGovernment";
import { ILeader } from "@/interfaces/ILeader";
import { IParliament } from "@/interfaces/IParliament";
import { IParty } from "@/interfaces/IParty";
import { IDepartment } from "@/interfaces/IDepartment";
import { IElections } from "@/interfaces/IElectionSubResponse";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import LeaderProfile from "@/components/LeaderProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
import ParliamentProfile from "@/components/ParliamentProfile";
import DepartmentProfile from "@/components/DepartmentProfile";
import ElectionProfile from "@/components/ElectionProfile";
import ContentProfile from "@/components/ContentProfile";
import { IContent } from "@/interfaces/IContent";
import MunicipalProfile from "@/components/MunicipalProfile";
import ElectionSubProfile from "@/components/ElectionSubProfile";
import { IElectionResult } from "@/interfaces/IElections";
import WardProfile from "@/components/WardProfile";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import MediaProfile from "@/components/MediaProfile";
import { IMedia } from "@/interfaces/IMedia";
import PartyProfile from "@/components/PartyProfile";

const RenderResourceType = (props: {
  resourceType: EntityTypeEnum;
  resource:
    | ILeader
    | IGovernment
    | IParty
    | IParliament
    | IDepartment
    | IElections;
}) => {
  const resourceType = props.resourceType?.toLowerCase();

  if (resourceType === EntityTypeEnum.Leader?.toLowerCase()) {
    return <LeaderHeroProfile data={props.resource as ILeader} />;
  }
  if (resourceType === EntityTypeEnum.Party?.toLowerCase()) {
    return <PartyProfile data={props.resource as IParty} />;
  }
  if (resourceType === EntityTypeEnum.Government?.toLowerCase()) {
    return <GovernmentProfile data={props.resource as IGovernment} />;
  }
  if (resourceType === EntityTypeEnum.Parliament?.toLowerCase()) {
    return <ParliamentProfile data={props.resource as IParliament} />;
  }
  if (resourceType === EntityTypeEnum.Department?.toLowerCase()) {
    return <DepartmentProfile data={props.resource as IDepartment} />;
  }
  if (resourceType === EntityTypeEnum.Election?.toLowerCase()) {
    return <ElectionProfile data={props.resource as IElections} />;
  }
  if (props.resourceType === EntityTypeEnum.ElectionSub?.toLowerCase()) {
    //@ts-expect-error
    return <ElectionSubProfile data={props.resource as IElectionResult} />;
  }
  if (resourceType === EntityTypeEnum.Content?.toLowerCase()) {
    return <ContentProfile data={props.resource as any} />;
  }
  if (resourceType === EntityTypeEnum.Municipal?.toLowerCase()) {
    //@ts-expect-error
    return <MunicipalProfile data={props.resource as IGovernment} />;
  }
  if (resourceType === EntityTypeEnum.Ward?.toLowerCase()) {
    //@ts-expect-error
    return <WardProfile data={props.resource as IGovernment} />;
  }
  if (resourceType === EntityTypeEnum.Media?.toLowerCase()) {
    //@ts-expect-error
    return <MediaProfile data={props.resource as IMedia} />;
  }
  return null;
};
export default RenderResourceType;
