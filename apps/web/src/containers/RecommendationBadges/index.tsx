import { useTranslation } from "next-i18next";
import { Badge, Group } from "@mantine/core";
import {
  IconMapPin,
  IconFlame,
  IconUsers,
  IconMapSearch,
  IconStar,
} from "@tabler/icons-react";
import { startCase } from "lodash";

export function RecommendationBadges({ reasons }: { reasons: string[] }) {
  const { t } = useTranslation();
  return (
    <Group gap="xs">
      {reasons.includes("SAME_ADDRESS") && (
        <Badge color="blue" leftSection={<IconMapPin size={14} />}>
          {t("common:same_area")}
        </Badge>
      )}

      {reasons.includes("TRENDING") && (
        <Badge color="red" leftSection={<IconFlame size={14} />}>
          {t("common:trending")}
        </Badge>
      )}

      {reasons.includes("SAME_PARTY") && (
        <Badge color="grape" leftSection={<IconUsers size={14} />}>
          {t("common:same_party")}
        </Badge>
      )}

      {reasons.includes("SAME_ELECTION_AREA") && (
        <Badge color="teal" leftSection={<IconMapSearch size={14} />}>
          {t("common:same_election_area")}
        </Badge>
      )}
      {reasons.includes("POPULAR") && (
        <Badge color="yellow" leftSection={<IconStar size={14} />}>
          {t("common:popular")}
        </Badge>
      )}

      {!reasons.some((reason) =>
        [
          "SAME_ADDRESS",
          "TRENDING",
          "SAME_PARTY",
          "SAME_ELECTION_AREA",
          "POPULAR",
        ].includes(reason)
      ) && (
        <Badge color="yellow" leftSection={<IconStar size={14} />}>
          {t(`common:${reasons}`, { defaultValue: reasons })}
        </Badge>
      )}
    </Group>
  );
}
