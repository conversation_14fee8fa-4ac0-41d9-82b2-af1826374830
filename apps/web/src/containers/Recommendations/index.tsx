import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import PartyProfile from "@/components/PartyProfile";
import { IParty } from "@/interfaces/IParty";
import { startCase } from "lodash";
import { Badge, Group } from "@mantine/core";
import { RecommendationBadges } from "../RecommendationBadges";
import { EntityInfo } from "@/components/EntityInfo";
import {
  ContentTypeColors,
  ContentTypeIconMap,
  ContentTypeIconMapType,
  truncateString,
} from "@/utils";
import Link from "next/link";
import { useEffect, useMemo } from "react";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import ProjectProfile from "@/components/ProjectProfile";
import DepartmentProfile from "@/components/DepartmentProfile";

const Recommendations = (props: {
  lists?: string[];
  title?: string;
  entityType: string;
  hideBadge?: boolean;
  entityId: string;
  fullWidthOnSP?: boolean;
  disableSeeMore?: boolean;
  renderBeforeItem?: (data: any, index: number) => JSX.Element;
  onDataLoad?: (data: any) => void;
  renderDescription?: (data: any) => JSX.Element;
  renderAfterItem?: (data: any, index: number) => JSX.Element;
  renderChild?: (data: unknown, options: { isLoading: boolean }) => JSX.Element;
}) => {
  const { t } = useTranslation();
  const query = useQuery(
    ["recommendations", props.entityType, props.entityId],
    async () => {
      const response = await ApiService.resource.getAll("recommendations", {
        entityType: props.entityType,
        entityId: props.entityId,
      });
      return response?.data || [];
    }
  );

  useEffect(() => {
    if (query.data && props.onDataLoad) {
      props.onDataLoad(
        //@ts-expect-error
        query.data?.recommendations?.items || query.data?.recommendations || []
      );
    }
  }, [query.data]);

  const memoizedData = useMemo(() => {
    const data =
      //@ts-expect-error
      query.data?.recommendations?.items || query.data?.recommendations || [];

    return data.filter((item: any) => +props.entityId != +item.id);
  }, [query.data]);

  if (props.renderChild) {
    return props.renderChild(memoizedData, { isLoading: query.isLoading });
  }
  return (
    <>
      <FeaturedSlider
        renderAfterItem={props.renderAfterItem}
        fullWidthOnSP={props.fullWidthOnSP}
        isLoading={query.isLoading}
        data={memoizedData}
        title={
          props.title ? (
            props.title
          ) : (
            <Group className="justify-between md:justify-normal items-center">
              {t("common:recommendations")}

              {props.disableSeeMore ? null : (
                <Link
                  href={`/feed?type=${EntityTypeEnum.Recommendations}&category=${props.entityType}`}
                  className="text-inherit text-xs mt-1"
                >
                  {t("common:see_more", "See more")} {">>"}
                </Link>
              )}
            </Group>
          )
        }
        //@ts-expect-error
        renderItems={(item: any) => {
          if (props.entityType === "departments") {
            return <DepartmentProfile data={item} />;
          }
          if (props.entityType === "projects") {
            return <ProjectProfile data={item} />;
          }
          if (props.entityType === "medias") {
            return (
              <EntityInfo
                titleTextAlign="start"
                id={item.id + ""}
                resources={`/medias`}
                name={item.name}
                address={truncateString(item?.description || "", 100)}
                avatar={item.logo}
                rate={item.rating?.average}
              />
            );
          }
          if (props.entityType === "contents") {
            const Icon = (ContentTypeIconMap[
              item.contentType as ContentTypeIconMapType
            ] || ContentTypeIconMap.DEFAULT) as any;
            const color =
              ContentTypeColors[item.contentType as ContentTypeIconMapType] ||
              "gray";

            return (
              <EntityInfo
                titleTextAlign="start"
                id={item.id + ""}
                resources={`/contents/${item.resourceType.toLowerCase()}/${item.contentType.toLowerCase()}/`}
                name={item.title}
                address={truncateString(item.content, 100)}
                title={
                  <Group>
                    <Badge color={color}>
                      {t(`common:${item.contentType}`)}
                    </Badge>{" "}
                    <Badge color={color} variant="outline">
                      {t(`common:${item.contentStatus}`, {
                        defaultValue: startCase(item.contentStatus),
                      })}
                    </Badge>
                  </Group>
                }
              />
            );
          }
          if (props.entityType === "leaders") {
            return (
              <LeaderHeroProfile
                data={item as ILeader}
                additionalInformationEnabled
                renderDescription={
                  props.renderDescription
                    ? (params) => props.renderDescription?.(item)
                    : (params) =>
                        props.hideBadge ? null : (
                          <RecommendationBadges reasons={[item.topic]} />
                        )
                }
              />
            );
          }

          if (props.entityType === "parties") {
            return item.gender ? (
              <LeaderHeroProfile
                data={item as ILeader}
                renderDescription={() =>
                  props.hideBadge ? null : (
                    <RecommendationBadges reasons={[item.topic]} />
                  )
                }
              />
            ) : (
              <PartyProfile data={item as IParty} />
            );
          }

          return null;
        }}
      />
    </>
  );
};
export default Recommendations;
