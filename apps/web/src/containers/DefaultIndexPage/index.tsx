import { useTranslation } from "react-i18next";
import {
  Chip,
  Grid,
  Group,
  Input,
  Pagination,
  Skeleton,
  Stack,
} from "@mantine/core";
import { useRouter } from "next/router";
import { IconSearch } from "@tabler/icons-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import { ApiService } from "../../../api";
import { useQuery } from "react-query";
import Head from "next/head";
import { startCase } from "lodash";
import Link from "next/link";

interface IProps {
  hideSearch?: boolean;
  pageTitle?: string;
  skeletonType?: "card" | "list";
  entityType: EntityTypeEnum;
  resource: string;
  renderItems: (props?: any) => React.ReactNode;
  entityId?: string;
  renderMoreChips?: (props?: any) => React.ReactNode;
  hideChips?: boolean;
  auth?: boolean;
  onDataLoad?: (data: any) => void;
  enableUrlChangeOnPageChange?: boolean;
  disablePageTitle?: boolean;
  renderAfterNoResults?: () => React.ReactNode;
  renderAfterItems?: () => React.ReactNode;
  limit?: number;
}
export function DefaultIndexPage<T>(props: IProps) {
  const router = useRouter();
  const [filter, setFilter] = useState("all");
  const routerPage = router.query.page as string;
  const [page, setPage] = React.useState(+routerPage || 1);
  const [search, setSearch] = useState("");

  const resourceQuery = useQuery(
    [props.entityType, props.resource, page, search, filter],
    async () => {
      const nextQuery = {
        limit: props.limit || 10,
        page: page || 1,
        search,
        top: filter === "all" ? undefined : filter,
        entityId: undefined,
        entityType: "",
      };
      if (props.entityId) {
        //@ts-expect-error
        nextQuery.entityId = props.entityId;
        nextQuery.entityType = props.entityType;
      }
      const response = props.auth
        ? await ApiService.resource.pvtGetAll(`${props.resource}`, nextQuery)
        : await ApiService.resource.getAll(`${props.resource}`, nextQuery);
      return response?.data;
    },
    {
      enabled: !!props.entityType,
    }
  );

  const handleSearch = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
      setFilter("all");
      router.push({
        pathname: router.pathname,
        query: {
          ...router.query,
          page,
          search: e.target.value,
        },
      });
    },
    [router.query]
  );

  const debouncedCallback = useDebouncedCallback(handleSearch, 1000);

  const handlePagination = useCallback((page: number) => {
    setPage(page);

    if (props.enableUrlChangeOnPageChange) {
      router.push({
        pathname: router.pathname,
        query: {
          ...router.query,
          page,
        },
      });
    }
  }, []);

  const total = useMemo(() => {
    return Math.ceil((resourceQuery.data?.totalItems as number) / 10);
  }, [resourceQuery.data?.totalItems]);

  useEffect(() => {
    const { search, ...query } = router.query;
    setSearch("");
    setPage(+routerPage || 1);

    // router.replace({
    //   pathname: router.pathname,
    //   query: {
    //     ...query,
    //     top: filter === "all" ? undefined : filter,
    //   },
    // });
  }, [filter, router.pathname, routerPage]);

  useEffect(() => {
    if (resourceQuery.data?.totalItems) {
      props.onDataLoad?.(resourceQuery.data);
    }
  }, [resourceQuery.data]);
  const { t } = useTranslation();

  return (
    <>
      <Head>
        {!props.disablePageTitle && (
          <title>
            {props.pageTitle +
              " | NepalTracks.com | Nepal’s Ultimate Political Tracker  | Leaders, Elections & Parties"}
          </title>
        )}
      </Head>
      <Stack align="center" p={"md"}>
        {props.hideChips ? null : (
          <Chip.Group value={filter} onChange={(e: any) => setFilter(e)}>
            <Group wrap="nowrap">
              <Chip value="all">{t("common:all")}</Chip>
              <Chip value="rates">{t("common:most_rated")}</Chip>
              <Chip value="views">{t("common:most_viewed")}</Chip>
              {props.renderMoreChips?.()}
            </Group>
          </Chip.Group>
        )}
        {props.hideSearch ? null : (
          <Input
            defaultValue={router.query.search || ""}
            w={{
              base: "100%",
              md: "100%",
            }}
            variant="filled"
            size="xl"
            leftSection={<IconSearch />}
            placeholder={t("common:search", {
              defaultValue: "Search",
            })}
            onChange={debouncedCallback}
          />
        )}

        {resourceQuery.isLoading ? (
          <Grid w={"100%"} gutter={"sm"}>
            {Array.from({ length: 10 }).map((_, index) =>
              props.skeletonType === "card" ? (
                <Skeleton
                  w={"350px"}
                  h={"200px"}
                  mt={"md"}
                  ml={"md"}
                  key={index}
                />
              ) : (
                <>
                  <Skeleton w={"100%"} h={"40px"} key={index} />
                  <br />
                </>
              )
            )}
          </Grid>
        ) : !resourceQuery.data?.items?.length ? (
          <>
            {t("common:no_results_found", {
              defaultValue: "No results found",
            })}
            {props.renderAfterNoResults?.()}
          </>
        ) : (
          <Grid w={"100%"} gutter={"sm"}>
            {props.renderItems?.(resourceQuery.data?.items || [])}
          </Grid>
        )}

        {props.renderAfterItems?.()}
        {/* @ts-expect-error */}
        {resourceQuery?.data?.totalItems <= 10 ? null : (
          <Pagination
            total={total || 0}
            value={page}
            onChange={handlePagination}
          />
        )}
      </Stack>
    </>
  );
}
export default DefaultIndexPage;
