import {
  getImageUrlWithFallback,
  getPublicSiteUrl,
  truncateString,
} from "@/utils";
import { startCase } from "lodash";
import { NextSeo, ArticleJsonLd, ProductJsonLd } from "next-seo";
import Head from "next/head";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";

interface SEOProps {
  data: any;
  type:
    | "leaders"
    | "elections"
    | "parliaments"
    | "parties"
    | "governments"
    | "contents"
    | "wards"
    | "districts"
    | "municipals"
    | "projects"
    | "medias"
    | "departments"
    | "states";
  pageTitle?: string;
  forcePageTitle?: string;
  slug?: string;
}

export function DynamicSEO({
  data,
  type,
  slug,
  pageTitle,
  forcePageTitle,
}: SEOProps) {
  const { t, i18n } = useTranslation();
  const pageUrl = `${getPublicSiteUrl()}/${slug || type}/${data.id}`;
  const router = useRouter();

  const tab = (router.query.tab as string) || "home";
  const isHomeTab = tab === "home";

  const baseTitle = `${capitalize(type)} | NepalTracks.com`;
  const nameInfo = `${data.localName || ""} (${data.name || ""})`;
  const defaultInfo = data.localName
    ? `${data.localName || data.title || ""} (${data.name || data.contentType})`
    : data.name || data.contentType;
  const title = forcePageTitle
    ? forcePageTitle + ` | ${defaultInfo} | ${baseTitle}` || pageTitle
    : !isHomeTab
    ? `${t(`common:${tab}`, startCase(tab))} - ${nameInfo} | ${baseTitle}`
    : `${defaultInfo} | ${baseTitle}` || pageTitle;

  const description = truncateString(
    (i18n.language === "en" ? data.summary : data.summaryNP) ||
      data.description ||
      data.content ||
      data.comment ||
      data.title ||
      data.name ||
      data.localName ||
      data.description ||
      data.content ||
      data.title ||
      data.name ||
      data.localName ||
      data.description ||
      data.content ||
      data.title ||
      data.name ||
      data.localName ||
      data.description ||
      data.content ||
      data.title ||
      data.name ||
      data.localName ||
      data.description ||
      data.content ||
      data.title,
    200
  );

  const imageUrl =
    type === "leaders"
      ? getImageUrlWithFallback(data?.img, data?.ecCandidateID)
      : data.logo ||
        data.img ||
        data.coverImage ||
        data.image ||
        data.avatar ||
        data.resource?.img ||
        data.resource?.logo ||
        data.resource?.img ||
        data.resource?.coverImage ||
        data.resource?.image ||
        data.resource?.avatar ||
        "https://nepaltracks.com/logos/nepaltracks.png";

  return (
    <>
      <Head>
        <meta property="og:image" content={imageUrl} />
        <meta property="og:url" content={pageUrl} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@NepalTracks" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={imageUrl} />
        <title>{title}</title>
        <meta name="description" content={description} />
      </Head>

      <NextSeo
        title={title}
        description={description}
        canonical={pageUrl}
        openGraph={{
          type: type === "leaders" ? "profile" : "article",
          title,
          description,
          url: pageUrl,
          images: [
            {
              url: imageUrl,
              width: 800,
              height: 800,
              alt: `${data.localName}'s Profile Picture`,
            },
          ],
          profile:
            type === "leaders"
              ? {
                  firstName: data.localName?.split(" ")[0],
                  lastName: data.localName?.split(" ")[1],
                  username: data.id + "",
                  gender: data.gender || "male",
                }
              : undefined,
        }}
        twitter={{
          handle: "@NepalTracks",
          site: getPublicSiteUrl(),
          cardType: "summary_large_image",
        }}
      />

      {data?.rating && +data.rating.count > 0 && (
        <ProductJsonLd
          productName={title || ""}
          aggregateRating={{
            ratingValue: data.rating.average || "0",
            ratingCount: data.rating.count || "0",
          }}
        />
      )}

      <ArticleJsonLd
        type="Blog"
        url={pageUrl}
        title={title || ""}
        images={[imageUrl]}
        datePublished={data.createdAt}
        dateModified={data.updatedAt}
        authorName={[
          data.localName || data.name || data.resource?.localName || "",
        ]}
        description={description}
      />
    </>
  );
}

// Helper to capitalize type name
function capitalize(text: string) {
  return text.charAt(0).toUpperCase() + text.slice(1);
}
