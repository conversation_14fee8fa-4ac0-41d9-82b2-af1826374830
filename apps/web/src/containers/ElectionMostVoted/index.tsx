import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import { IParty } from "@/interfaces/IParty";
import { Badge, Stack, Text } from "@mantine/core";
import Link from "next/link";
import { VoteCountBox } from "@/components/VoteCountBox";
import {
  IDistricts,
  IElections,
  IMunicipals,
  IWard,
} from "@/interfaces/IElectionSubResponse";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import { formatConstituencyAddress } from "@/utils";

const ElectionMostVoted = (props: { electionId: string; partyId: string }) => {
  const { t } = useTranslation();
  const query = useQuery(["elections", props.electionId], async () => {
    const response = await ApiService.resource.getAll(
      "elections/" + props.electionId,
      {
        partyId: props.partyId,
      }
    );
    return response?.data || [];
  });

  return (
    <>
      <FeaturedSlider
        isLoading={query.isLoading}
        //@ts-expect-error
        data={query.data?.mostVotedLeaders}
        title={
          t("common:most_voted_leaders", {
            defaultValue: "Most voted leaders",
            //@ts-expect-error
          }) + ` ${query.data?.election?.name}`
        }
        renderItems={({
          leaders,
          voteCount,
          parties,
          election,
          ward,
          districts,
          municipals,
          area,
          ...item
        }: {
          elCode: string;
          candidacyTypeId: number;
          leaders: ILeader;
          voteCount: number;
          parties: IParty;
          election: IElections;
          districts: IDistricts;
          municipals: IMunicipals;
          ward: IWard;
          area: string;
        }) => {
          return (
            <LeaderHeroProfile
              data={leaders as ILeader}
              renderDescription={() => (
                <Stack align="center" gap={5}>
                  <Text>{}</Text>
                  <VoteCountBox count={voteCount} />
                  <Link
                    className="no-underline text-inherit"
                    //@ts-expect-error
                    href={`/elections/${query.data?.electionId}/sub/${item.elCode}/${item.candidacyTypeId}`}
                  >
                    <Stack>
                      {formatConstituencyAddress({
                        municipal: municipals,
                        district: districts,
                        area: area,
                        ward: ward,
                      })}
                    </Stack>
                  </Link>
                </Stack>
              )}
            />
          );
        }}
      />
    </>
  );
};
export default ElectionMostVoted;
