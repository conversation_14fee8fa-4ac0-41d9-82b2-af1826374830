import { useTranslation } from "next-i18next";
import {
  Card,
  Group,
  Stack,
  Text,
  Badge,
  Avatar,
  Box,
  Divider,
  Button,
  Anchor,
} from "@mantine/core";
import {
  IconUsers,
  IconCalendar,
  IconExternalLink,
  IconBuilding,
  IconUser,
  IconFlag,
} from "@tabler/icons-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { ISystemPoll } from "@/interfaces/ISystemPoll";
import SystemPoll from "@/components/SystemPoll";

interface PollCardProps {
  poll: ISystemPoll;
  onVoteSubmitted?: (poll: ISystemPoll) => void;
}

export default function PollCard({ poll, onVoteSubmitted }: PollCardProps) {
  const { t, i18n } = useTranslation();

  const getResourceIcon = () => {
    switch (poll.resourceType) {
      case "LEADER":
        return <IconUser size={16} />;
      case "PARTY":
        return <IconFlag size={16} />;
      case "DEPARTMENT":
        return <IconBuilding size={16} />;
      default:
        return <IconBuilding size={16} />;
    }
  };

  const getResourceLink = () => {
    if (!poll.resourceInfo) return null;

    switch (poll.resourceType) {
      case "LEADER":
        return `/leaders/${poll.resourceId}`;
      case "PARTY":
        return `/parties/${poll.resourceId}`;
      case "DEPARTMENT":
        return `/departments/${poll.resourceId}`;
      default:
        return null;
    }
  };

  const getResourceDisplayName = () => {
    if (!poll.resourceInfo) return `${poll.resourceType} #${poll.resourceId}`;

    const name = i18n.language === "ne" && poll.resourceInfo.nameLocal
      ? poll.resourceInfo.nameLocal
      : poll.resourceInfo.name;

    return name;
  };

  const getResourceSubtitle = () => {
    if (!poll.resourceInfo) return null;

    if (poll.resourceType === "LEADER" && poll.resourceInfo.position) {
      return i18n.language === "ne" && poll.resourceInfo.positionLocal
        ? poll.resourceInfo.positionLocal
        : poll.resourceInfo.position;
    }

    if (poll.resourceType === "PARTY" && poll.resourceInfo.code) {
      return poll.resourceInfo.code;
    }

    return null;
  };

  const resourceLink = getResourceLink();

  return (
    <Card withBorder shadow="sm" p="lg">
      <Stack gap="md">
        {/* Resource Information Header */}
        <Group justify="space-between" align="flex-start">
          <Group gap="sm" style={{ flex: 1 }}>
            {poll.resourceInfo?.avatar || poll.resourceInfo?.logo ? (
              <Avatar
                src={poll.resourceInfo.avatar || poll.resourceInfo.logo}
                size="md"
                radius="sm"
              />
            ) : (
              <Avatar size="md" radius="sm">
                {getResourceIcon()}
              </Avatar>
            )}

            <Box style={{ flex: 1 }}>
              <Group gap="xs" align="center">
                <Badge
                  variant="light"
                  color={
                    poll.resourceType === "LEADER"
                      ? "blue"
                      : poll.resourceType === "PARTY"
                      ? "green"
                      : "orange"
                  }
                  leftSection={getResourceIcon()}
                  size="sm"
                >
                  {t(`common:${poll.resourceType.toLowerCase()}`)}
                </Badge>

                {resourceLink ? (
                  <Link href={resourceLink} passHref legacyBehavior>
                    <Anchor
                      component="a"
                      fw={600}
                      size="sm"
                      className="hover:underline"
                    >
                      <Group gap={4} align="center">
                        {getResourceDisplayName()}
                        <IconExternalLink size={12} />
                      </Group>
                    </Anchor>
                  </Link>
                ) : (
                  <Text fw={600} size="sm">
                    {getResourceDisplayName()}
                  </Text>
                )}
              </Group>

              {getResourceSubtitle() && (
                <Text size="xs" c="dimmed">
                  {getResourceSubtitle()}
                </Text>
              )}

              {poll.resourceInfo?.description && (
                <Text size="xs" c="dimmed" lineClamp={2} mt={2}>
                  {poll.resourceInfo.description}
                </Text>
              )}
            </Box>
          </Group>

          <Group gap="xs" align="center">
            <Badge
              variant="light"
              color="gray"
              leftSection={<IconUsers size={12} />}
              size="sm"
            >
              {poll.totalResponses} {t("common:responses")}
            </Badge>

            <Badge
              variant="light"
              color="gray"
              leftSection={<IconCalendar size={12} />}
              size="sm"
            >
              {formatDistanceToNow(new Date(poll.createdAt), { addSuffix: true })}
            </Badge>

            {poll.hasUserResponded && (
              <Badge variant="light" color="green" size="sm">
                {t("common:voted")}
              </Badge>
            )}
          </Group>
        </Group>

        <Divider />

        {/* Poll Component */}
        <SystemPoll
          poll={poll}
          onVoteSubmitted={onVoteSubmitted}
          showResults={false}
        />
      </Stack>
    </Card>
  );
}
