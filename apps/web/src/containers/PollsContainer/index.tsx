import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "next-i18next";
import {
  Box,
  Card,
  Group,
  Stack,
  Text,
  TextInput,
  Select,
  Button,
  Skeleton,
  Center,
  Alert,
  Badge,
  Divider,
} from "@mantine/core";
import {
  IconSearch,
  IconFilter,
  IconSortDescending,
  IconSortAscending,
  IconRefresh,
  IconAlertCircle,
} from "@tabler/icons-react";
import { useInfiniteQuery } from "react-query";
import { useIntersection } from "@mantine/hooks";
import { ApiService } from "../../../api";
import { useProfile } from "@/store";
import { IPollsResponse } from "@/interfaces/ISystemPoll";
import PollCardSkeleton from "./PollCardSkeleton";
import PollCard from "./PollCard";

interface PollsFilters {
  search: string;
  userVoted?: boolean;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

export default function PollsContainer() {
  const { t } = useTranslation();
  const profile = useProfile();
  const isLoggedIn = !!profile.profile?.firstName;

  const [filters, setFilters] = useState<PollsFilters>({
    search: "",
    userVoted: undefined,
    sortBy: "responses",
    sortOrder: "desc",
  });

  const [debouncedSearch, setDebouncedSearch] = useState("");

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(filters.search);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters.search]);

  // Infinite query for polls
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
  } = useInfiniteQuery<IPollsResponse>(
    [
      "allPolls",
      debouncedSearch,
      filters.userVoted,
      filters.sortBy,
      filters.sortOrder,
      isLoggedIn,
    ],
    async ({ pageParam = 1 }) => {
      const params = {
        page: pageParam,
        limit: 10,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        ...(debouncedSearch && { search: debouncedSearch }),
        ...(filters.userVoted !== undefined && {
          userVoted: filters.userVoted,
        }),
      };

      const response = isLoggedIn
        ? await ApiService.getAllPollsWithAuth(params)
        : await ApiService.getAllPolls(params);

      // The API response structure is: { statusCode: 200, data: { data: [...], pagination: {...} } }
      // So we need to access response.data.data
      return response.data.data;
    },
    {
      getNextPageParam: (lastPage) => {
        return lastPage.pagination.hasNext
          ? lastPage.pagination.page + 1
          : undefined;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Intersection observer for infinite scroll
  const { ref, entry } = useIntersection({
    threshold: 1,
  });

  useEffect(() => {
    if (entry?.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [entry?.isIntersecting, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Get all polls from pages
  const allPolls = data?.pages.flatMap((page) => page.data) || [];
  const totalPolls = data?.pages[0]?.pagination.total || 0;

  const handleFilterChange = useCallback(
    (key: keyof PollsFilters, value: any) => {
      setFilters((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  const handleSortToggle = useCallback(() => {
    setFilters((prev) => ({
      ...prev,
      sortOrder: prev.sortOrder === "desc" ? "asc" : "desc",
    }));
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handlePollVoteSubmitted = useCallback(() => {
    // Refetch to get updated data
    refetch();
  }, [refetch]);

  if (isError) {
    return (
      <Alert
        icon={<IconAlertCircle size={16} />}
        color="red"
        title={t("common:error")}
      >
        {t("common:failed_to_load_polls")}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Filters */}
      <Card withBorder mb="lg" p="md">
        <Stack gap="md">
          <Group justify="space-between" align="center">
            <Text fw={600} size="lg">
              {t("common:filters")}
            </Text>
            <Badge variant="light" color="blue">
              {totalPolls} {t("common:polls")}
            </Badge>
          </Group>

          <Group grow>
            <TextInput
              placeholder={t("common:search_polls")}
              leftSection={<IconSearch size={16} />}
              value={filters.search}
              onChange={(e) => handleFilterChange("search", e.target.value)}
            />

            {isLoggedIn && (
              <Select
                placeholder={t("common:vote_status")}
                leftSection={<IconFilter size={16} />}
                data={[
                  { value: "", label: t("common:all_polls") },
                  { value: "true", label: t("common:voted") },
                  { value: "false", label: t("common:not_voted") },
                ]}
                value={filters.userVoted?.toString() || ""}
                onChange={(value) =>
                  handleFilterChange(
                    "userVoted",
                    value === "" ? undefined : value === "true"
                  )
                }
              />
            )}

            <Select
              placeholder={t("common:sort_by")}
              data={[
                { value: "responses", label: t("common:most_responses") },
                { value: "createdAt", label: t("common:newest_first") },
              ]}
              value={filters.sortBy}
              onChange={(value) =>
                handleFilterChange("sortBy", value || "responses")
              }
            />

            <Button
              variant="light"
              leftSection={
                filters.sortOrder === "desc" ? (
                  <IconSortDescending size={16} />
                ) : (
                  <IconSortAscending size={16} />
                )
              }
              onClick={handleSortToggle}
            >
              {filters.sortOrder === "desc"
                ? t("common:descending")
                : t("common:ascending")}
            </Button>

            <Button
              variant="light"
              leftSection={<IconRefresh size={16} />}
              onClick={handleRefresh}
              loading={isLoading}
            >
              {t("common:refresh")}
            </Button>
          </Group>
        </Stack>
      </Card>

      {/* Polls List */}
      <Stack gap="md">
        {isLoading && allPolls.length === 0 ? (
          // Initial loading skeletons
          Array.from({ length: 5 }).map((_, index) => (
            <PollCardSkeleton key={index} />
          ))
        ) : allPolls.length === 0 ? (
          // No polls found
          <Card withBorder p="xl">
            <Center>
              <Stack align="center" gap="md">
                <IconAlertCircle size={48} color="gray" />
                <Text size="lg" fw={500} c="dimmed">
                  {t("common:no_polls_found")}
                </Text>
                <Text size="sm" c="dimmed" ta="center">
                  {t("common:try_adjusting_filters")}
                </Text>
              </Stack>
            </Center>
          </Card>
        ) : (
          // Polls list
          allPolls.map((poll) => (
            <PollCard
              key={poll.id}
              poll={poll}
              onVoteSubmitted={handlePollVoteSubmitted}
            />
          ))
        )}

        {/* Loading more indicator */}
        {isFetchingNextPage && (
          <Stack gap="md">
            {Array.from({ length: 3 }).map((_, index) => (
              <PollCardSkeleton key={`loading-${index}`} />
            ))}
          </Stack>
        )}

        {/* Intersection observer target */}
        {hasNextPage && <div ref={ref} style={{ height: 1 }} />}

        {/* End of results */}
        {!hasNextPage && allPolls.length > 0 && (
          <Card withBorder p="md">
            <Center>
              <Text size="sm" c="dimmed">
                {t("common:end_of_results")}
              </Text>
            </Center>
          </Card>
        )}
      </Stack>
    </Box>
  );
}
