import {
  Card,
  Group,
  Stack,
  Skeleton,
  Box,
  Divider,
} from "@mantine/core";

export default function PollCardSkeleton() {
  return (
    <Card withBorder shadow="sm" p="lg">
      <Stack gap="md">
        {/* Resource Information Header Skeleton */}
        <Group justify="space-between" align="flex-start">
          <Group gap="sm" style={{ flex: 1 }}>
            <Skeleton height={40} width={40} radius="sm" />

            <Box style={{ flex: 1 }}>
              <Group gap="xs" align="center" mb="xs">
                <Skeleton height={20} width={80} radius="sm" />
                <Skeleton height={16} width={120} radius="sm" />
              </Group>
              <Skeleton height={12} width={100} radius="sm" mb={4} />
              <Skeleton height={12} width={200} radius="sm" />
            </Box>
          </Group>

          <Group gap="xs" align="center">
            <Skeleton height={20} width={80} radius="sm" />
            <Skeleton height={20} width={90} radius="sm" />
          </Group>
        </Group>

        <Divider />

        {/* Poll Content Skeleton */}
        <Stack gap="md">
          {/* Poll Title and Question */}
          <Box>
            <Skeleton height={24} width="80%" radius="sm" mb="xs" />
            <Skeleton height={16} width="90%" radius="sm" mb="xs" />
            <Skeleton height={14} width="70%" radius="sm" />
          </Box>

          {/* Poll Options */}
          <Stack gap="sm">
            {Array.from({ length: 3 }).map((_, index) => (
              <Box
                key={index}
                p="sm"
                style={{
                  border: "1px solid var(--mantine-color-gray-3)",
                  borderRadius: "var(--mantine-radius-sm)",
                }}
              >
                <Group justify="space-between">
                  <Group gap="sm">
                    <Skeleton height={16} width={16} radius="sm" />
                    <Skeleton height={16} width={150 + index * 30} radius="sm" />
                  </Group>
                  <Group gap="xs">
                    <Skeleton height={16} width={20} radius="sm" />
                    <Skeleton height={16} width={40} radius="sm" />
                  </Group>
                </Group>
              </Box>
            ))}
          </Stack>

          {/* Vote Button */}
          <Group justify="center">
            <Skeleton height={32} width={120} radius="sm" />
          </Group>
        </Stack>
      </Stack>
    </Card>
  );
}
