import {
  <PERSON>chor,
  Avatar,
  Badge,
  Button,
  Chip,
  Group,
  Stack,
  Tabs,
  Text,
} from "@mantine/core";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { IElections } from "@/interfaces/IElectionSubResponse";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import GeoDropdown from "@/components/GeoDropdown";
import { PaginatedTableProps } from "@/components/PaginatedTable";
import RequestPaginatedTable from "../RequestPaginatedTable";
import { formatNumber } from "@/utils";
import ElectionResultsContents from "../ElectionResultContents";
import { Carousel } from "@mantine/carousel";
import { IconCheck } from "@tabler/icons-react";

interface IProps {
  partyId: number;
}
const ElectionInTab = (props: IProps) => {
  const [tab, selectTab] = useState("");
  //@ts-expect-error
  const tabsQuery = useQuery<null, IElections[]>("elections", async () => {
    try {
      const { data } = await ApiService.resource.getAll<IElections[]>(
        "elections",
        {}
      );
      return data;
    } catch (error) {
      throw new Error("Failed to fetch elections data");
    }
  });

  useEffect(() => {
    if (tabsQuery.data && !tab) {
      //@ts-expect-error
      selectTab(tabsQuery.data[0].id + "");
    }
  }, [tabsQuery, tab]);

  return (
    <Stack p="xs">
      <Carousel slideSize="20%" height={40} align="start" slideGap="md">
        {/* @ts-ignore */}
        {tabsQuery.data?.map?.((election: IElections, index: number) => (
          <Carousel.Slide key={index} ml={"lg"}>
            <Button
              onClick={() => selectTab(election.id + "")}
              size="xs"
              variant="outline"
              radius={"lg"}
              leftSection={
                <Avatar
                  size={"sm"}
                  src={"/images/others/ec-nepal.png"}
                  radius={"md"}
                />
              }
              rightSection={tab === election.id + "" ? <IconCheck /> : null}
            >
              <Text fz="sm" fw={500}>
                {election.name}
              </Text>
            </Button>
          </Carousel.Slide>
        ))}
      </Carousel>
      <Tabs
        w={"100%"}
        orientation={"horizontal"}
        value={tab}
        onChange={(e) => selectTab(e as string)}
      >
        {/* @ts-ignore */}
        {tabsQuery.data?.map?.((election: IElections, index: number) => (
          <Tabs.Panel value={election.id + ""} key={election.id + ""} p={"md"}>
            <ElectionResultsContents
              key={tab}
              url={`parties/${props.partyId}/elections/${election.id}/leaders`}
              electionId={election.id + ""}
            />
          </Tabs.Panel>
        ))}
      </Tabs>
    </Stack>
  );
};
export default ElectionInTab;
