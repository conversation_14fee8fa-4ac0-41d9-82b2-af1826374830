import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import Storage from "@/core/Storage";
import { useProfile } from "@/store";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import PartyProfile from "@/components/PartyProfile";
import { IParty } from "@/interfaces/IParty";
import { useTranslation } from "react-i18next";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import MediaProfile from "@/components/MediaProfile";
import { IMedia } from "@/interfaces/IMedia";
import { RecommendationBadges } from "../RecommendationBadges";
import ProjectProfile from "@/components/ProjectProfile";
import DepartmentProfile from "@/components/DepartmentProfile";
import ContentProfile from "@/components/ContentProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
import WardProfile from "@/components/WardProfile";
import ElectionSubProfile from "@/components/ElectionProfile";
import ParliamentProfile from "@/components/ParliamentProfile";

const RecentlySelfViewed = (props: {
  lists?: string[];
  entityId?: string;
  fullWidthOnSP?: boolean;
}) => {
  const profile = useProfile();
  const items =
    Storage.getInstance().get(`viewed:${profile?.profile?.id || "none"}`) || [];

  const query = useQuery(["recentlyviewed"], async () => {
    const response = await ApiService.resource.post(
      "most-viewed/self-viewed",
      items
    );
    //@ts-expect-error
    return response?.data || [];
  });
  const { t } = useTranslation();

  return (
    <>
      {Object.keys(query.data || {}).map((key) =>
        props.lists && !props.lists.includes(key) ? null : (
          <FeaturedSlider
            fullWidthOnSP={key === "parties" || props.fullWidthOnSP}
            key={key}
            data={query.data[key].filter(
              //@ts-expect-error
              (item) => item?.id + "" !== props.entityId
            )}
            title={
              t(`common:recentlyviewed`, {
                defaultValue: `Recently viewed ${key}`,
              }) +
              " " +
              t(`common:${key}`)
            }
            //@ts-expect-error
            renderItems={(item) => {
              if (key === "departments") {
                return <DepartmentProfile data={item as any} />;
              }
              if (key === "projects") {
                return <ProjectProfile data={item as any} />;
              }
              if (key === "medias") {
                return <MediaProfile data={item as IMedia} />;
              }

              if (key === "leaders") {
                return <LeaderHeroProfile data={item as ILeader} />;
              }

              if (key === "parties") {
                return <PartyProfile data={item as IParty} />;
              }
              if (key === "contents") {
                return <ContentProfile data={item as any} />;
              }
              if (key === "governments") {
                return <GovernmentProfile data={item as any} />;
              }
              if (key === "wards") {
                return <WardProfile data={item as any} />;
              }
              if (key === "municipals") {
                return <GovernmentProfile data={item as any} />;
              }
              if (key === "elections") {
                return <ElectionSubProfile data={item as any} />;
              }
              if (key === "parliaments") {
                return <ParliamentProfile data={item as any} />;
              }

              return null;
            }}
          />
        )
      )}
    </>
  );
};
export default RecentlySelfViewed;
