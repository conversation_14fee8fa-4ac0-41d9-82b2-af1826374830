import { useTranslation } from "next-i18next";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import { IContentStats } from "@/interfaces/IContentStats";
import {
  ContentStatusColors,
  ContentStatusColorsIcons,
  ContentTypeColors,
  ContentTypeIconMap,
} from "@/utils";
import { Card, Text, Progress, Badge, Group, Stack } from "@mantine/core";
import Link from "next/link";
import { useRouter } from "next/router";

const mapContentTypeToPath = {
  SCANDAL: "scandals",
  CONTROVERSIES: "scandals",
  ACHIEVEMENTS: "achievements",
  MILESTONES: "achievements",
  ANNOUNCEMENTS: "announcements",
  PROMISES: "announcements",
};

const StatsCard = ({
  contentType,
  total,
  statuses,
  resourceId,
  resourceType,
}: {
  contentType: keyof typeof mapContentTypeToPath;
  resourceId?: string;
  resourceType?: string;
  total: number;
  statuses: Record<string, number>;
}) => {
  const contentTypeColor = ContentTypeColors[contentType];
  const ContentTypeIcon = ContentTypeIconMap[contentType];
  const segments = Object.entries(statuses).map(([status, count]) => {
    //@ts-ignore
    const percentage = ((count / total) * 100).toFixed(2);
    return {
      status,
      part: parseFloat(percentage),
      color: status === "COMPLETED" ? "teal" : "gray", // Custom color logic
      label: `${status.replace(/_/g, " ").toLowerCase()} (${percentage}%)`,
    };
  });
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <Card
      withBorder
      radius="md"
      padding="xl"
      bg="var(--mantine-color-body)"
      className="cursor-pointer"
      onClick={() => {
        if (!mapContentTypeToPath[contentType] || !resourceId || !resourceType)
          return;
        router.push(
          `/${resourceType}/${resourceId}/${mapContentTypeToPath[contentType]}`
        );
      }}
    >
      <Badge
        radius={"xs"}
        size="lg"
        leftSection={ContentTypeIcon ? <>{ContentTypeIcon}</> : null}
        color={contentTypeColor}
        variant={"transparent"}
      >
        {t(`common:${contentType}`, {
          defaultValue: contentType.replace(/_/g, " ").toLowerCase(),
        })}
      </Badge>
      <Text fz="sm" c="dimmed" mb="sm">
        {t("common:total")}: <b>{total}</b>
      </Text>
      <div style={{ marginBottom: 12 }}>
        <Progress.Root size={20}>
          {segments.map(({ part, color, label, status }) => {
            const contentStatusColor = ContentStatusColors[status];

            return (
              <Progress.Section
                key={color}
                value={part}
                color={contentStatusColor}
              >
                {part > 10 && <Progress.Label>{part}%</Progress.Label>}
              </Progress.Section>
            );
          })}
        </Progress.Root>{" "}
        <Stack mt={10}>
          <Group gap={1}>
            {Object.keys(statuses)?.map((status, index) => {
              const contentStatusColor = ContentStatusColors[status];
              //@ts-ignore
              const ContentStatusIcon = ContentStatusColorsIcons[status];

              return (
                <Badge
                  key={index}
                  size="xs"
                  radius={"xs"}
                  leftSection={ContentStatusIcon ? <ContentStatusIcon /> : null}
                  color={contentStatusColor}
                >
                  {t(`common:${status}`, {
                    defaultValue: status.replace(/_/g, " ").toLowerCase(),
                  })}
                </Badge>
              );
            })}
          </Group>
        </Stack>
      </div>
    </Card>
  );
};

export default function ContentStatsCards({
  stats,
  title,
  resourceId,
  resourceType,
}: {
  stats: IContentStats;
  title?: string;
  resourceId?: string;
  resourceType?: string;
}) {
  const { t } = useTranslation();
  // Flatten stats into a consumable array
  const statCards = Object.entries(stats).flatMap(([contentType, data]) =>
    Object.entries(data.statuses).map(([status, count]) => ({
      contentType,
      status,
      count,
      total: data.total,
      //@ts-ignore
      percentage: parseFloat(data.percentages[status]),
    }))
  );

  // Inside your render
  return (
    <FeaturedSlider
      isLoading={false}
      title={title === undefined ? t("common:track_records") : title}
      data={
        Object.entries(stats) as [
          keyof typeof mapContentTypeToPath,
          {
            total: number;
            statuses: Record<string, number>;
            percentages: Record<string, string>;
          }
        ][]
      }
      fullWidthOnSP
      renderItems={function ([contentType, { total, statuses }]: [
        keyof typeof mapContentTypeToPath,
        { total: number; statuses: Record<string, number> }
      ]) {
        return (
          <StatsCard
            key={contentType}
            contentType={contentType}
            total={total}
            statuses={statuses}
            resourceId={resourceId}
            resourceType={resourceType}
          />
        );
      }}
    />
  );
}
