import { IconArrowUpRight, IconHierarchy, IconUser } from "@tabler/icons-react";
import {
  Avatar,
  Box,
  Group,
  Paper,
  Progress,
  SimpleGrid,
  Text,
} from "@mantine/core";
import classes from "./style.module.css";
import { useTranslation } from "next-i18next";

// Example cabinet distribution by political parties
interface ICabinetData {
  label: string;
  count: number;
  part: number;
  color?: string;
  img?: string;
}
export function CabinetDistributionByParty({
  data: cabinetData,
}: {
  data: ICabinetData[];
}) {
  const { t } = useTranslation();
  const allMembersCount = cabinetData.reduce(
    (acc, item) => acc + item.count,
    0
  );

  const segments = cabinetData.map((segment) => (
    <Progress.Section
      value={segment.part}
      color={segment.color}
      key={segment.color}
    >
      {segment.part > 10 && <Progress.Label>{segment.part}%</Progress.Label>}
    </Progress.Section>
  ));

  const descriptions = cabinetData.map((stat) => (
    <Box
      key={stat.label}
      style={{ borderBottomColor: stat.color }}
      className={classes.stat}
    >
      <Group gap={5}>
        <Avatar src={stat.img} size={"sm"} radius={100} />
        <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
          {stat.label}
        </Text>
      </Group>

      <Group justify="space-between" align="flex-end" gap={0}>
        <Text fw={700}>{stat.count}</Text>
        <Text c={stat.color} fw={700} size="sm" className={classes.statCount}>
          {stat.part}%
        </Text>
      </Group>
    </Box>
  ));

  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="space-between">
        <Group align="flex-end" gap="xs">
          <Text fz="xl" fw={700}>
            {allMembersCount}
            <IconUser size={16} stroke={1.5} />
          </Text>
        </Group>
        <IconHierarchy size={22} className={classes.icon} stroke={1.5} />
      </Group>
      {/* <Text c="dimmed" fz="sm">
        {t("common:cabinet_distribution_by_party", {
          defaultValue: "Cabinet distribution by party",
        })}
      </Text> */}
      <Progress.Root
        size={34}
        classNames={{ label: classes.progressLabel }}
        mt={40}
      >
        {segments}
      </Progress.Root>
      <SimpleGrid cols={{ base: 1, xs: 3 }} mt="xl">
        {descriptions}
      </SimpleGrid>
    </Paper>
  );
}
