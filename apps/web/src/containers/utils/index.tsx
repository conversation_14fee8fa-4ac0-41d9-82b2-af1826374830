import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import ContentsContainer from "../ContentsContainer";
import { useCallback, useMemo, useState } from "react";
import { capitalize } from "lodash";
import { IContentStats } from "@/interfaces/IContentStats";
import { useTranslation } from "react-i18next";
import { Stack } from "@mantine/core";
import AsyncProjectOfEntitySlider from "../AsyncProjectOfEntitySlider";
import ProjectProfile from "@/components/ProjectProfile";
import AsyncFeatureSlider from "../AsyncFeatureSlider";
import ContentProfile from "@/components/ContentProfile";

export const getContentMenus = (
  resourceId: string,
  resourceType: EntityTypeEnum,
  stats?: IContentStats,
  additionalContentTypes?: {
    key: string;
    label: string;
    types: string[];
  }[]
) => {
  const { t } = useTranslation();
  const menuConfig = [
    ...(additionalContentTypes || []),
    {
      key: "announcements",
      label: "promises",
      types: ["ANNOUNCEMENTS", "PROMISES"],
    },
    {
      key: "scandals",
      label: "scandals",
      types: ["SCANDAL", "CONTROVERSIES"],
    },
    {
      key: "achievements",
      label: "achievements",
      types: ["ACHIEVEMENTS", "MILESTONES"],
    },
  ];

  //@ts-ignore
  const getTotal = (types) =>
    //@ts-ignore
    types.reduce((sum, type) => sum + (stats?.[type]?.total || 0), 0);

  const menus = useMemo(
    () =>
      menuConfig.map(({ key, label, types }) => {
        const total = getTotal(types);
        return {
          label: `${t(`${label}`, {
            defaultValue: label,
          })}${total ? ` (${total})` : ""}`,
          value: key,
          contentType: types,
        };
      }),
    [stats, resourceId, resourceType]
  );

  return menus.map((menu) => ({
    ...menu,
    label: t(`${menu.label}`, {
      defaultValue: menu.label,
    }),
    content: (
      <Stack>
        {menu.value === "achievements" && (
          <Stack>
            <AsyncProjectOfEntitySlider
              lists={["projects"]}
              entityId={resourceId as string}
              entityType={resourceType}
              resourceUrl={
                resourceType === EntityTypeEnum.Leader
                  ? `leaders/${resourceId}`
                  : resourceType === EntityTypeEnum.Party
                  ? `parties/${resourceId}`
                  : resourceType === EntityTypeEnum.Government
                  ? `governments/${resourceId}`
                  : `projects/${resourceId}`
              }
              title={"Projects related to this leader"}
              renderItems={(item: any) => {
                return <ProjectProfile data={item} />;
              }}
            />
            <AsyncFeatureSlider
              //@ts-expect-error
              transformResponse={(data) => data?.items || []}
              lists={["projects"]}
              entityId={resourceId as string}
              entityType={resourceType}
              resourceUrl={`contents?resourceType=${resourceType}&resourceId=${resourceId}&contentType=ACHIEVEMENTS,MILESTONES&limit=10&page=1&search=&entityType=&top=rates`}
              title={"Rated Achievements & Milestones"}
              renderItems={(item: any) => {
                console.log(item);
                return <ContentProfile data={item} />;
              }}
            />
          </Stack>
        )}
        {menu.value === "scandals" && (
          <AsyncFeatureSlider
            //@ts-expect-error
            transformResponse={(data) => data?.items || []}
            lists={["projects"]}
            entityId={resourceId as string}
            entityType={resourceType}
            resourceUrl={`contents?resourceType=${resourceType}&resourceId=${resourceId}&contentType=SCANDAL,CONTROVERSIES&limit=10&page=1&search=&entityType=&top=rates`}
            title={"Top Scandals & Controversies"}
            renderItems={(item: any) => {
              console.log(item);
              return <ContentProfile data={item} />;
            }}
          />
        )}
        {menu.value === "announcements" && (
          <AsyncFeatureSlider
            //@ts-expect-error
            transformResponse={(data) => data?.items || []}
            lists={["projects"]}
            entityId={resourceId as string}
            entityType={resourceType}
            resourceUrl={`contents?resourceType=${resourceType}&resourceId=${resourceId}&contentType=ANNOUNCEMENTS,PROMISES&limit=10&page=1&search=&entityType=&top=rates`}
            title={"Rated Announcements & Promises"}
            renderItems={(item: any) => {
              console.log(item);
              return <ContentProfile data={item} />;
            }}
          />
        )}

        <ContentsContainer
          disablePageTitle
          resourceId={resourceId as string}
          resourceType={resourceType}
          contentType={menu.contentType as string[]}
          title={menu.label}
        />
      </Stack>
    ),
  }));
};
