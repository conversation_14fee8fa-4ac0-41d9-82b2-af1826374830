import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import PartiesTable, {
  IElectionPartyResponse,
} from "@/components/PartiesTable";
import {
  <PERSON>,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { Box, Stack, Title } from "@mantine/core";
import SeatDistribution from "@/components/SeatDistribution";
import SegmentedTab from "@/components/SegmentedTab";
import { FEDERAL_PARLIAMENT_TOTAL_SEATS } from "@/pages/parliaments/[parliamentId]/[tab]";
import { CabinetDistributionByParty } from "../Sections/CabinetDistributionByParty";

interface PartiesTabProps {
  entityId: string;
  displayCharts?: boolean;
  resources: string;
  hideTable?: boolean;
  noTreeMap?: boolean;
  onlyWinner?: boolean;
}

const ElectionPartiesTab: React.FC<PartiesTabProps> = (props) => {
  const electionsQuery = useQuery(
    ["data", props.entityId, props.resources],
    async () => {
      const response = await ApiService.resource.getAll(
        `${props.resources}/${props.entityId}/parties`,
        {}
      );
      return response.data as unknown as IElectionPartyResponse[];
    }
  );
  const parties = electionsQuery.data || [];
  const totalSeats = parties.reduce((sum, p) => sum + p.wonPlaces, 0);
  const seats = [];

  parties.forEach((party) => {
    for (let i = 0; i < party.wonPlaces; i++) {
      seats.push(
        <Tooltip label="Vacant / Pending" key={`vacant-${i}`}>
          <div className="w-4 h-4 rounded-sm bg-gray-300" />
        </Tooltip>
      );
    }
  });
  const wonSeats = parties.reduce((sum, p) => sum + p.wonPlaces, 0);
  const remainingSeats = totalSeats - wonSeats;

  const data = parties
    .map((p) => ({
      name: p.party.code,
      seats: p.wonPlaces,
      color: p.party.partyColorCode,
      img: p.party.logo,
      percentage: ((p.wonPlaces / totalSeats) * 100).toFixed(2),
    }))
    .filter((item) => (props.onlyWinner ? item.seats > 0 : true));

  const seatData = parties
    .map((party, index) => ({
      name: party.party.localName,
      seats: party.wonPlaces,
      color: party.party.partyColorCode,
      img: party.party.logo,
      percentage: (party.wonPlaces / party.runningPlaces) * 100,
    }))
    .filter((item) => (props.onlyWinner ? item.seats > 0 : true));
  const { t } = useTranslation("common");

  return (
    <Stack>
      {props.displayCharts && (
        <Stack>
          <Stack>
            <Title order={4}>{t("common:seats_distribution")}</Title>
            <SegmentedTab
              defaultValue={"bar"}
              entityId={props.entityId}
              resources={props.resources}
              disableRouting
              data={[
                {
                  label: "Bar",
                  value: "bar",
                  content: (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
                      {data.map((party) => (
                        <div
                          key={party.name}
                          className="flex items-center p-3 rounded-lg shadow bg-white"
                        >
                          <div
                            className="w-4 h-4 rounded-full mr-3"
                            style={{ backgroundColor: party.color }}
                          ></div>
                          <div>
                            <div className="font-semibold text-gray-800">
                              {party.name}
                            </div>
                            <div className="text-sm text-gray-600">
                              {party.seats} seats
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ),
                },
                props.noTreeMap
                  ? undefined
                  : {
                      label: t("common:treemap"),
                      value: "treemap",
                      content: (
                        <Box w={"100%"} h={"500px"}>
                          <SeatDistribution seatData={seatData} />
                        </Box>
                      ),
                    },
              ]}
            />
          </Stack>

          <SegmentedTab
            defaultValue={"bar"}
            entityId={props.entityId}
            resources={props.resources}
            disableRouting
            data={[
              {
                label: "Bar",
                value: "bar",
                content: (
                  <Stack>
                    <CabinetDistributionByParty
                      // @ts-expect-error
                      data={data.map((item) => ({
                        color: item.color || "",
                        count: item.seats,
                        label: item.name,
                        part: item.percentage,
                        img: item.img,
                      }))}
                    />
                    <ResponsiveContainer width="100%" height={400}>
                      <BarChart
                        width={500}
                        height={300}
                        data={data}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey={"name"} />
                        <YAxis domain={[0, FEDERAL_PARLIAMENT_TOTAL_SEATS]} />
                        <Tooltip />
                        <Bar dataKey={"seats"} background={{ fill: "#eee" }}>
                          {data.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </Stack>
                ),
              },
              props.noTreeMap
                ? undefined
                : {
                    label: "Pie",
                    value: "pie",
                    content: (
                      <ResponsiveContainer width="100%" height={400}>
                        <PieChart>
                          <Pie
                            dataKey={"seats"}
                            data={seatData}
                            cx="50%"
                            cy="50%"
                            outerRadius={130}
                            label
                          >
                            {seatData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    ),
                  },
            ]}
          />
        </Stack>
      )}
      {props.hideTable ? null : (
        <PartiesTable
          isLoading={electionsQuery.isLoading}
          //@ts-expect-error
          items={electionsQuery.data?.items || electionsQuery.data || []}
        />
      )}
    </Stack>
  );
};
export default ElectionPartiesTab;
