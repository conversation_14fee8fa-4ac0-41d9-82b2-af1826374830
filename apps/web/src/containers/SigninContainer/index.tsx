import { useTranslation } from "next-i18next";
import AppTextInput from "@/components/AppTextInput";
import { ERR_STRONG_PASSWORD } from "@/consts";
import Storage from "@/core/Storage";
import useLogin from "@/hooks/useLogin";
import { useGlobalState } from "@/store";
import {
  Checkbox,
  Anchor,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Button,
  Stack,
  Divider,
} from "@mantine/core";
import { useForm, zodResolver } from "@mantine/form";
import Link from "next/link";
import React from "react";
import { z } from "zod";
import { useTurnstileToken } from "@/hooks/useTurnsiteToken";
import { GetServerSideProps } from "next";
import { loadTranslation } from "@/i18n";
import Head from "next/head";
import { FaFacebookF } from "react-icons/fa";
import { SocialSignin } from "@/components/SocialSign";

const schema = z.object({
  password: z.string().min(8, ERR_STRONG_PASSWORD),
  email: z.string().email(),
});

const SigninContainer = (props: { returnUrl?: string }) => {
  const { t } = useTranslation();
  const token = useTurnstileToken(true);

  const [setGlobalMessage, setGlobalLoading] = useGlobalState((s) => [
    s.setMessage,
    s.toggleLoading,
  ]);
  const { login } = useLogin();

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: zodResolver(schema),
  });

  const handleSubmit = React.useCallback(
    async (values: typeof form.values) => {
      try {
        if (!token) return;
        setGlobalLoading(true);
        login({ ...values, turnstileToken: token });
        setTimeout(() => {
          Storage.getInstance().set("returnUrl", props.returnUrl || "");
        }, 10000);
      } catch (err: any) {
        setGlobalLoading(false);
        setGlobalMessage(err.response.data.message);
      }
    },
    [token]
  );

  return (
    <Stack>
      <Title ta="center">
        {t("common:welcome_back", { defaultValue: "Welcome Back" })}
      </Title>
      <Text color="dimmed" size="sm" ta="center" mt={5}>
        {t("common:dont_have_account", {
          defaultValue: "Don`t have an account yet?",
        })}{" "}
        <Link href={"/auth/signup"} className="no-underline">
          <Anchor size="sm" component="button">
            {t("common:create_account", { defaultValue: "Create account" })}
          </Anchor>
        </Link>
      </Text>
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Paper withBorder shadow="md" p={30} mt={30} radius="md">
          <Stack>
            <SocialSignin />
            <Divider
              label={t("common:or_continue_with_email", {
                defaultValue: "Or continue with email",
              })}
              labelPosition="center"
            />
            <AppTextInput
              withAsterisk
              placeholder={t("common:email", { defaultValue: "Email" })}
              {...form.getInputProps("email")}
            />
            <AppTextInput
              mt={10}
              withAsterisk
              placeholder={t("common:password", { defaultValue: "Password" })}
              type="password"
              {...form.getInputProps("password")}
            />
            {/* <Group justify="apart" mt="lg">
            <Checkbox label="Remember me" />
            <Anchor component="button" size="sm">
              Forgot password?
            </Anchor>
          </Group> */}
            <div
              id="cf-container"
              className="cf-turnstile"
              data-sitekey={process.env.NEXT_PUBLIC_CF_TURNSTILE_SITE_KEY}
              data-callback="javascriptCallback"
            ></div>

            <Button type="submit" fullWidth mt="xl">
              {t("common:login", { defaultValue: "Login" })}
            </Button>
          </Stack>
        </Paper>
      </form>
    </Stack>
  );
};

export default SigninContainer;
