import { useTranslation } from "next-i18next";
import { Avatar, Table, Group, Text, ScrollArea } from "@mantine/core";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import Link from "next/link";
import { format } from "date-fns";
import { IPartyLeader } from "@/interfaces/IPartyLeader";

interface PartiesTabProps {
  entityId: string;
  resources: string;
}

export function PartiesTab(props: PartiesTabProps) {
  const { t } = useTranslation();
  const electionsQuery = useQuery(
    ["leadersparties", props.entityId, props.resources],
    async () => {
      const response = await ApiService.resource.getAll(
        `${props.resources}/${props.entityId}/parties`,
        {}
      );
      return response.data as unknown as IPartyLeader[];
    }
  );

  const rows = electionsQuery.data?.map((item) => (
    <Table.Tr key={item.id}>
      <Table.Td>
        <Link className="no-underline" href={`/parties/${item.party.id}`}>
          <Group gap="sm" p={"md"}>
            <Avatar size={"md"} src={item.party.logo} />
            <div>
              <Text fz="sm" fw={500}>
                {item.party.localName}
              </Text>
            </div>
          </Group>
        </Link>
      </Table.Td>
      <Table.Td>
        <Text fz="xs" c="dimmed">
          {item.role}
        </Text>
      </Table.Td>

      <Table.Td>
        <Text fz="xs" c="dimmed">
          {item.party.electionSymbol}
        </Text>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <ScrollArea>
      <Table miw={800} verticalSpacing="sm">
        <Table.Thead>
          <Table.Tr>
            <Table.Th>{t("common:party")}</Table.Th>
            <Table.Th>{t("common:status")}</Table.Th>
            <Table.Th>{t("common:election_symbol")}</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    </ScrollArea>
  );
}
