import GeoDropdown from "@/components/GeoDropdown";
import { Box, Button, Group, Stack } from "@mantine/core";
import { useCallback, useState } from "react";
import { useMutation } from "react-query";
import { ApiService } from "../../../api";
import { useProfile } from "@/store";
import { IUserAddress } from "@/interfaces/IUserAddress";
import { notifications } from "@mantine/notifications";

interface IProps {
  readonly?: boolean;
}
const MyAddressForm = (props: IProps) => {
  const [addressData, setAddressData] = useState<any>(null);
  const profile = useProfile((s) => s.profile);

  const handleAddressChange = (params: any) => {
    setAddressData(params);
  };

  const useProfileMutation = () => {
    return useMutation((newProfile: any) => {
      return ApiService.updateProfile(newProfile);
    });
  };

  const profileMutation = useProfileMutation();

  const handleAddressSubmit = useCallback(
    (e: any) => {
      e.preventDefault();
      const nextAddress = { ...addressData } as IUserAddress;
      if (profile?.user_address) nextAddress.id = profile?.user_address[0]?.id;
      nextAddress.stateId = nextAddress.stateId || "";
      nextAddress.districtId = nextAddress.districtId || "";
      //@ts-expect-error
      nextAddress.municipalId = nextAddress.municipalId || "";
      nextAddress.wardId = nextAddress.wardId || "";

      //@ts-expect-error
      delete nextAddress.partyId;

      profileMutation.mutate(
        { addresses: [nextAddress] },
        {
          onSuccess: () => {
            notifications.show({
              message: "Address submitted successfully",
              color: "green",
            });
            setTimeout(() => {
              window.location.href = "/app/my-representatives";
            }, 500);
          },
          onError: () => {
            notifications.show({ message: "Failed to submit address" });
          },
        }
      );
    },
    [profileMutation, profile]
  );

  return (
    <Stack>
      <form onSubmit={handleAddressSubmit}>
        <Group w={"100%"}>
          <GeoDropdown
            defaultValue={profile?.user_address[0]}
            onChange={handleAddressChange}
            readonly={props.readonly}
          />
          {!props.readonly && <Button type="submit">Submit</Button>}
        </Group>
      </form>
    </Stack>
  );
};
export default MyAddressForm;
