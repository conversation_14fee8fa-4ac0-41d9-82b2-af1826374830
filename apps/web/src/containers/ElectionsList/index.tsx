import { useTranslation } from "next-i18next";
import { Avatar, Badge, Table, Group, Text, ScrollArea } from "@mantine/core";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { IElectionResult } from "@/interfaces/IElections";
import Link from "next/link";
import { format } from "date-fns";

interface ElectionsListProps {
  entityId?: string;
  resourceUrl: string;
}

export function ElectionsList(props: ElectionsListProps) {
  const { t } = useTranslation();
  const electionsQuery = useQuery(
    ["leaderselections", props.entityId, props.resourceUrl],
    async () => {
      const response = await ApiService.resource.getAll(props.resourceUrl, {});
      return response.data as unknown as {
        elections: IElectionResult[];
      };
    }
  );
  //@ts-expect-error
  const rows = electionsQuery.data?.map((item) => (
    <tr key={item.id}>
      <td>
        <Group gap="sm" p={"md"}>
          <Avatar size={"md"} src={"/images/others/ec-nepal.png"} />
          <div>
            <Link
              href={`/elections/${item.electionId}/sub/${item.elCode}/${item.candidacyTypeId}`}
            >
              <Text fz="sm" fw={500}>
                {item.elections?.name}
              </Text>
            </Link>

            <Text fz="xs" c="dimmed">
              {item.candidacyType?.localName}
            </Text>
          </div>
        </Group>
      </td>
      <td>
        <Text fz="xs" c="dimmed">
          {format(new Date(item.elections?.year), "yyyy/M/dd")}
        </Text>
      </td>
      <td>
        <Link
          style={{
            textDecoration: "none",
          }}
          href={`/parties/${item.parties.id}`}
        >
          <Text fz="xs" c="dimmed">
            {item.parties.code} ({item.parties.electionSymbol})
          </Text>
        </Link>
      </td>
      <td>
        <Text fz="xs" c="dimmed">
          {item.districts?.localName} - {item.area}
        </Text>
      </td>
      <td>
        {" "}
        <Text fz="xs" c="dimmed">
          {item.voteCount}
        </Text>
      </td>
    </tr>
  ));

  return (
    <ScrollArea>
      <Table miw={800} verticalSpacing="sm">
        <Table.Thead>
          <Table.Tr>
            <Table.Th>{t("common:election")}</Table.Th>
            <Table.Th>{t("common:date")}</Table.Th>
            <Table.Th>{t("common:party")}</Table.Th>
            <Table.Th>{t("common:district")}</Table.Th>
            <Table.Th>{t("common:votes")}</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    </ScrollArea>
  );
}
