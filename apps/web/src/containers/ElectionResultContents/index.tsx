import { useTranslation } from "next-i18next";
import GeoDropdown from "@/components/GeoDropdown";
import { Avatar, Badge, Chip, Group, Stack, Text } from "@mantine/core";
import RequestPaginatedTable, {
  RequestPaginatedTableProps,
} from "../RequestPaginatedTable";
import Link from "next/link";
import { PaginatedTableProps } from "@/components/PaginatedTable";
import { useCallback, useState } from "react";
import { formatNumber, getAgeFromDate } from "@/utils";
import { VoteCountBox } from "@/components/VoteCountBox";

export interface SimpleUserCardProps {
  image?: string;
  name: string;
  birthDate?: string;
  id: string;
}
const SimpleUserCard = (props: SimpleUserCardProps) => (
  <Group gap="sm" wrap="nowrap">
    <Avatar
      size={"sm"}
      src={props.image || `/images/leaders/${props.id}.jpg`}
    />
    <Text fz="sm" fw={500} className="no-underline" td={"none"}>
      {props.name}
    </Text>
    {props.birthDate && (
      <Badge color="gray">{getAgeFromDate(props.birthDate)}</Badge>
    )}
  </Group>
);

interface IProps {
  electionId: string;
  url: string;
  noFilter?: boolean;
  renderCustomTable?: RequestPaginatedTableProps["renderCustomTable"];
  enableElection?: boolean;
}
const ElectionResultsContents = (props: IProps) => {
  const { t } = useTranslation();
  const [electedType, setElectedType] = useState("all");
  const [geoFilters, handleGeoChanges] = useState({});
  const columns: PaginatedTableProps["columns"] = [
    {
      id: "localName",
      label: "Name",
      renderCell(value, data: any) {
        return (
          <Link
            href={`/leaders/${data.leaders.id}`}
            className="no-underline text-inherit"
          >
            <SimpleUserCard
              image={data?.leaders.img}
              id={data?.leaders.ecCandidateID}
              name={data?.leaders.localName}
            />
          </Link>
        );
      },
    },
    {
      id: "remarks",
      label: "Status",
      renderCell(remarks, d) {
        return remarks ? (
          <Badge
            variant={t("common:gradient")}
            gradient={{
              from: "teal",
              to: "lime",
              deg: 105,
            }}
          >
            {" "}
            Elected
          </Badge>
        ) : (
          <>{t("common:na")}</>
        );
      },
    },
    {
      id: "election",
      label: "election",
      renderCell: (_, d: any) => d?.elections?.localName || d?.elections.name,
    },
    {
      id: "candidacyType",
      label: "candidacy_type",
      renderCell: (_, d: any) => d?.candidacyType?.localName || d?.electionType,
    },
    {
      id: "voteCount",
      label: "votes",
      renderCell(value, data: any) {
        return (
          <Link
            className="text-black"
            href={`/elections/${props.electionId}/sub/${data.elCode}/${data.candidacyTypeId}`}
          >
            <VoteCountBox count={value || 0} variant="filled" />
          </Link>
        );
      },
    },
    {
      id: "party",
      label: "Party",
      renderCell: (_, d: any) => (
        <Link href={`/parties/${d?.parties?.id}`} className="no-underline">
          <SimpleUserCard
            image={d?.parties?.logo}
            id={d?.parties?.id}
            name={d?.parties?.localName}
          />
        </Link>
      ),
    },
    {
      id: "states",
      label: "State",
      renderCell: (_, d: any) => d?.states?.localName,
    },
    {
      id: "districts",
      label: "District",
      renderCell: (_, d: any) => d?.districts?.localName,
    },
    {
      id: "area",
      label: "Area",
    },
    {
      id: "municipal",
      label: "Municipals",
      renderCell: (_, d: any) => {
        return d?.municipals?.id || "N/A";
      },
    },
    {
      id: "ward",
      label: "Ward",
      renderCell: (_, d: any) => d?.ward?.id || "N/A",
    },
  ];
  const handleElectedType = useCallback((v: string) => {
    setElectedType(v);
  }, []);

  return (
    <>
      <Stack align={t("common:start")}>
        <GeoDropdown
          onChange={handleGeoChanges}
          enableParty
          enableElection={props.enableElection}
        />
        {props.noFilter ? null : (
          <Group justify="center">
            <Chip.Group
              onChange={(v) => handleElectedType(v as string)}
              value={electedType}
            >
              <Chip value={"all"}>{t("common:all")}</Chip>
              <Chip value={"elected"}>{t("common:elected")}</Chip>
              <Chip value={"not-elected"}>{t("common:not_elected")}</Chip>
            </Chip.Group>
          </Group>
        )}
      </Stack>
      {/* <Title order={4}>{leaders.data?.totalItems} results</Title> */}
      <RequestPaginatedTable
        renderCustomTable={props.renderCustomTable}
        columns={columns}
        getQueryKey={({ page }) => `parties-leaders-${props.electionId}`}
        url={props.url}
        filters={{ ...geoFilters, electedType }}
      />
    </>
  );
};
export default ElectionResultsContents;
