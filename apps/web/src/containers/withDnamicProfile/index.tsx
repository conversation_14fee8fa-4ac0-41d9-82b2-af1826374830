import DepartmentProfile from "@/components/DepartmentProfile";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";
import PartyProfile from "@/components/PartyProfile";
import ElectionSubProfile from "@/components/ElectionSubProfile";
import GovernmentProfile from "@/components/GovernmentProfile";
import MediaProfile from "@/components/MediaProfile";
import ProjectProfile from "@/components/ProjectProfile";
import ContentProfile from "@/components/ContentProfile";
import ParliamentProfile from "@/components/ParliamentProfile";
import WardProfile from "@/components/WardProfile";
import RatingProfile from "@/components/RatingProfile";
import CabinetMemberProfile from "@/components/CabinerMemberProfile";

const withDynamicProfile = (Component: any) => {
  return (
    props: any & {
      profileType:
        | "Leader"
        | "Party"
        | "Election"
        | "Department"
        | "CabinetMember";
    }
  ) => {
    if (props.profileType === "CabinetMember") {
      return <CabinetMemberProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Department") {
      return <DepartmentProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Leader") {
      return <LeaderHeroProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Party") {
      return <PartyProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Election") {
      return <ElectionSubProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Government") {
      return <GovernmentProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Media") {
      return <MediaProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Project") {
      return <ProjectProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Content") {
      return <ContentProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Parliament") {
      return <ParliamentProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Ward") {
      return <WardProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Municipal") {
      return <GovernmentProfile data={props.data} {...props} />;
    }
    if (props.profileType === "Rating") {
      return <RatingProfile data={props.data} {...props} />;
    }
    return <Component {...props} />;
  };
};

export default withDynamicProfile;
