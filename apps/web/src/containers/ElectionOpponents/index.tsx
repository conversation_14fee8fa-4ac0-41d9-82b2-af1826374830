import { useTranslation } from "next-i18next";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { FeaturedSlider } from "@/components/FeatutredSlider";
import LeaderProfile from "@/components/LeaderProfile";
import { ILeader } from "@/interfaces/ILeader";
import PartyProfile from "@/components/PartyProfile";
import { IParty } from "@/interfaces/IParty";
import { startCase } from "lodash";
import { Badge, Group, Stack } from "@mantine/core";
import { RecommendationBadges } from "../RecommendationBadges";
import { VoteCountBox } from "@/components/VoteCountBox";
import PartyBadge from "@/components/PartyBadge";
import Link from "next/link";
import { IconCheck } from "@tabler/icons-react";
import LeaderHeroProfile from "@/components/LeaderHeroProfile";

const ElectionOpponents = (props: {
  lists?: string[];
  entityType: string;
  entityId: string;
}) => {
  const query = useQuery(
    [
      `leaders/${props.entityId}/elections/opponents`,
      props.entityType,
      props.entityId,
    ],
    async () => {
      const response = await ApiService.resource.getAll(
        `leaders/${props.entityId}/elections/opponents`,
        {
          entityType: props.entityType,
          entityId: props.entityId,
        }
      );
      return response?.data || [];
    }
  );

  const { t } = useTranslation();

  return (
    <>
      <FeaturedSlider
        isLoading={query.isLoading}
        //@ts-expect-error
        data={query.data || []}
        title={t(`common:election_opponents`, {
          defaultValue: "Election Opponents",
        })}
        //@ts-expect-error
        renderItems={({
          leaders,
          voteCount,
          parties,
          isElected,
          elections,
          ...item
        }: {
          elCode: string;
          candidacyTypeId: number;
          leaders: ILeader;
          voteCount: number;
          parties: IParty;
          isElected: boolean;
          elections: any;
        }) => {
          if (props.entityType === "leaders") {
            return (
              <LeaderHeroProfile
                additionalInformationEnabled
                renderBeforeAvatar={() => {
                  return (
                    <Link
                      href={`/elections/${elections.id}/sub/${item.elCode}/${item.candidacyTypeId}`}
                    >
                      <Badge
                        radius={"sm"}
                        variant={t("common:outline")}
                        color={"gray"}
                      >
                        {elections.name}
                      </Badge>{" "}
                    </Link>
                  );
                }}
                data={leaders as ILeader}
                renderDescription={() => (
                  <Stack align="start" gap={5}>
                    <PartyBadge
                      partyId={parties.id}
                      partyImage={parties.logo}
                      partyName={parties.localName}
                    />

                    <Link
                      href={`/elections/${elections.id}/sub/${item.elCode}/${item.candidacyTypeId}`}
                    >
                      <VoteCountBox count={voteCount} />
                    </Link>
                    {isElected ? (
                      <Badge
                        size="xs"
                        color={isElected ? "teal" : "blue"}
                        leftSection={
                          isElected ? (
                            <IconCheck
                              size={12}
                              strokeWidth={5}
                              color={"white"}
                            />
                          ) : null
                        }
                      >
                        {isElected ? "Elected" : "Not Elected"}
                      </Badge>
                    ) : null}
                  </Stack>
                )}
              />
            );
          }

          //   if (props.entityType === {t("common:parties")}) {
          //     //@ts-expect-error
          //     return leaders.gender ? (
          //       <LeaderProfile
          //         leader={leaders as ILeader}
          //         renderDescription={() => (
          //           //@ts-expect-error
          //           <RecommendationBadges reasons={[item.topic]} />
          //         )}
          //       />
          //     ) : (
          //       <PartyProfile party={leaders as IParty} />
          //     );
          //   }

          return null;
        }}
      />
    </>
  );
};
export default ElectionOpponents;
