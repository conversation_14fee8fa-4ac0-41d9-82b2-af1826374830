import SearchBox from "@/components/SearchBox";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import {
  Badge,
  Divider,
  Group,
  LoadingOverlay,
  Stack,
  Tabs,
  Text,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import LeaderTrendChart from "@/components/LeaderTrendChart";
import { SectionTitle } from "@/components/SectionTitle";
import { PartyLineChart } from "@/components/PartyTrendChart";
import { PartyTrendsLineChart } from "@/components/ElectionIndexOverview";
import { IconX } from "@tabler/icons-react";
import { IElectionOverview } from "@/interfaces/IElectionOverview";

interface IProps {
  leaders?: any[];
  parties?: any[];
  disableSearch?: boolean;
  disableChips?: boolean;
}
const ElectionTrendComparer = (props: IProps) => {
  const [leaders, setLeaders] = useState<any[]>(props?.leaders || []);
  const [parties, setParties] = useState<any[]>(props.parties || []);
  const { t } = useTranslation();
  const handleLeaderSelect = (item: any) => {
    if (item.electionSymbol) {
      const isExisting = parties.find((p) => p.id === item.id);
      if (isExisting) return;
      setParties([...parties, item]);
    } else {
      const isExisting = leaders.find((l) => l.id === item.id);
      if (isExisting) return;
      setLeaders([...leaders, item]);
    }
  };

  const leaderQuery = useQuery<IElectionOverview>(
    ["elections", "leaders", leaders],
    async () => {
      const response = await ApiService.resource.getAll<IElectionOverview>(
        `elections/trends`,
        {
          type: "leader",
          id: leaders.map((l) => l.id).join(","),
        }
      );
      return response.data as unknown as IElectionOverview;
    },
    {
      keepPreviousData: true,
      enabled: !!leaders.length,
      retryDelay: 1000,
      retry: 3,
    }
  );
  const partyQuery = useQuery<IElectionOverview>(
    ["elections", "parties", parties],
    async () => {
      const response = await ApiService.resource.getAll<IElectionOverview>(
        `elections/trends`,
        {
          type: "party",
          id: parties.map((p) => p.id).join(","),
        }
      );
      return response.data as unknown as IElectionOverview;
    },
    {
      keepPreviousData: true,
      enabled: !!parties.length,
      retryDelay: 1000,
      retry: 3,
    }
  );

  return (
    <>
      <LoadingOverlay visible={leaderQuery.isLoading || partyQuery.isLoading} />
      <Stack>
        {!props.disableSearch && (
          <Group>
            <SearchBox
              entities={[EntityTypeEnum.Leader, EntityTypeEnum.Party]}
              //@ts-expect-error
              onSelect={handleLeaderSelect}
              inputProps={{
                //@ts-expect-error
                placeholder: t("common:add_leader_or_party_to_compare", {
                  defaultValue: "Add leader or party to compare",
                }),
              }}
            />
          </Group>
        )}
        {!props.disableChips && (
          <Stack gap={10}>
            <Group>
              {parties.length > 0 && <Divider orientation="vertical" />}
              {leaders.length > 0
                ? leaders.map((l) => (
                    <Badge
                      rightSection={<IconX size={12} />}
                      key={l.id}
                      onClick={() =>
                        setLeaders(leaders.filter((i) => l.id !== i.id))
                      }
                    >
                      {l.name}
                    </Badge>
                  ))
                : null}
            </Group>
            <Group>
              {parties.length > 0 && <Divider orientation="vertical" />}
              {parties.length > 0
                ? parties.map((p) => (
                    <Badge
                      leftSection={
                        p.electionSymbol ? (
                          <img
                            src={p.logo}
                            alt={p.name}
                            width={16}
                            height={16}
                          />
                        ) : null
                      }
                      rightSection={<IconX size={12} />}
                      key={p.id}
                      onClick={() =>
                        setParties(parties.filter((i) => p.id !== i.id))
                      }
                    >
                      {p.name}
                    </Badge>
                  ))
                : null}
            </Group>
          </Stack>
        )}
        {leaderQuery.data?.leaderTrends && (
          <>
            <SectionTitle>Leader Trends</SectionTitle>

            {/* @ts-expect-error */}
            <LeaderTrendChart data={leaderQuery.data.leaderTrends || []} />
          </>
        )}

        {partyQuery.data && (
          <>
            <SectionTitle>Party Trends Votes</SectionTitle>
            <Tabs defaultValue="vote">
              <Tabs.List>
                <Tabs.Tab value="vote">Vote Trends in %</Tabs.Tab>
                <Tabs.Tab value="vote1">Vote Trends </Tabs.Tab>
              </Tabs.List>
              <Tabs.Panel value="vote">
                <PartyLineChart
                  data={partyQuery.data.partyTrendsSeries}
                  mode="vote"
                />
              </Tabs.Panel>
              <Tabs.Panel value="vote1">
                <PartyTrendsLineChart data={partyQuery.data.partyTrends} />
              </Tabs.Panel>
            </Tabs>

            <SectionTitle>Party Trends Seats</SectionTitle>
            <Tabs defaultValue="seat">
              <Tabs.List>
                <Tabs.Tab value="seat">Vote Trends in %</Tabs.Tab>
                <Tabs.Tab value="seat1">Vote Trends </Tabs.Tab>
              </Tabs.List>
              <Tabs.Panel value="seat">
                <PartyLineChart
                  data={partyQuery.data.partyTrendsSeries}
                  mode="seat"
                />
              </Tabs.Panel>
              <Tabs.Panel value="seat1">
                <PartyTrendsLineChart
                  data={partyQuery.data.partyTrends}
                  valueKey="seats"
                />
              </Tabs.Panel>
            </Tabs>
          </>
        )}
      </Stack>
    </>
  );
};
export default ElectionTrendComparer;
