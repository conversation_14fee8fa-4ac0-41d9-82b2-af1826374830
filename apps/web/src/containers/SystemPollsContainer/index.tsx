import { useTranslation } from "next-i18next";
import {
  Box,
  Button,
  Card,
  Center,
  Divider,
  Group,
  Loader,
  Paper,
  Stack,
  Text,
  Title,
  Badge,
  Alert,
} from "@mantine/core";
import { useCallback, useMemo, useState } from "react";
import { useQuery, useQueryClient } from "react-query";
import { ApiService } from "../../../api";
import { formatDistanceToNow } from "date-fns";
import { useRouter } from "next/router";
import {
  IconBalloon,
  IconMoodEmpty,
  IconRefresh,
  IconTrendingUp,
} from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { useProfile } from "@/store";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import SystemPoll from "@/components/SystemPoll";
import { ISystemPoll, ResourceType } from "@/interfaces/ISystemPoll";

interface SystemPollsContainerProps {
  entityType: string;
  entityId: string;
  showStatistics?: boolean;
  showCreateButton?: boolean;
}

const SystemPollsContainer = ({
  entityType,
  entityId,
  showStatistics = true,
  showCreateButton = false,
}: SystemPollsContainerProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const profile = useProfile();
  const { openLoginModal } = useLeaderHover();
  const queryClient = useQueryClient();
  const [isCreatingPolls, setIsCreatingPolls] = useState(false);

  const isLoggedIn = !!profile.profile?.firstName;
  const resourceType = entityType.toUpperCase() as ResourceType;

  // Query for system polls
  const pollsQuery = useQuery<ISystemPoll[]>(
    ["SystemPolls", entityType, entityId, isLoggedIn],
    async () => {
      const response = isLoggedIn
        ? await ApiService.getSystemPollsWithAuth(resourceType, entityId, true)
        : await ApiService.getSystemPolls(resourceType, entityId, false);
      return response.data?.data || response.data || [];
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Query for poll statistics
  const statisticsQuery = useQuery(
    ["SystemPollsStatistics", resourceType, entityId],
    async () => {
      const response = await ApiService.getPollStatistics(
        resourceType,
        entityId
      );
      return response.data?.data || response.data;
    },
    {
      enabled: showStatistics,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Query to check if polls exist
  const pollsExistQuery = useQuery(
    ["SystemPollsExist", entityType, entityId],
    async () => {
      const response = await ApiService.checkSystemPollsExist(
        resourceType,
        entityId
      );
      return response.data?.exists || false;
    },
    {
      enabled: showCreateButton,
      staleTime: 5 * 60 * 1000,
    }
  );

  const handleVoteSubmitted = useCallback(
    (updatedPoll: ISystemPoll) => {
      // Update the polls query cache with the new vote data
      queryClient.setQueryData(
        ["SystemPolls", entityType, entityId, isLoggedIn],
        (oldData: ISystemPoll[] | undefined) => {
          if (!oldData) return [];
          return oldData.map((poll) =>
            poll.id === updatedPoll.id ? updatedPoll : poll
          );
        }
      );

      // Invalidate statistics to refresh them
      queryClient.invalidateQueries([
        "SystemPollsStatistics",
        resourceType,
        entityId,
      ]);
    },
    [queryClient, resourceType, entityId, isLoggedIn]
  );

  const handleCreatePolls = async () => {
    if (!isLoggedIn) {
      openLoginModal();
      notifications.show({
        message: t("common:please_login_to_create_polls"),
        color: "red",
      });
      return;
    }

    setIsCreatingPolls(true);
    try {
      await ApiService.forceCreateSystemPolls(resourceType, entityId);

      notifications.show({
        message: t("common:polls_created_successfully"),
        color: "green",
      });

      // Refresh the polls data
      pollsQuery.refetch();
      pollsExistQuery.refetch();
      statisticsQuery.refetch();
    } catch (error: any) {
      console.error("Error creating polls:", error);
      const errorMessage =
        error.response?.data?.message || t("common:error_creating_polls");
      notifications.show({
        message: errorMessage,
        color: "red",
      });
    } finally {
      setIsCreatingPolls(false);
    }
  };

  const handleRefreshPolls = () => {
    pollsQuery.refetch();
    statisticsQuery.refetch();
  };

  const pollsData = pollsQuery.data || [];
  const statisticsData = statisticsQuery.data;

  return (
    <Stack gap="md" w="100%">
      {/* Header */}
      <Card shadow="sm" padding="md" radius="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconBalloon size={24} />
            <Title order={3}>{t("common:system_polls")}</Title>
          </Group>
          <Group>
            <Button
              variant="light"
              size="sm"
              leftSection={<IconRefresh size={16} />}
              onClick={handleRefreshPolls}
              loading={pollsQuery.isFetching}
            >
              {t("common:refresh")}
            </Button>
            {showCreateButton && !pollsExistQuery.data && (
              <Button
                size="sm"
                leftSection={<IconBalloon size={16} />}
                onClick={handleCreatePolls}
                loading={isCreatingPolls}
              >
                {t("common:create_polls")}
              </Button>
            )}
          </Group>
        </Group>
      </Card>

      {/* Statistics */}
      {showStatistics && statisticsData && (
        <Card shadow="sm" padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="sm">
            <Text fw={500}>{t("common:poll_statistics")}</Text>
            <IconTrendingUp size={20} />
          </Group>
          <Group grow>
            <Box ta="center">
              <Text size="xl" fw={700} c="blue">
                {statisticsData.totalPolls}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:total_polls")}
              </Text>
            </Box>
            <Box ta="center">
              <Text size="xl" fw={700} c="green">
                {statisticsData.totalResponses}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:total_responses")}
              </Text>
            </Box>
            <Box ta="center">
              <Text size="xl" fw={700} c="orange">
                {statisticsData.averageResponsesPerPoll}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:avg_responses")}
              </Text>
            </Box>
          </Group>
        </Card>
      )}

      {/* Polls List */}
      <Box w="100%">
        {pollsQuery.isLoading || pollsQuery.isFetching ? (
          <Center w="100%" mih="15rem">
            <Loader />
          </Center>
        ) : pollsData.length > 0 ? (
          <Stack gap="md">
            {pollsData.map((poll) => (
              <SystemPoll
                key={poll.id}
                poll={poll}
                onVoteSubmitted={handleVoteSubmitted}
              />
            ))}
          </Stack>
        ) : (
          <Center w="100%" mih="15rem">
            <Stack align="center" gap="md">
              <IconMoodEmpty size={100} color="#c2c2c2" />
              <Text size="xl" c="#c2c2c2" ta="center">
                {t("common:no_polls_available")}
              </Text>
              {showCreateButton && !pollsExistQuery.data && (
                <Button
                  leftSection={<IconBalloon size={16} />}
                  onClick={handleCreatePolls}
                  loading={isCreatingPolls}
                >
                  {t("common:create_first_polls")}
                </Button>
              )}
            </Stack>
          </Center>
        )}
      </Box>

      {/* Error handling */}
      {pollsQuery.error && (
        <Alert color="red" title={t("common:error")}>
          {t("common:error_loading_polls")}
        </Alert>
      )}
    </Stack>
  );
};

export default SystemPollsContainer;
