import { useTranslation } from "next-i18next";
import {
  Box,
  Button,
  Card,
  Center,
  Group,
  Loader,
  Stack,
  Text,
  Title,
  Alert,
} from "@mantine/core";
import { useCallback, useState, useEffect } from "react";
import { useQuery, useQueryClient } from "react-query";
import { ApiService } from "../../../api";
import {
  IconBalloon,
  IconMoodEmpty,
  IconRefresh,
  IconTrendingUp,
  IconArrowsShuffle,
} from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { useProfile } from "@/store";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import SystemPoll from "@/components/SystemPoll";
import { ISystemPoll, ResourceType } from "@/interfaces/ISystemPoll";

interface SystemPollsContainerProps {
  entityType: string;
  entityId: string;
  showStatistics?: boolean;
  showCreateButton?: boolean;
}

const SystemPollsContainer = ({
  entityType,
  entityId,
  showStatistics = true,
  showCreateButton = false,
}: SystemPollsContainerProps) => {
  const { t } = useTranslation();
  const profile = useProfile();
  const { openLoginModal } = useLeaderHover();
  const queryClient = useQueryClient();
  const [isCreatingPolls, setIsCreatingPolls] = useState(false);
  const [shuffledPolls, setShuffledPolls] = useState<ISystemPoll[]>([]);
  const [isShuffled, setIsShuffled] = useState(false);

  const isLoggedIn = !!profile.profile?.firstName;
  const resourceType = entityType.toUpperCase() as ResourceType;

  // Query for system polls
  const pollsQuery = useQuery<ISystemPoll[]>(
    ["SystemPolls", entityType, entityId, isLoggedIn],
    async () => {
      const response = isLoggedIn
        ? await ApiService.getSystemPollsWithAuth(resourceType, entityId, true)
        : await ApiService.getSystemPolls(resourceType, entityId, false);
      return response.data?.data || response.data || [];
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Query for poll statistics
  const statisticsQuery = useQuery(
    ["SystemPollsStatistics", resourceType, entityId],
    async () => {
      const response = await ApiService.getPollStatistics(
        resourceType,
        entityId
      );
      return response.data?.data || response.data;
    },
    {
      enabled: showStatistics,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Query to check if polls exist
  const pollsExistQuery = useQuery(
    ["SystemPollsExist", entityType, entityId],
    async () => {
      const response = await ApiService.checkSystemPollsExist(
        resourceType,
        entityId
      );
      return response.data?.exists || false;
    },
    {
      enabled: showCreateButton,
      staleTime: 5 * 60 * 1000,
    }
  );

  const handleVoteSubmitted = useCallback(
    (updatedPoll: ISystemPoll) => {
      // Update the polls query cache with the new vote data
      queryClient.setQueryData(
        ["SystemPolls", entityType, entityId, isLoggedIn],
        (oldData: ISystemPoll[] | undefined) => {
          if (!oldData) return [];
          return oldData.map((poll) =>
            poll.id === updatedPoll.id ? updatedPoll : poll
          );
        }
      );

      // Invalidate statistics to refresh them
      queryClient.invalidateQueries([
        "SystemPollsStatistics",
        resourceType,
        entityId,
      ]);
    },
    [queryClient, resourceType, entityId, isLoggedIn]
  );

  const handleCreatePolls = async () => {
    if (!isLoggedIn) {
      openLoginModal();
      notifications.show({
        message: t("common:please_login_to_create_polls"),
        color: "red",
      });
      return;
    }

    setIsCreatingPolls(true);
    try {
      await ApiService.forceCreateSystemPolls(resourceType, entityId);

      notifications.show({
        message: t("common:polls_created_successfully"),
        color: "green",
      });

      // Refresh the polls data
      pollsQuery.refetch();
      pollsExistQuery.refetch();
      statisticsQuery.refetch();
    } catch (error: any) {
      console.error("Error creating polls:", error);
      const errorMessage =
        error.response?.data?.message || t("common:error_creating_polls");
      notifications.show({
        message: errorMessage,
        color: "red",
      });
    } finally {
      setIsCreatingPolls(false);
    }
  };

  const handleRefreshPolls = () => {
    pollsQuery.refetch();
    statisticsQuery.refetch();
  };

  // Shuffle function using Fisher-Yates algorithm
  const shuffleArray = <T,>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const handleShufflePolls = () => {
    if (originalPolls.length === 0) return;

    const shuffled = shuffleArray(originalPolls);
    setShuffledPolls(shuffled);
    setIsShuffled(true);
    notifications.show({
      message: t("common:polls_shuffled") || "Polls shuffled",
      color: "blue",
      icon: <IconArrowsShuffle size={16} />,
    });
  };

  const handleResetPollOrder = () => {
    setIsShuffled(false);
    setShuffledPolls([]);
    notifications.show({
      message: t("common:polls_order_reset") || "Poll order reset",
      color: "green",
      icon: <IconRefresh size={16} />,
    });
  };

  const originalPolls = pollsQuery.data || [];
  const pollsData = isShuffled ? shuffledPolls : originalPolls;
  const statisticsData = statisticsQuery.data;

  // Reset shuffle state when polls data changes
  useEffect(() => {
    if (originalPolls.length > 0) {
      setIsShuffled(false);
      setShuffledPolls([]);
    }
  }, [originalPolls.length]);

  return (
    <Stack gap="md" w="100%">
      {/* Header */}
      <Card shadow="sm" padding="md" radius="md" withBorder>
        <Group justify="space-between" align="center">
          <Group align="center">
            <IconBalloon size={24} />
            <Title order={3}>{t("common:system_polls")}</Title>
          </Group>
          <Group>
            {pollsData.length > 1 && (
              <>
                <Button
                  variant="subtle"
                  size="sm"
                  leftSection={<IconArrowsShuffle size={16} />}
                  onClick={handleShufflePolls}
                  color="blue"
                  title={t("common:shuffle_polls") || "Shuffle polls"}
                >
                  {t("common:shuffle") || "Shuffle"}
                </Button>
                {isShuffled && (
                  <Button
                    variant="subtle"
                    size="sm"
                    leftSection={<IconRefresh size={16} />}
                    onClick={handleResetPollOrder}
                    color="gray"
                    title={t("common:reset_poll_order") || "Reset poll order"}
                  >
                    {t("common:reset") || "Reset"}
                  </Button>
                )}
              </>
            )}
            <Button
              variant="light"
              size="sm"
              leftSection={<IconRefresh size={16} />}
              onClick={handleRefreshPolls}
              loading={pollsQuery.isFetching}
            >
              {t("common:refresh")}
            </Button>
            {showCreateButton && !pollsExistQuery.data && (
              <Button
                size="sm"
                leftSection={<IconBalloon size={16} />}
                onClick={handleCreatePolls}
                loading={isCreatingPolls}
              >
                {t("common:create_polls")}
              </Button>
            )}
          </Group>
        </Group>
      </Card>

      {/* Statistics */}
      {showStatistics && statisticsData && (
        <Card shadow="sm" padding="md" radius="md" withBorder>
          <Group justify="space-between" mb="sm">
            <Text fw={500}>{t("common:poll_statistics")}</Text>
            <IconTrendingUp size={20} />
          </Group>
          <Group grow>
            <Box ta="center">
              <Text size="xl" fw={700} c="blue">
                {statisticsData.totalPolls}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:total_polls")}
              </Text>
            </Box>
            <Box ta="center">
              <Text size="xl" fw={700} c="green">
                {statisticsData.totalResponses}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:total_responses")}
              </Text>
            </Box>
            <Box ta="center">
              <Text size="xl" fw={700} c="orange">
                {statisticsData.averageResponsesPerPoll}
              </Text>
              <Text size="sm" c="dimmed">
                {t("common:avg_responses")}
              </Text>
            </Box>
          </Group>
        </Card>
      )}

      {/* Shuffle Status Indicator */}
      {isShuffled && pollsData.length > 0 && (
        <Alert
          icon={<IconArrowsShuffle size={16} />}
          color="blue"
          variant="light"
        >
          <Group gap="xs" align="center">
            <Text size="sm">
              {t("common:polls_are_shuffled") ||
                "Polls are displayed in random order"}
            </Text>
            <Button
              variant="subtle"
              size="xs"
              leftSection={<IconRefresh size={12} />}
              onClick={handleResetPollOrder}
              color="gray"
            >
              {t("common:restore_order") || "Restore original order"}
            </Button>
          </Group>
        </Alert>
      )}

      {/* Polls List */}
      <Box w="100%">
        {pollsQuery.isLoading || pollsQuery.isFetching ? (
          <Center w="100%" mih="15rem">
            <Loader />
          </Center>
        ) : pollsData.length > 0 ? (
          <Stack gap="md">
            {pollsData.map((poll) => (
              <SystemPoll
                key={poll.id}
                poll={poll}
                onVoteSubmitted={handleVoteSubmitted}
              />
            ))}
          </Stack>
        ) : (
          <Center w="100%" mih="15rem">
            <Stack align="center" gap="md">
              <IconMoodEmpty size={100} color="#c2c2c2" />
              <Text size="xl" c="#c2c2c2" ta="center">
                {t("common:no_polls_available")}
              </Text>
              {showCreateButton && !pollsExistQuery.data && (
                <Button
                  leftSection={<IconBalloon size={16} />}
                  onClick={handleCreatePolls}
                  loading={isCreatingPolls}
                >
                  {t("common:create_first_polls")}
                </Button>
              )}
            </Stack>
          </Center>
        )}
      </Box>

      {/* Error handling */}
      {pollsQuery.error && (
        <Alert color="red" title={t("common:error")}>
          {t("common:error_loading_polls")}
        </Alert>
      )}
    </Stack>
  );
};

export default SystemPollsContainer;
