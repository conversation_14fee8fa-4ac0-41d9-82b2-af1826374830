import { Head, Html, Main, NextScript } from "next/document";
import { ColorSchemeScript, MantineProvider } from "@mantine/core";
import Script from "next/script";

export default function Document() {
  return (
    <Html lang="en">
      {/* @ts-ignore */}
      <Head>
        <ColorSchemeScript defaultColorScheme="auto" />{" "}
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
