import {
  MantineProvider,
  createTheme,
  <PERSON>tine<PERSON><PERSON><PERSON>Tuple,
  But<PERSON>,
} from "@mantine/core";

const myColor: MantineColorsTuple = [
  "#ffebeb",
  "#fbd5d6",
  "#f1aaaa",
  "#e87b7c",
  "#e15455",
  "#dd3b3c",
  "#dc2d2e",
  "#c32022",
  "#af191d",
  "#990d16",
];

export const theme = createTheme({
  fontFamily: "Open Sans, sans-serif",
  primaryColor: "myColor",
  primaryShade: 7,
  colors: {
    dark: [
      '#d5d7e0',
      '#acaebf',
      '#8c8fa3',
      '#666980',
      '#4d4f66',
      '#34354a',
      '#2b2c3d',
      '#1d1e30',
      '#0c0d21',
      '#01010a',
    ],
    myColor,
  },
  components: {
    Button: {
      defaultProps: {},
    },
  },
});
