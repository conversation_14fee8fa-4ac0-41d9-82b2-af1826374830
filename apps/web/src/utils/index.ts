import Storage from "@/core/Storage";
import { IOauthResponse } from "@/interfaces/IOauthResponse";
import jwt_decode from "jwt-decode";
import { AUTH_TOKEN_NAME } from "@/consts";
import { format, parseISO } from "date-fns";
import {
  FaExclamationTriangle, // scandal
  FaAward, // achievements
  FaFlagCheckered, // milestones
  FaBullhorn, // announcements
  FaHandshake, // promises
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaRegFileAlt,
  FaClipboardCheck,
  FaClipboardList,
  FaGavel,
  FaBug,
  FaThumbsUp,
  FaThumbsDown,
  FaCircleNotch,
  FaCalendarPlus,
  FaRegFileArchive,
  FaPen,
} from "react-icons/fa";
import { ApiService } from "../../api";
import { EntityTypeEnum } from "@/enum/EntityTypeEnum";
import {
  ICandidacyType,
  IDistricts,
  IMunicipals,
  IWard,
} from "@/interfaces/IElectionSubResponse";

export const getDecodedCode = (): {
  [key: string]: string | boolean | number;
} => {
  const token = Storage.getInstance().get(AUTH_TOKEN_NAME) as IOauthResponse;
  return jwt_decode(token.accessToken);
};

export function normalizeAndKebabCase(str: string) {
  // Normalize the string by converting it to lowercase, removing special characters, and replacing non-alphanumeric characters with a space
  const normalizedStr = str
    .toLowerCase()
    .replace(/[^\w\s]+/g, "")
    .replace(/[^a-z0-9]+/g, " ");

  // Convert the normalized string to kebab case
  const kebabCaseStr = normalizedStr.trim().replace(/\s+/g, "-");

  return kebabCaseStr;
}

export function reverseKebabCase(str: any) {
  if (!str) return;
  // Split the kebab case string into an array of words
  const words = str.split("-");

  // Capitalize the first letter of each word
  const reversedWords = words.map(
    (word: any) => word.charAt(0).toUpperCase() + word.slice(1)
  );

  // Join the reversed words with spaces
  const reversedStr = reversedWords.join(" ");

  return reversedStr;
}
export const formatDate = (date?: string | Date, formatType?: string) => {
  if (!date) return undefined;
  return format(new Date(date), formatType || "yyyy/MM/dd");
};

export const formatDateAndTime = (date: string | Date) => {
  return format(new Date(date), "yyyy/MM/dd HH:mm");
};

export const getBytes = (planId: string) => {
  if (planId.includes("GB")) return +planId.split("GB")[0] * Math.pow(1024, 3);
  return +planId.split("MB")[0] * Math.pow(1024, 2);
};
export const getEnv = (key: string) => process.env[key];
export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1)?.toLowerCase();
}

type DebounceFunction<T extends (...args: any[]) => any> = (
  func: T,
  delay: number
) => T;

export const debounce: DebounceFunction<(...args: any[]) => any> = (
  func,
  delay
) => {
  let timeoutId: NodeJS.Timeout | undefined;

  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId);

    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
    //@ts-expect-error
  } as T;
};
export const extendExistingQuery = (
  path: string,
  query: string,
  value: string
) => {
  const params = new URLSearchParams(path.slice(path.indexOf("?")));
  params.set(query, value + "");
  return params.toString();
};
export function startCase(input: string): string {
  return input
    .replace(/^[^a-zA-Z]*|[^a-zA-Z]*$/g, "") // Remove non-alphabetic characters from the beginning and end
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space between lowercase and uppercase letters
    .replace(/(\s|-|_)+/g, " ") // Replace spaces, hyphens, and underscores with a single space
    .replace(/\b\w/g, (match) => match.toUpperCase()); // Convert first letter of each word to uppercase
}

// Example usage:
const inputString = "hello_world-fooBar";
const result = startCase(inputString);

export function getAgeFromDate(dateString: string): number {
  const birthDate = new Date(dateString);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
}

// Function to format date in English
export function formatDateToEnglish(dateString: string): string {
  const date = parseISO(dateString);
  return format(date, "MMMM dd, yyyy");
}

// Function to format date in Nepali
export function formatDateToNepali(dateString: string): string {
  const date = parseISO(dateString);
  return format(date, "MMMM dd, yyyy");
}
export const anka = ["०", "१", "२", "३", "४", "५", "६", "७", "८", "९"];
export function replaceNumberWithAnka(angrejiNumber: number) {
  return (
    String(angrejiNumber)
      .split("")
      //@ts-expect-error
      .map((number: number) => anka[number])
      .join("")
  );
}

export function getImageUrlWithFallback(img: string | null, itemId: string) {
  return (
    img || `${process.env.NEXT_PUBLIC_SITE_URL}/images/leaders/${itemId}.jpg`
  );
}
export function formatNumber(num: number) {
  return num.toLocaleString("en-IN");
}
export function truncateString(str: string = "", limit: number = 10): string {
  if (str?.length <= limit) {
    return str; // No need to truncate
  } else {
    return str ? str?.slice(0, limit) + "... " : ""; // Truncate string and append ellipsis
  }
}

// Mapping
export const ContentTypeIconMap = {
  SCANDAL: FaExclamationTriangle,
  CONTROVERSIES: FaGavel,
  ACHIEVEMENTS: FaAward,
  MILESTONES: FaFlagCheckered,
  ANNOUNCEMENTS: FaBullhorn,
  PROMISES: FaHandshake,
  DEFAULT: FaPen,
};
export type ContentTypeIconMapType = keyof typeof ContentTypeIconMap;
export const ContentTypeColors: Record<string, string> = {
  SCANDAL: "red",
  CONTROVERSIES: "orange",
  ACHIEVEMENTS: "green",
  MILESTONES: "blue",
  ANNOUNCEMENTS: "grape",
  PROMISES: "teal",
};
export const ContentStatusColors: Record<string, string> = {
  PUBLISHED: "green",
  DRAFT: "gray",
  PROVED: "blue",
  REJECTED: "red",
  ALLEGATION_PROVED: "blue",
  ALLEGATION_REJECTED: "red",
  COURT_PROVED: "indigo",
  COURT_REJECTED: "pink",
  ONGOING: "orange",
  INCOMING: "yellow",
  COMPLETED: "teal",
  POSITIVE: "green",
  NEGATIVE: "red",
  INCOMPLETE: "gray",
};
export const ContentStatusColorsIcons = {
  PUBLISHED: FaCheckCircle,
  DRAFT: FaRegFileAlt,
  PROVED: FaClipboardCheck,
  REJECTED: FaTimesCircle,
  ALLEGATION_PROVED: FaClipboardCheck,
  ALLEGATION_REJECTED: FaClipboardList,
  COURT_PROVED: FaGavel,
  COURT_REJECTED: FaGavel,
  ONGOING: FaHourglassHalf,
  INCOMING: FaCalendarPlus,
  COMPLETED: FaCheckCircle,
  POSITIVE: FaThumbsUp,
  NEGATIVE: FaThumbsDown,
  INCOMPLETE: FaRegFileArchive,
};
export type ContentStatusColorsIconsType =
  keyof typeof ContentStatusColorsIcons;

export const removeUnnecessaryDashes = (string: string) =>
  string.replaceAll(/-+/g, " ").trim();

export const getPublicSiteUrl = () => `${process.env.NEXT_PUBLIC_SITE_URL}`;
export function extractLinks(text: string) {
  const regex = /(https?:\/\/[^\s]+)/g;
  return text.match(regex) || [];
}

export function handleURLPreviewFetcher(url: string, metadata: any) {
  if (metadata?.[url]) return Promise.resolve(metadata[url]);
  return ApiService.resource
    .getAll(`contents/link-preview?url=${url}`, {})
    .then((res) => res.data);
}
export function saveToCookie(key: string, value: boolean) {
  if (typeof document !== "undefined") {
    document.cookie = `${key}=${value}; path=/;`;
  }
}
export function getFromCookie(key: string): boolean | undefined {
  if (typeof document === "undefined") return undefined;
  const cookies = document.cookie.split("; ").reduce((acc, cookie) => {
    const [cookieKey, cookieValue] = cookie.split("=");
    acc[cookieKey] = decodeURIComponent(cookieValue);
    return acc;
  }, {} as Record<string, string>);
  return cookies[key] === "true"
    ? true
    : cookies[key] === "false"
    ? false
    : undefined;
}

export function getLinkOfResource(resourceType: string, resource: any) {
  let link;
  switch (resourceType) {
    case EntityTypeEnum.Leader:
      return `/leaders/${resource.id}`;
    case EntityTypeEnum.Party:
      return `/parties/${resource.id}`;
    case EntityTypeEnum.Parliament:
      return `/parliaments/${resource.id}`;

    case EntityTypeEnum.Election:
      return `/elections/${resource.id}`;

    case EntityTypeEnum.Government:
      return `/governments/${resource.id}`;

    case EntityTypeEnum.Content:
      return `/contents/${resource.resourceType.toLowerCase()}/${resource.contentType.toLowerCase()}/${
        resource.id
      }`;

    case EntityTypeEnum.Municipal:
      return `/municipals/${resource.id}`;

    case EntityTypeEnum.Ward:
      return `/wards/${resource.id}`;
    case EntityTypeEnum.ElectionSub:
      return `/elections/${resource.elections.id}/sub/${resource.elCode}/${resource.candidacyTypeId}`;
  }
}

export const formatConstituencyAddress = (data: {
  municipal?: IMunicipals;
  district?: IDistricts;
  area?: string;
  ward?: IWard;
  candidacyType?: ICandidacyType;
}) => {
  const candidacy = data?.candidacyType?.localName
    ? data.candidacyType.localName + " - "
    : "";
  const municipal = data.municipal?.localName;
  const district = data.district?.localName || "";
  const area = data?.area || "";
  const ward = data?.ward?.localName ? `- ${data.ward.localName}` : "";
  return `${candidacy} ${municipal || `${district} ${area}`}${ward}`;
};

export function replaceLinksWithSources(text: string): string {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  let counter = 1;

  return text.replace(urlRegex, (url) => {
    const anchor = `<a href="${url}" target="_blank" rel="noopener noreferrer">Source ${counter}</a>`;
    counter++;
    return anchor;
  });
}

export function getHostFromUrl(cmsLink: string | null): string | undefined {
  if (!cmsLink) return undefined;
  try {
    const url = new URL(cmsLink);
    return url.protocol + "//" + url.hostname;
  } catch (error) {
    console.error("Invalid URL:", cmsLink, error);
    return undefined;
  }
}
export const cleanUrl = (url: string = "") =>
  url.replace(/^(https?:\/\/)?(www\.)?/, "");

export function getInitials(name: string = "") {
  const parts = name.trim().split(/\s+/); // handles extra spaces
  if (parts.length === 1) return parts[0][0].toUpperCase();
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
}
