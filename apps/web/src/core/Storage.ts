import { useTranslation } from 'next-i18next';
import { get, set } from "dot-prop-immutable";

type StorageType = "local" | "cookie"
class Storage {
  static INSTANCE: Storage;
  static NAME = "ri-values";
  public values: { [key: string]: string } = {};

  constructor(private storageType?: StorageType) {
    this.loadValues();
  }

  setValues(values: any) {
    this.values = values
  }
  loadValues() {
    if (typeof window === "undefined") return;
    const localValues =
      window.localStorage.getItem(Storage.NAME);
    this.values = localValues ? JSON.parse(localValues) : null;
    if (!this.values && typeof window !== "undefined") {
      window.localStorage.setItem(Storage.NAME, "{}");
      this.values = {};
    }
  }


  get(key: string, defaultValue = null) {
    this.loadValues();
    const data = get(this.values, key, defaultValue)
    return data;
  }

  set(key: string, value: string | Array<string | number | object>, storageType?: StorageType) {
    this.values = set(this.values, key, value);
    const string = JSON.stringify(this.values)
    if (typeof window !== "undefined") {
      window.localStorage.setItem(Storage.NAME, string);
    }

  }

  static getInstance(storageType?: StorageType) {
    if (!Storage.INSTANCE) {
      Storage.INSTANCE = new Storage(storageType || "local");
    }

    return Storage.INSTANCE;
  }
}
export default Storage;
