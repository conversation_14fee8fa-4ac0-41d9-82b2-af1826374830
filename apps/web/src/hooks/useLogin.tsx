import React from "react";
//@ts-ignore
import { useAuth } from "@ri/fe-auth";
import jwt_decode from "jwt-decode";
import Storage from "@/core/Storage";
import { useGlobalState } from "@/store";
import Link from "next/link";
import { Text } from "@mantine/core";
import { AUTH_TOKEN_NAME, ERR_PASSWORD_NOT_MATCHED } from "@/consts";
import { ApiService } from "../../api";

const useLogin = () => {
  const auth = useAuth();
  const [setGlobalMessage, setGlobalLoading, isLoading] = useGlobalState(
    (s) => [s.setMessage, s.toggleLoading, s.isLoading]
  );

  const logout = React.useCallback(() => {
    auth?.endSession();
    Storage.getInstance().set(AUTH_TOKEN_NAME, "");
    setTimeout(() => {
      //Buffer time for state clearance
      window.location.href = "/auth/signin";
    }, 100);
  }, []);

  const login = React.useCallback(
    (payload: { email: string; password: string; turnstileToken: string }) => {
      setGlobalLoading(true);
      ApiService.login({
        username: payload.email,
        password: payload.password,
        turnstileToken: payload.turnstileToken,
      })
        .then(({ data }) => {
          Storage.getInstance().set(AUTH_TOKEN_NAME, data.data);
          setTimeout(() => {
            window.location.href =
              Storage.getInstance().get("returnUrl") || "/";
            setGlobalLoading(false);
          }, 500);
        })
        .catch((err) => {
          setGlobalLoading(false);
          setGlobalMessage(
            <>
              {err?.response?.data?.message || ERR_PASSWORD_NOT_MATCHED}{" "}
              {err?.response?.data?.message === "User is not confirmed." && (
                <Link href={"/account/resend-email"} style={{}}>
                  <Text size={"sm"} color="app-dark">
                    Resend Email
                  </Text>
                </Link>
              )}
            </>
          );
        });
    },
    []
  );

  return {
    isLoading,
    login,
    logout,
  };
};
export default useLogin;
