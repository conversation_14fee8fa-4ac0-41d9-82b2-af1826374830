import { useInterval } from "@mantine/hooks";
import { useEffect, useRef, useState } from "react";

declare global {
  interface Window {
    turnstile: any;
  }
}

export function useTurnstileToken(opened: boolean) {
  const [token, setToken] = useState<string | null>(null);
  const [tokenLoading, setTokenLoading] = useState(false);
  const widgetIdRef = useRef<any>(null);

  const interval = useInterval(() => {
    window.turnstile?.reset(widgetIdRef.current);
    return interval.stop;
  }, 30000);

  useEffect(() => {
    if (!opened) return;
    setTokenLoading(true);

    const renderTurnstile = () => {
      if (!document.getElementById("cf-container")) return;

      widgetIdRef.current = window.turnstile.render("#cf-container", {
        sitekey: process.env.NEXT_PUBLIC_CF_TURNSTILE_SITE_KEY!,
        callback: (token: string) => {
          setToken(token);
          setTokenLoading(false);
        },
      });
    };

    // Wait for Turnstile script to be ready
    const timer = setTimeout(() => {
      try {
        window.turnstile?.ready(() => {
          renderTurnstile();
        });
      } catch (err) {
        console.log(err);
      }
    }, 2000);

    return () => {
      clearTimeout(timer);
      if (widgetIdRef.current !== null) {
        window.turnstile?.remove(widgetIdRef.current);
        widgetIdRef.current = null;
      }
    };
  }, [opened]);

  useEffect(() => {
    if (token) {
      interval.start();
    }
  }, [token]);

  return token;
}
