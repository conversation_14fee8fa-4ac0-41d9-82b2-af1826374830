import { useState, useRef, useCallback, useEffect } from "react";

export function useDelayedHover({
  delay = 700,
  enabled = true,
}: {
  delay?: 700;
  enabled?: boolean;
}) {
  const [isHovering, setIsHovering] = useState(false);
  const [hasHovered, setHasHovered] = useState(false);
  const [canTrigger, setCanTrigger] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const onMouseEnter = useCallback(() => {
    if (!enabled) return;
    setIsHovering(true);
    setHasHovered(true);
    if (!timeoutRef.current) {
      timeoutRef.current = setTimeout(() => {
        setCanTrigger(true);
        timeoutRef.current = null;
      }, delay);
    }
  }, [delay, enabled]);

  const onMouseLeave = useCallback(() => {
    if (!enabled) return;
    setIsHovering(false);
    setHasHovered(true);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setCanTrigger(false);
  }, [enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, []);

  return { isHovering, hasHovered, canTrigger, onMouseEnter, onMouseLeave };
}
