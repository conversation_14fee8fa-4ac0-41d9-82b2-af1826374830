"use client";

import { ILeader } from "@/interfaces/ILeader";
import { getImageUrlWithFallback } from "@/utils";
import { useMemo } from "react";

export function getLeaderImages(leader?: ILeader) {
  const defaultImage =
    leader?.leaders_images?.find?.((item) => item.isDefault)?.url ||
    leader?.img;

  const parsedImages =
    leader?.leaders_images
      ?.filter?.((img) => img.enabled)
      .map((item) => item.url) || [];

  const randomImage = [leader?.img, ...parsedImages][
    Math.floor(Math.random() * parsedImages.length + 1)
  ];

  return {
    defaultImage:
      defaultImage ||
      getImageUrlWithFallback(leader?.img || "", leader?.ecCandidateID || ""),
    randomImage:
      randomImage ||
      getImageUrlWithFallback(leader?.img || "", leader?.ecCandidateID || ""),
  };
}

const useLeaderImages = (leader?: ILeader) => {
  const { defaultImage, randomImage } = useMemo(() => {
    return getLeaderImages(leader);
  }, [leader]);

  return {
    defaultImage,
    randomImage,
  };
};

export default useLeaderImages;
