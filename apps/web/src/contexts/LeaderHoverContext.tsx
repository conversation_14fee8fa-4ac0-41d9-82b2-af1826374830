// context/LeaderHoverContext.tsx
"use client";

import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";

type HoverState = Record<string, { hovered: boolean; timestamp: number }>;

interface LeaderHoverContextType {
  hoveredLeaders: Record<string, boolean>; // exposed without timestamp
  markHovered: (leaderId: string) => void;
  openLoginModal: () => void;
  closeLoginModal: () => void;
}

const STORAGE_KEY = "hoveredLeaders";
const EXPIRY_MS = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

const LeaderHoverContext = createContext<LeaderHoverContextType | undefined>(
  undefined
);

export const LeaderHoverProvider = ({
  children,
  openLoginModal,
  closeLoginModal,
}: {
  children: ReactNode;
  openLoginModal: () => void;
  closeLoginModal: () => void;
}) => {
  const [hoveredLeaders, setHoveredLeaders] = useState<Record<string, boolean>>(
    {}
  );

  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed: HoverState = JSON.parse(stored);
      const now = Date.now();

      const validData: HoverState = {};
      const cleanedData: Record<string, boolean> = {};

      Object.entries(parsed).forEach(([key, { hovered, timestamp }]) => {
        if (hovered && now - timestamp < EXPIRY_MS) {
          validData[key] = { hovered, timestamp };
          cleanedData[key] = true;
        }
      });

      setHoveredLeaders(cleanedData);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(validData));
    }
  }, []);

  const markHovered = (leaderId: string) => {
    const newData: HoverState = {
      ...JSON.parse(localStorage.getItem(STORAGE_KEY) || "{}"),
      [leaderId]: { hovered: true, timestamp: Date.now() },
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newData));
    setHoveredLeaders((prev) => ({ ...prev, [leaderId]: true }));
  };

  return (
    <LeaderHoverContext.Provider
      value={{ hoveredLeaders, markHovered, openLoginModal, closeLoginModal }}
    >
      {children}
    </LeaderHoverContext.Provider>
  );
};

export const useLeaderHover = () => {
  const context = useContext(LeaderHoverContext);
  if (!context)
    throw new Error("useLeaderHover must be used within LeaderHoverProvider");
  return context;
};
