{"click_here_to_update": "Click here to update\n", "leaders": "Leader\n", "parties": "Parties\n", "most_liked_leaders": "Frequently liked Leaders\n", "popular_leaders": "Popular leaders\n", "popular_parties": "Popular Parties\n", "rajniti_report_track_nepal": "Rajniti Report – Track Nepal\n", "log_in_to_continue": "Log in to continue\n", "go_to_home": "Go to Home\n", "partyid": ", partyId +\n", "parliaments": "Parliaments\n", "present": "Present\n", "light": "light\n", "current": "Current\n", "government_opposition": "Government & Opposition\n", "ruling_parties": "Ruling Parties\n", "opponent_parties": "Opponent Parties\n", "data": "data\n", "hello_world_of_leaders_in_tokyo": "hello world of leaders in tokyo\n", "person": "Person\n", "name": "Name\n", "image": "image\n", "former_prime_minister_of_nepal": "Former Prime Minister of Nepal\n", "affiliation": "affiliation\n", "organization": "Organization\n", "nepali_congress": "Nepali Congress\n", "cabinet_ministries": "Cabinet & Ministries\n", "federal": "Federal\n", "federal_government": "Federal Government\n", "provincial": "Provincial\n", "provincial_government": "Provincial Government\n", "local": "local\n", "local_government": "Local Government\n", "cabinet_distributions": "Cabinet Distributions\n", "cabinet_members": "Cabinet Members\n", "department_distributions": "Department Distributions\n", "party": "Party\n", "leader": "Leader\n", "position": "Position\n", "department": "Department\n", "started_at": "Started at\n", "level": "level\n", "elections": "Elections\n", "event": "Event\n", "nepal_general_election_2025": "Nepal General Election 2025\n", "location": "location\n", "place": "Place\n", "nepal": "Nepal\n", "address": "Address\n", "kathmandu_nepal": "Kathmandu, Nepal\n", "organizer": "organizer\n", "election_commission_nepal": "Election Commission Nepal\n", "subelection": "sub-election\n", "hola_world_of_departments_of_wprk": "hola world of departments of wprk\n", "start": "start\n", "government": "Government\n", "parliament": "Parliament\n", "election": "Election\n", "outline": "outline\n", "contents_nepolit_rajniti_report_nepals_ultimate_political_trackerleaderselectionsparties": "Contents | Nepolit | Rajniti Report – Nepal’s Ultimate Political Tracker|Leaders,Elections&Parties\n", "create_an_account": "Create an account\n", "please_agree_to_the_terms_and_conditions": "Please agree to the terms and conditions\n", "welcome_back": "Welcome back!\n", "my_representatives": "My Representatives\n", "my_address": "My Address\n", "permanent": "permanent\n", "average": "average\n", "count": "Count\n", "rates": "rates\n", "total": "total\n", "percent": "Percent\n", "speaker": "Speaker\n", "direct": "Direct\n", "member": "Member\n", "proportional": "Proportional\n", "male": "Male\n", "female": "Female\n", "others": "Others\n", "announcements": "Announcements\n", "promises": "Promises\n", "scandals": "Scandals\n", "scandal": "Scandal\n", "controversies": "Controversies\n", "achievements": "Achievements\n", "milestones": "Milestones\n", "completed": "Completed\n", "transparent": "transparent\n", "track_records": "Track Records\n", "recommendations": "Recommendations\n", "contents": "Contents\n", "trending": "Trending\n", "popular": "Popular\n", "recentlyviewed": "Recently Viewed\n", "reviews": "Reviews\n", "reviewsanalysis": "Reviews analysis\n", "rate_this_leader": "Rate them\n", "hello_world": "hello world\n", "status": "Status\n", "election_symbol": "Election Symbol\n", "joined": "Joined\n", "leadersparties": "leaders-parties\n", "submit": "Submit\n", "date": "Date\n", "district": "District\n", "votes": "Votes\n", "result": "Result\n", "leaderselections": "leaders-elections\n", "na": "N/A\n", "all": "All\n", "elected": "elected\n", "not_elected": "Not Elected\n", "columns": "columns\n", "remarks": "remarks\n", "gradient": "gradient\n", "candidacy_type": "Candidacy Type\n", "state": "State\n", "area": "Area\n", "municipals": "Municipals\n", "ward": "Ward\n", "notelected": "not-elected\n", "seats_distribution": "Seats Distribution\n", "treemap": "treemap\n", "seats": "Seats\n", "most_rated": "Most Rated\n", "most_viewed": "Frequently Viewed\n", "list": "list\n", "this_field_is_required": "This field is required\n", "top_candidates": "Top Candidates\n", "candidates_by_community": "Candidates by community\n", "community": "community\n", "of_votes": "<h1>of Votes</h1>\n", "vote": "vote\n", "green": "green\n", "default": "default\n", "representatives": "Representatives\n", "constituencies": "Constituencies\n", "wins": "Wins\n", "loss": "Loss\n", "total_votes": "Total Votes\n", "not_available": "Not available\n", "my_representives": "My Representives\n", "account_zone": "Account zone\n", "birth_date": "Birth Date\n", "win_margin": "<PERSON>\n", "loss_margin": "Loss Margin\n", "review": "Review\n", "rajnitireport": "rajniti-report\n", "departments": "departments\n", "coalition_parties": "Coalition Parties\n", "ratings": "Ratings\n", "discussions": "Discussions\n", "invovled-event": "{{ entity }} involved in this {{ item}}\n", "write-review": "Write Review\n", "no-reviews-yet": "No reviews here\n", "please_login_to_rate": "Please login to write review\n", "same_election_area": "Same election area\n", "same_party": "Same party\n", "same_area": "Same area\n", "most_liked_parties": "Most Liked parties\n", "recently_added_leaders": "Recently added leaders\n", "you_can_also_see_your_area_s_representatives_make_sure_you_update_your_address_here": "  You can also see your area`s representatives, make sure you update your address here\n", "recently_added_parties": "Recently added parties\n", "centre": "Centre\n", "no_results_found": "No results found\n", "search": "Search\n", "UPPER": "Upper\n", "government_parties_seats": "{{count}} political parties with {{seats}} seats\n", "summary": "Summary\n", "cabinet_members_count": "{{count}} members in cabinet\n", "municipal": "Municipals\n", "state_heads_count": "State heads ({{count}})\n", "cabinet_member_times": "{{name}} has become a Cabinet member or Minister {{count}} time\n", "parliament_member_times": "{{name}} has become a parliament member {{count}} time\n", "election_opponents": "Election Opponents\n", "related_elections": "Releated Elections\n", "nearby_elections": "Nearby elections\n", "most_voted_leaders": "Most voted leaders\n", "most_voted_parties": "Most voted parties\n", "most_win_parties": "Most win parties\n", "read_more": "Read more\n", "PUBLISHED": "PUBLISHED\n", "DRAFT": "DRAFT\n", "PROVED": "PROVED\n", "REJECTED": "REJECTED\n", "ALLEGATION_PROVED": "ALLEGATION_PROVED\n", "ALLEGATION_REJECTED": "ALLEGATION_REJECTED\n", "COURT_PROVED": "COURT_PROVED\n", "COURT_REJECTED": "COURT_REJECTED\n", "ONGOING": "ONGOING\n", "INCOMING": "INCOMING\n", "COMPLETED": "COMPLETED\n", "POSITIVE": "POSITIVE\n", "NEGATIVE": "NEGATIVE\n", "INCOMPLETE": "INCOMPLETE\n", "SCANDAL": "SCANDAL\n", "CONTROVERSIES": "CONTROVERSIES\n", "ACHIEVEMENTS": "ACHIEVEMENTS\n", "MILESTONES": "MILESTONES\n", "ANNOUNCEMENTS": "ANNOUNCEMENTS\n", "PROMISES": "PROMISES\n", "parliament-heads": "Parliament heads\n", "governments": "Governments\n", "recentlyAdded_parliament": "Recently Added Parliaments\n", "most_viewed_parliament": "Frequently Viewed Parliaments\n", "mostLiked_parliament": "Rated Parliament\n", "recentlyAdded_government": "Recently Added Governments\n", "most_viewed_government": "Frequently Viewed Governments\n", "mostLiked_government": "Rated Government\n", "recentlyAdded_leader": "Recently Added Leaders\n", "most_viewed_leader": "Frequently Viewed Leaders\n", "mostLiked_leader": "Rated Leaders\n", "recentlyAdded_party": "Recently Added Parties\n", "most_viewed_party": "Frequently Viewed Parties\n", "mostLiked_party": "Rated Parties\n", "recentlyAdded_department": "Recently Added Departments\n", "most_viewed_department": "Frequently Viewed Departments\n", "mostLiked_department": "Rated Departments\n", "recentlyAdded_municipal": "Recently Added Municipals\n", "most_viewed_municipal": "Frequently Viewed Municipals\n", "mostLiked_municipal": "Rated Municipals\n", "recentlyAdded_ward": "Recently Added Wards\n", "most_viewed_ward": "Frequently Viewed Wards\n", "mostLiked_ward": "Rated Wards\n", "recentlyAdded_election_sub": "Recently Added Sub Election\n", "most_viewed_election_sub": "Frequently Viewed Sub Elections\n", "mostLiked_election_sub": "Top Rated Election Results\n", "recentlyAdded_election": "Recently Added Elections\n", "most_viewed_election": "Frequently Viewed Elections\n", "mostLiked_election": "Rated Elections\n", "recentlyAdded_content": "Recently Added Scandals & Controversies\n", "most_viewed_content": "Frequently Viewed Scandals & Controversies\n", "mostLiked_content": "Rated Scandals & Controversies\n", "recentlyAdded_all": "Recently Added All\n", "most_viewed_all": "Frequently Viewed All\n", "mostLiked_all": "Rated All\n", "my_reviews": "My Reviews\n", "review_message": "{{ user }} reviewed {{ item }} {{ value }}\n", "delete_alert": "Are your sure to delete this item?\n", "review_verification": "Review submitted successfully, it will be visible after verification.\n", "delete_error": "We are not able to delete this item, please try again.\n", "tenures": "Tenures\n", "election_sub": "Election\n", "anniversaryGovernments": "Government Anniversary Celebrations\n", "birthdayPost": "Celebrating {{age}}th birthday\n", "anniversaryParties": "Political Party Anniversary Celebrations\n", "knowYourLocalRepresentatives": "Meet Your Local Representatives\n", "knowYourFederalRepresentatives": "Meet Your Federal Representatives\n", "knowYourProvincialRepresentatives": "Meet Your Provincial Representatives\n", "knowYourLocalWardRepresentatives": "Meet Your Ward Representatives\n", "randomElectionResults": "Explore Random Election Results\n", "birthdayPostMessage": "Celebrating {{age}}th birthday\n", "anniversaryPostMessage": "Marking {{age}} year anniversary\n", "anniversaryParties_socialmediacontent": "Political Party Anniversary Celebrations\n", "birthdayPost_socialmediacontent": "Birthday Greetings and Posts\n", "anniversaryGovernments_socialmediacontent": "Government Anniversary Celebrations\n", "knowYourLocalRepresentatives_socialmediacontent": "Meet Your Local Representatives\n", "knowYourLocalWardRepresentatives_socialmediacontent": "Meet Your Ward Representatives\n", "knowYourFederalRepresentatives_socialmediacontent": "Meet Your Federal Representatives\n", "randomElectionResults_socialmediacontent": "Explore Random Election Results\n", "knowYourProvincialRepresentatives_socialmediacontent": "Meet Your Provincial Representatives\n", "old_leader": "Meet leaders not so young\n", "young_leader": "Meet Young Leaders\n", "recommendations_recommendations": "Recommendations\n", "medias": "Medias\n", "recentlyAdded_recentlyaddedsummary": "Recently Added Summaries\n", "compare": "Compare\n", "news_content": "Recently added contents\n", "projects": "Projects\n", "recentlyAdded_projects": "Recently Added Projects\n", "most_viewed_projects": " Frequently Viewed Projects\n", "mostLiked_projects": "Rated Projects\n", "recentlyAdded_project": "Recently Added Projects\n", "most_viewed_project": " Frequently Viewed Projects\n", "mostLiked_project": "Rated Projects\n", "hot_cabinet_members": "Famous Cabinet Members\n", "cabinet_distribution_by_party": "Cabinet distribution by political party\n", "MINISTER": "Minister\n", "ministers": "Ministers\n", "PRIME_MINSTER": "Prime Minister\n", "DEPUTY_PRIME_MINISTER": "Deputy Prime Minister\n", "minister": "Minister\n", "STATE_MINISTER": "State Minister\n", "end_at": "End Date\n", "see_more": "See More\n", "hotParliamentLeaders_parliament": "Hot Parliament Leaders\n", "hotCabinetMembers_government": "Hot Cabinet Members\n", "login": "Log In\n", "register": "Register\n", "home": "Home\n", "leadership_tenures": "Leadership Tenures\n", "description": "Description\n", "qualification": "Qualifications\n", "experience": "Experiences\n", "fatherName": "Father's Name\n", "spouseName": "Spouse's Name\n", "approval": "Approval\n", "most_common": "Most Common\n", "create_new": "Create New\n", "in": "In\n", "create_new_post": "Create new {{ type }} in\n", "times": "Times\n", "overview": "Overview\n", "top_contents_leaders": "Top Leaders in {{ contents }}\n", "strong_regions": "Strong Regions in {{ election }}\n", "weak_regions": "Weak Regions in {{ election }}\n", "losses": "Losses\n", "votes_received": "Votes Received\n", "win_percent": "Win Percentage\n", "loss_percent": "Loss Percentage\n", "top_performer_area": "Top Performing Area\n", "district_performance": "District Performance\n", "top_district": "Top District\n", "bottom_district": "Bottom District\n", "hot_parliamentarians": "Hot Parliamentarians\n", "bar": "Bar Chart\n", "pie": "Pie Chart\n", "results": "Results\n", "representatives_of": "Representatives of {{entity}}\n", "visit_ward": "Visit Ward {{entity}}\n", "series": "Series\n", "budget": "Budget\n", "learn_more": "Learn More\n", "funding_source": "Funding Source\n", "contractor": "Contractor\n", "ended": "Ended\n", "progress": "Progress\n", "related_to_this_project": "Related to This Project\n", "add_leader_to_compare": "Add Leader to Compare\n", "compare_leaders": "Compare Leaders\n", "recommendations_for": "Recommendations For\n", "add_to_compare": "Add to Compare\n", "born": "Date of Birth\n", "no_positions_recorded": "No Positions Recorded\n", "no_projects_recorded": "No Projects Found\n", "no_controversies_recorded": "No Controversies Recorded\n", "achievements_logged": "Achievements Logged\n", "average_rating": "Average Rating\n", "education_party": "Education & Party\n", "education": "Education\n", "statistics": "Statistics\n", "approval_rating": "Approval Rating\n", "AGE_GROUP_COMPETITOR": "Age Group Competitor\n", "ELECTION_COMPETITOR": "Election Competitor\n", "MOST_VIEWED": "Most Viewed\n", "news_controversies": "News & Controversies\n", "also_previously_served_in": "Also Previously Served In\n", "prime_minister": "Prime Minister\n", "deputy_prime_minister": "Deputy Prime Minister\n", "state_minister": "State Minister\n", "most_repeated_ministers": "Most Repeated Ministers\n", "dont_have_account": "Don't have an account yet?\n", "create_account": "Create Account\n", "or_continue_with_email": "Or continue with email\n", "email": "Email\n", "password": "Password\n", "last_name": "Last Name\n", "first_name": "First Name\n", "already_have_account": "Already have an account?\n", "continue_with_x": "Continue with {{X}}\n", "continue_with_google": "Continue with Google\n", "by_signing_up_you_agree_to_our_terms_and_conditions": "By signing up, you agree to our terms and conditions\n", "repeatedLeaders_department": "Repeated Leaders by Department\n", "recentlyAdded_rating": "Recently Added Ratings\n", "SPEAKER": "Speaker\n", "VICE_SPEAKER": "Vice Speaker\n", "members": "Members\n", "PROPORTIONAL": "Proportional\n", "DIRECT": "Direct\n", "polls": "Polls", "all_polls": "All Polls", "polls_page_description": "Explore all polls and share your opinions on political matters", "polls_page_subtitle": "Discover and participate in polls about leaders, parties, and political issues", "filters": "Filters", "search_polls": "Search polls...", "vote_status": "Vote Status", "voted": "Voted", "not_voted": "Not Voted", "sort_by": "Sort By", "most_responses": "Most Responses", "newest_first": "Newest First", "descending": "Descending", "ascending": "Ascending", "refresh": "Refresh", "responses": "Responses", "no_polls_found": "No polls found", "try_adjusting_filters": "Try adjusting your filters to find more polls", "end_of_results": "You have reached the end of results", "error": "Error", "failed_to_load_polls": "Failed to load polls. Please try again.", "submit_vote": "Submit <PERSON><PERSON>", "submitting": "Submitting...", "you_have_voted": "You have already voted on this poll", "please_login_to_vote": "Please login to vote", "vote_submitted_successfully": "Your vote has been submitted successfully", "error_submitting_vote": "Error submitting your vote. Please try again.", "shuffle": "Shuffle", "reset": "Reset", "shuffle_options": "Shuffle options", "reset_options": "Reset options", "options_shuffled": "Options have been shuffled", "options_reset": "Options have been reset", "options_shuffled_indicator": "Options are shuffled"}