export const ESIM_API_PATH = process.env.NEXT_PUBLIC_API_HOST;

export const ERR_GENERIC_REQUIRED = "This field is required";
export const ERR_GENERIC_REQUIRED_NP = "This field is required";
export const REGEX_STRONG_PASSWORD = /^(?=.*[A-Z]).{8,}$/;
export const ERR_MIN_PASSWORD_LENGTH =
  "Password must contain at least 8 character(s)";
export const ERR_STRONG_PASSWORD =
  "Password must be a minimum of 8 characters long and contain at least one uppercase letter";
export const AUTH_TOKEN_NAME = "gesim";
export const ERR_PASSWORD_NOT_MATCHED =
  "Username and password combination didnt match.";
export const google_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Google`;
export const fb_url = `${ESIM_API_PATH}/auth/social/login/links?platform=Facebook`;
