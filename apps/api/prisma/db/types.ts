import type { ColumnType } from 'kysely';
export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

import type {
  ElectionResultType,
  GenderType,
  ParliamentMemberType,
  ParliamentMemberElectionType,
  CommentableType,
  CandidacyTypeEnum,
  ElectionType,
  PartyRole,
  GOVERNMENT_TYPE,
  CABINET_ROLE,
  ParliamentHouseType,
  CABINET_EVENT_TYPE,
} from './enums';

export type cabinet_events = {
  id: Generated<number>;
  cabinetMemberId: number;
  title: string;
  description: string;
  type: CABINET_EVENT_TYPE;
  date: Timestamp;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type cabinet_members = {
  id: Generated<number>;
  governmentId: number;
  partyId: number | null;
  leaderId: number;
  departmentId: number;
  role: CABINET_ROLE | null;
  rank: number | null;
  startedAt: Timestamp | null;
  endAt: Timestamp | null;
  isResigned: Generated<number>;
  isActing: Generated<number>;
  portfolioTitle: string | null;
  remarks: string | null;
  appointedBy: string | null;
  appointmentMethod: string | null;
  officialLink: string | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type candidacy_types = {
  id: Generated<number>;
  name: CandidacyTypeEnum | null;
  localName: string | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type comments = {
  id: Generated<number>;
  content: string;
  date: Generated<Timestamp>;
  commentOnId: number;
  commentOnType: CommentableType;
};
export type contents = {
  code: string;
  id: Generated<number>;
  resourceType: string;
  contentType: string;
  contentStatus: string;
  resourceId: number;
  content: string | null;
  isDeleted: Generated<number>;
  title: string;
  cmsLink: string | null;
  slug: string | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  metadata: unknown | null;
  eventDate: Timestamp | null;
  eventDueDate: Timestamp | null;
  eventEndDate: Timestamp | null;
  parentContentId: number | null;
};
export type departments = {
  id: Generated<number>;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  name: string;
  code: string;
  description: string;
};
export type districts = {
  id: Generated<number>;
  localName: string;
  name: string;
  code: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  stateId: number;
};
export type election_centres = {
  id: Generated<number>;
  localName: string;
  name: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  wardId: number;
};
export type election_results = {
  id: Generated<number>;
  electionId: number;
  leaderId: number;
  voteCount: number;
  area: string | null;
  districtId: number;
  isElected: number | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  candidacyTypeId: number | null;
  wardId: number | null;
  partyId: number | null;
  code: string;
  remarks: string | null;
  municipalId: number | null;
  stateId: number | null;
  elCode: string | null;
};
export type elections = {
  id: Generated<number>;
  name: string;
  year: Timestamp;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  electionType: ElectionType;
};
export type GovernmentElectionRelation = {
  A: number;
  B: number;
};
export type governments = {
  id: Generated<number>;
  name: string;
  description: string | null;
  img: string | null;
  startedAt: Timestamp;
  endAt: Timestamp | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  headId: number | null;
  government_type: GOVERNMENT_TYPE | null;
  municipalId: number | null;
  stateId: number | null;
};
export type leaders = {
  id: Generated<number>;
  name: string;
  localName: string | null;
  description: string | null;
  img: string | null;
  address: string | null;
  contact: string | null;
  startedAt: Timestamp | null;
  endAt: Timestamp | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  gender: GenderType;
  birthDate: Timestamp | null;
  deathDate: Timestamp | null;
  emailAddress: string | null;
  metadata: unknown | null;
  qualification: string | null;
  experience: string | null;
  otherDetails: string | null;
  nameOfInst: string | null;
  ecCandidateID: string;
};
export type most_likes = {
  id: Generated<number>;
  resourceId: number;
  resourceType: string;
  createdAt: Generated<Timestamp>;
  userId: number;
};
export type most_viewed = {
  id: Generated<number>;
  resourceId: number;
  resourceType: string;
  views: number;
  createdAt: Generated<Timestamp>;
  ipAddress: string | null;
  browser: string | null;
  userId: number | null;
};
export type municipals = {
  id: Generated<number>;
  localName: string;
  name: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  districtId: number;
  stateId: number;
};
export type parliament_members = {
  id: Generated<number>;
  memberId: number;
  parliamentId: number;
  memberType: ParliamentMemberType;
  electionType: ParliamentMemberElectionType;
  partyId: number | null;
};
export type parliaments = {
  id: Generated<number>;
  name: string | null;
  description: string | null;
  startDate: Timestamp | null;
  endDate: Timestamp | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  stateId: number | null;
  houseType: ParliamentHouseType | null;
  electionId: number | null;
};
export type parties = {
  id: Generated<number>;
  name: string;
  description: string | null;
  startDate: Timestamp;
  code: string;
  motto: string | null;
  endDate: Timestamp | null;
  logo: string | null;
  coverImage: string | null;
  localName: string | null;
  partyColorCode: string | null;
  electionSymbol: string | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type party_leaders = {
  id: Generated<number>;
  leaderId: number;
  partyId: number;
  startDate: Timestamp;
  endDate: Timestamp | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  position: string;
  role: PartyRole | null;
  code: string;
};
export type projects = {
  id: Generated<number>;
  name: string;
  description: string | null;
};
export type ratings = {
  id: Generated<number>;
  value: number;
  rateOnId: string;
  rateOnType: CommentableType;
  comment: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  userId: number;
};
export type scandals = {
  id: Generated<number>;
  name: string;
  description: string | null;
};
export type states = {
  id: Generated<number>;
  localName: string;
  name: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type user_address = {
  id: Generated<number>;
  userId: number;
  municipalityId: number | null;
  districtId: number | null;
  wardId: number | null;
  stateId: number | null;
  electionCenterId: number | null;
  address_type: Generated<string>;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type users = {
  id: Generated<number>;
  firstName: string;
  lastName: string;
  isVerified: number;
  password: string;
  email: string;
  stripeId: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  profileImage: string | null;
  defaultPaymentMethodId: string | null;
  verifyCode: string | null;
  address: unknown | null;
};
export type wards = {
  id: Generated<number>;
  localName: string;
  name: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  municipalId: number | null;
  districtId: number | null;
  stateId: number | null;
  code: string | null;
};
export type DB = {
  _GovernmentElectionRelation: GovernmentElectionRelation;
  cabinet_events: cabinet_events;
  cabinet_members: cabinet_members;
  candidacy_types: candidacy_types;
  comments: comments;
  contents: contents;
  departments: departments;
  districts: districts;
  election_centres: election_centres;
  election_results: election_results;
  elections: elections;
  governments: governments;
  leaders: leaders;
  most_likes: most_likes;
  most_viewed: most_viewed;
  municipals: municipals;
  parliament_members: parliament_members;
  parliaments: parliaments;
  parties: parties;
  party_leaders: party_leaders;
  projects: projects;
  ratings: ratings;
  scandals: scandals;
  states: states;
  user_address: user_address;
  users: users;
  wards: wards;
};
