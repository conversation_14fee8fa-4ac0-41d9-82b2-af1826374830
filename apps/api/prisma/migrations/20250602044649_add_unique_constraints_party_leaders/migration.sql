/*
  Warnings:

  - A unique constraint covering the columns `[leaderId,partyId,startDate]` on the table `party_leaders` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[hash]` on the table `ratings` will be added. If there are existing duplicate values, this will fail.
  - Made the column `hash` on table `ratings` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `ratings` MODIFY `hash` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `party_leaders_leaderId_partyId_startDate_key` ON `party_leaders`(`leaderId`, `partyId`, `startDate`);

-- CreateIndex
CREATE UNIQUE INDEX `ratings_hash_key` ON `ratings`(`hash`);
