/*
  Warnings:

  - Added the required column `status` to the `projects` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `projects` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `projects` ADD COLUMN `beneficiaries` INTEGER NULL,
    ADD COLUMN `budget` DOUBLE NULL,
    ADD COLUMN `challenges` VARCHAR(191) NULL,
    ADD COLUMN `contractor` VARCHAR(191) NULL,
    ADD COLUMN `currency` VARCHAR(191) NULL,
    ADD COLUMN `expectedOutcome` VARCHAR(191) NULL,
    ADD COLUMN `fundingSource` VARCHAR(191) NULL,
    ADD COLUMN `lastInspectedAt` DATETIME(3) NULL,
    ADD COLUMN `location` VARCHAR(191) NULL,
    ADD COLUMN `mediaGallery` JSON NULL,
    ADD COLUMN `milestones` JSON NULL,
    ADD COLUMN `plannedEndDate` DATETIME(3) NULL,
    ADD COLUMN `plannedStartDate` DATETIME(3) NULL,
    ADD COLUMN `sector` ENUM('ENERGY', 'TRANSPORT', 'WATER', 'IRRIGATION', 'URBAN_DEVELOPMENT', 'HEALTH', 'EDUCATION', 'AGRICULTURE', 'TELECOMMUNICATION') NULL,
    ADD COLUMN `status` ENUM('PLANNED', 'ONGOING', 'COMPLETED', 'CANCELLED') NOT NULL,
    ADD COLUMN `type` ENUM('MEGA', 'INFRASTRUCTURE', 'SMALL') NOT NULL;
