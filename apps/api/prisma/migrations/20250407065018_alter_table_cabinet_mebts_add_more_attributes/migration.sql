-- AlterTable
ALTER TABLE `cabinet_members` ADD COLUMN `appointedBy` VARCHAR(191) NULL,
    ADD COLUMN `appointmentMethod` VARCHAR(191) NULL,
    ADD COLUMN `isActing` <PERSON><PERSON>OLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isResigned` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    ADD COLUMN `officialLink` VARCHAR(191) NULL,
    ADD COLUMN `portfolioTitle` VARCHAR(191) NULL,
    ADD COLUMN `rank` INTEGER NULL,
    ADD COLUMN `remarks` MEDIUMTEXT NULL,
    ADD COLUMN `role` ENUM('PRIME_MINISTER', 'DEPUTY_PRIME_MINISTER', 'MINISTER', 'STATE_MINISTER') NULL;

-- CreateTable
CREATE TABLE `cabinet_events` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cabinetMemberId` INTEGER NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `type` ENUM('ACHIEVEMENT', 'CONTROVERSY', 'POLICY', 'RESIGNATION', 'REPLACEMENT') NOT NULL,
    `date` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `cabinet_events_cabinetMemberId_idx`(`cabinetMemberId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- RenameIndex
ALTER TABLE `cabinet_members` RENAME INDEX `government_leaders_departmentId_fkey` TO `cabinet_members_departmentId_idx`;

-- RenameIndex
ALTER TABLE `cabinet_members` RENAME INDEX `government_leaders_governmentId_fkey` TO `cabinet_members_governmentId_idx`;

-- RenameIndex
ALTER TABLE `cabinet_members` RENAME INDEX `government_leaders_leaderId_fkey` TO `cabinet_members_leaderId_idx`;
