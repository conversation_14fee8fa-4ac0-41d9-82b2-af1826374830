-- CreateTable
CREATE TABLE `polls` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `resourceId` INTEGER NOT NULL,
    `resourceType` ENUM('PARLIAMENT', 'RATING', 'GOVERNMENT', 'LEADER', 'PARTY', 'DEPARTMENT', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'WARD', 'ELECTION_SUB', 'ELECTION', 'CONTENT', 'ALL', 'MEDI<PERSON>', 'PROJECT') NOT NULL,
    `question` VARCHAR(191) NOT NULL,
    `type` ENUM('RADIO', 'CHECKBOX') NOT NULL,
    `createdBy` ENUM('USER', 'SYSTEM') NOT NULL,
    `createdByUser` INTEGER NULL,
    `deadline` DATETIME(3) NULL,
    `hash` VARCHAR(191) NOT NULL,
    `isDeleted` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `polls_hash_key`(`hash`),
    INDEX `polls_resourceId_resourceType_idx`(`resourceId`, `resourceType`),
    INDEX `polls_createdByUser_idx`(`createdByUser`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `poll_options` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `pollId` INTEGER NOT NULL,
    `text` VARCHAR(191) NOT NULL,
    `resourceId` INTEGER NULL,
    `resourceType` ENUM('PARLIAMENT', 'RATING', 'GOVERNMENT', 'LEADER', 'PARTY', 'DEPARTMENT', 'MUNICIPAL', 'WARD', 'ELECTION_SUB', 'ELECTION', 'CONTENT', 'ALL', 'MEDIA', 'PROJECT') NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `poll_options_resourceId_resourceType_idx`(`resourceId`, `resourceType`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `poll_responses` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `pollId` INTEGER NOT NULL,
    `optionId` INTEGER NOT NULL,
    `userId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `poll_responses_pollId_idx`(`pollId`),
    INDEX `poll_responses_optionId_idx`(`optionId`),
    INDEX `poll_responses_userId_idx`(`userId`),
    UNIQUE INDEX `poll_responses_pollId_userId_optionId_key`(`pollId`, `userId`, `optionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
