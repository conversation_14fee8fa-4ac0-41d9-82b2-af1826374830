-- AlterTable
ALTER TABLE `comments` MODIFY `commentOnType` ENUM('Government', 'Leader', 'Party', 'Department', 'ELECTION', 'ELECTION_SUB', 'PARLIAMENT', 'CONTENT', 'PROJECT', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'WARD', '<PERSON><PERSON><PERSON>') NOT NULL;

-- AlterTable
ALTER TABLE `contents` MODIFY `resourceType` ENUM('PARLIAMENT', 'RATING', 'GOVERNMENT', 'LEADER', 'PARTY', 'DEPARTMENT', 'MUNICIPAL', 'WARD', 'ELECTION_SUB', 'ELECTION', 'CONTENT', 'ALL', 'MEDIA') NOT NULL;

-- AlterTable
ALTER TABLE `ratings` MODIFY `rateOnType` ENUM('Government', 'Leader', 'Party', 'Department', 'ELECTION', 'ELECTION_SUB', 'PARLIAMENT', 'CONTENT', 'PROJECT', 'MUNICIPAL', 'WARD', 'MEDIA') NOT NULL;

-- CreateTable
CREATE TABLE `medias` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `website` VARCHAR(191) NULL,
    `image` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `startDate` DATETIME(3) NULL,
    `endDate` DATETIME(3) NULL,
    `localName` VARCHAR(191) NULL,
    `years` INTEGER NULL,
    `owner` VARCHAR(191) NULL,
    `metadata` JSON NULL,
    `fbPage` VARCHAR(191) NULL,
    `twitterPage` VARCHAR(191) NULL,
    `instagramPage` VARCHAR(191) NULL,
    `youtubePage` VARCHAR(191) NULL,
    `contact` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `logo` VARCHAR(191) NULL,
    `coverImage` VARCHAR(191) NULL,
    `address` VARCHAR(191) NULL,
    `mediaType` ENUM('NEWSPAPER', 'RADIO', 'TELEVISION', 'ONLINE', 'PRINT', 'MAGAZINE', 'BLOG', 'SOCIAL_MEDIA', 'OTHER') NOT NULL,
    `parentMediaId` INTEGER NULL,

    INDEX `medias_parentMediaId_idx`(`parentMediaId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `categories` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `enabled` BOOLEAN NOT NULL DEFAULT true,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
