-- CreateTable
CREATE TABLE `compare` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resourceType` ENUM('PARLIAMENT', 'RATING', 'GOVERNMENT', 'LEADER', 'PARTY', 'DEPARTMENT', 'MUNICIPAL', 'WARD', 'ELECTION_SUB', 'ELECTION', 'CONTENT', 'ALL', 'MEDIA') NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `userId` INTEGER NULL,
    `metadata` JSON NULL,
    `createdCount` INTEGER NOT NULL DEFAULT 0,
    `viewedCount` INTEGER NOT NULL DEFAULT 0,

    UNIQUE INDEX `compare_code_key`(`code`),
    PRIMAR<PERSON> (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
