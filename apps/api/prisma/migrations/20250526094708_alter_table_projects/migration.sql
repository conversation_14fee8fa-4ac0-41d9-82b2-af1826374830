/*
  Warnings:

  - Added the required column `startDate` to the `projects` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `leaders` ADD COLUMN `projectsId` INTEGER NULL;

-- AlterTable
ALTER TABLE `projects` ADD COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    ADD COLUMN `endDate` DATETIME(3) NULL,
    ADD COLUMN `startDate` DATETIME(3) NOT NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);

-- CreateTable
CREATE TABLE `projects_by` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `projectId` INTEGER NOT NULL,
    `leaderId` INTEGER NULL,
    `partyId` INTEGER NULL,
    `image` VARCHAR(191) NULL,
    `link` VARCHAR(191) NULL,
    `governmentId` INTEGER NULL,
    `progress` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `projects_by_governmentId_idx`(`governmentId`),
    INDEX `projects_by_projectId_idx`(`projectId`),
    INDEX `projects_by_leaderId_idx`(`leaderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
