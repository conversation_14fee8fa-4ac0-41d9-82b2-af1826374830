/*
  Warnings:

  - You are about to alter the column `resourceType` on the `contents` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(13))`.
  - You are about to alter the column `contentType` on the `contents` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(14))`.
  - You are about to alter the column `contentStatus` on the `contents` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Enum(EnumId(15))`.

*/
-- AlterTable
ALTER TABLE `contents` MODIFY `resourceType` ENUM('PARLIAMENT', 'RATING', 'GOVERNMENT', 'LEADER', 'PARTY', 'DEPARTMENT', 'MUNICIPAL', 'WARD', 'ELECTION_SUB', 'ELECTION', 'CONTENT', 'ALL') NOT NULL,
    MODIFY `contentType` ENUM('SCANDAL', 'NEWS', 'EVENTS', 'CONTROVERSIES', 'ACHIEVEMENTS', 'MILESTONES', 'ANNOUNCEMENTS', 'PROMISES', 'MISC', 'DEFAULT', 'SUMMARY', 'SUMMARY_NP') NOT NULL,
    MODIFY `contentStatus` ENUM('PUBLISHED', 'ALLEGATION', 'DRAFT', 'PROVED', 'REJECTED', 'ALLEGATION_PROVED', 'ALLEGATION_REJECTED', 'COURT_PROVED', 'COURT_REJECTED', 'ONGOING', 'INCOMING', 'COMPLETED', 'POSITIVE', 'NEGATIVE', 'INCOMPLETE', 'NONE') NOT NULL,
    MODIFY `title` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `parliament_government` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `startedAt` DATETIME(3) NOT NULL,
    `endAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `governmentsId` INTEGER NOT NULL,
    `parliamentsId` INTEGER NOT NULL,
    `partiesId` INTEGER NOT NULL,
    `leadersId` INTEGER NOT NULL,
    `type` ENUM('VOTE_OF_CONFIDENCE', 'INVOLVEMENT', 'SUPPORT_ONLY') NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
