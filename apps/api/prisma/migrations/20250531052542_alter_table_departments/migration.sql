/*
  Warnings:

  - A unique constraint covering the columns `[code,name]` on the table `departments` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `departments_code_key` ON `departments`;

-- AlterTable
ALTER TABLE `departments` ADD COLUMN `coverImage` VARCHAR(191) NULL,
    ADD COLUMN `fbPage` VARCHAR(191) NULL,
    ADD COLUMN `localName` VARCHAR(191) NULL,
    ADD COLUMN `logo` VARCHAR(191) NULL,
    ADD COLUMN `metadata` JSON NULL,
    ADD COLUMN `twitterPage` VARCHAR(191) NULL,
    ADD COLUMN `website` VARCHAR(191) NULL,
    ADD COLUMN `youtubePage` VARCHAR(191) NULL;

-- CreateIndex
CREATE UNIQUE INDEX `departments_code_name_key` ON `departments`(`code`, `name`);
