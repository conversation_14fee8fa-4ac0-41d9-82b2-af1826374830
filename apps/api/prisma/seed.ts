import xlsx from 'node-xlsx';
import * as path from 'path';

const partiesSource = xlsx.parse(
  path.join(__dirname, '../data/parties.xlsx'),
  {},
);

import { PrismaClient } from '@prisma/client';
import { seedPAElection } from './seeder/pa-seeder';
import { seedPRElection } from './seeder/hor-seedr';
import { seedPRElection2074 } from './seeder/hor-seedr-2074';
import { seedPRElectionX } from './seeder/hor-seedr-1999';
// import { seedLAElection } from './seeder/le-seedr';
import { seedPRProportional } from './seeder/pr-proportional';

const prisma = new PrismaClient();
// function seedParties(parties) {
//   parties.forEach(async (row) => {
//     const [localName, name, motto, code, description, startDate] = row;
//     await prisma.party.upsert({
//       where: {
//         code,
//       },
//       create: {
//         localName,
//         name,
//         startDate: startDate ? new Date(startDate + '') : new Date(),
//         code,
//         description,
//         motto,
//       },
//       update: {
//         localName,
//         name,
//         startDate: startDate ? new Date(startDate + '') : new Date(),
//         code,
//         description,
//         motto,
//       },
//     });
//   });
// }
// function seedGovernmentDepartments(data) {
//   data.forEach(async (row) => {
//     const [name, code, description] = row;
//     await prisma.departments.upsert({
//       where: {
//         code,
//       },
//       create: {
//         name,
//         code,
//         description,
//       },
//       update: {
//         name,
//         code,
//         description,
//       },
//     });
//   });
// }

async function main() {
  // await seedPRElection2074();
  await seedPRElectionX();
}

main()
  .then(async () => {})
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
