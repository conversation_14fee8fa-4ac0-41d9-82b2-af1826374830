generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "fullTextIndex"]
}

// generator kysely {
//   provider = "prisma-kysely"

//   // Optionally provide a destination directory for the generated file
//   // and a filename of your choice
//   output       = "./db"
//   fileName     = "types.ts"
//   // Optionally generate runtime enums to a separate file
//   enumFileName = "enums.ts"
// }

// generator typegraphql {
//   provider = "typegraphql-prisma"
// }

// generator nestgraphql {
//   provider            = "prisma-nestjs-graphql"
//   // for yarn monorepos
//   // provider = "prisma-nestjs-graphql"
//   output              = "../src/@generated"
//   decorate_wards_name = "import name"
// }

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

enum ElectionResultType {
  ELECTED
}

enum GenderType {
  MALE
  FEMALE
  OTHERS
}

enum ParliamentMemberType {
  SPEAKER
  VICE_SPEAKER
  MEMBER
}

enum ParliamentMemberElectionType {
  DIRECT
  PROPORTIONAL
}

enum CommentableType {
  Government
  Leader
  Party
  ELECTION
  ELECTION_SUB
  PARLIAMENT
  CONTENT
  PROJECT
  MUNICIPAL
  WARD
  MEDIA
  COMPARE
  DEPARTMENT
}

enum CandidacyTypeEnum {
  PROVINCIAL_LAW_MAKER
  LAW_MAKER
  MAYOR
  DEPUTY_MAYOR
  WARD_CHAIRPERSON
  CHAIRPERSON
  DEPUTY_CHAIRPERSON
  WARD_MEMBER_FEMALE
  WARD_MEMBER_DALIT
  WARD_MEMBER
}

enum ElectionType {
  LOCAL_ELECTION
  GENERAL_PROVINCIAL_ELECTION
  GENERAL_PARLIAMENT_ELECTION
  GENERAL_PARLIAMENT_ELECTION_SUB
  BY_ELECTION
}

enum PartyRole {
  PRESIDENT
  GENERAL_SECRETARY
  SPOKESPERSON
  CENTRAL_COMMITTEE_MEMBER
  MEMBER_OF_PARLIAMENT
  LOCAL_PARTY_LEADER
  YOUTH_WING_LEADER
  WOMEN_WING_LEADER
  STUDENT_WING_LEADER
  CONSTITUENCY_LEADER
  WARD_LEADER
  MEMBER
}

enum ParliamentPartyGovermentRelationType {
  VOTE_OF_CONFIDENCE // only gave VOC
  INVOLVEMENT // has ministers in government
  SUPPORT_ONLY // Only supporting
}

enum CABINET_EVENT_TYPE {
  ACHIEVEMENT
  CONTROVERSY
  POLICY
  RESIGNATION
  REPLACEMENT
}

enum CONTENT_TYPE {
  SCANDAL
  NEWS
  EVENTS
  CONTROVERSIES
  ACHIEVEMENTS
  MILESTONES
  ANNOUNCEMENTS
  PROMISES
  MISC
  DEFAULT
  SUMMARY
  SUMMARY_NP
}

enum CONTENT_STATUS {
  PUBLISHED
  ALLEGATION
  DRAFT
  PROVED
  REJECTED
  ALLEGATION_PROVED
  ALLEGATION_REJECTED
  COURT_PROVED
  COURT_REJECTED
  ONGOING
  INCOMING
  COMPLETED
  POSITIVE
  NEGATIVE
  INCOMPLETE
  NONE
}

enum RESOURCE_TYPE {
  PARLIAMENT
  RATING
  GOVERNMENT
  LEADER
  PARTY
  DEPARTMENT
  MUNICIPAL
  WARD
  ELECTION_SUB
  ELECTION
  CONTENT
  ALL
  MEDIA
  PROJECT
}

model parliament_government {
  id            Int                                  @id @default(autoincrement())
  startedAt     DateTime
  endAt         DateTime?
  createdAt     DateTime                             @default(now())
  updatedAt     DateTime                             @default(now()) @updatedAt
  government    governments                          @relation(fields: [governmentsId], references: [id])
  parliament    parliaments                          @relation(fields: [parliamentsId], references: [id])
  party         parties?                             @relation(fields: [partiesId], references: [id])
  leader        leaders?                             @relation(fields: [leadersId], references: [id])
  governmentsId Int
  parliamentsId Int
  partiesId     Int
  leadersId     Int
  type          ParliamentPartyGovermentRelationType
}

enum CABINET_ROLE {
  PRIME_MINISTER
  DEPUTY_PRIME_MINISTER
  MINISTER
  STATE_MINISTER // aka Rajyamantri
  SECRETARY
}

model users {
  id                     Int              @id @default(autoincrement())
  firstName              String
  lastName               String
  isVerified             Boolean
  password               String
  email                  String           @unique
  stripeId               String
  createdAt              DateTime         @default(now())
  updatedAt              DateTime         @default(now()) @updatedAt
  profileImage           String?
  defaultPaymentMethodId String?
  verifyCode             String?
  ratings                ratings[]
  most_viewed            most_viewed[]
  mostLiked              most_likes[]
  address                Json?
  user_address           user_address[]
  role                   Role?            @default(User)
  compare                compare[]
  polls                  polls[]
  poll_responses         poll_responses[]
}

enum GOVERNMENT_TYPE {
  MONARCHY
  FEDERAL
  PROVINCIAL
  LOCAL
}

enum Role {
  User
  Admin
  Editor
  Leader
  PartyLeader
}

model governments {
  id                    Int                     @id @default(autoincrement())
  name                  String
  description           String?
  img                   String?
  startedAt             DateTime
  endAt                 DateTime?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  headId                Int?
  head                  leaders?                @relation(fields: [headId], references: [id])
  government_type       GOVERNMENT_TYPE?
  municipalId           Int?
  municipals            municipals?             @relation(fields: [municipalId], references: [id])
  stateId               Int?
  states                states?                 @relation(fields: [stateId], references: [id])
  elections             elections[]             @relation("GovernmentElectionRelation")
  cabinet_members       cabinet_members[]
  parliamentId          Int?
  parliament            parliaments?            @relation(fields: [parliamentId], references: [id])
  localName             String?
  parliament_government parliament_government[]
  projects_by           projects_by[]
  website               String?
  twitterPage           String?
  fbPage                String?
  youtubePage           String?
  wasDismissed          Boolean                 @default(false)
  remarks               String?

  @@index([government_type], map: "governments_government_type_idx")
  @@index([parliamentId], map: "governments_parliamentId_idx")
  @@index([municipalId], map: "governments_municipalId_idx")
  @@index([stateId], map: "governments_stateId_idx")
  @@index([headId], map: "governments_headId_fkey")
}

model elections {
  id               Int                @id @default(autoincrement())
  name             String             @unique
  year             DateTime
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  electionType     ElectionType
  election_results election_results[]
  parliaments      parliaments[]
  localName        String?            @default("")
  parentElectionId Int?
  parentElection   elections?         @relation(name: "ParentElection", fields: [parentElectionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  childElections   elections[]        @relation(name: "ParentElection") // Use the same relation name for children

  governments governments[] @relation("GovernmentElectionRelation")

  @@index([parentElectionId])
}

model cabinet_members {
  id                Int           @id @default(autoincrement())
  governmentId      Int
  img               String?
  partyId           Int?
  party             parties?      @relation(fields: [partyId], references: [id])
  leaderId          Int?
  departmentId      Int?
  role              CABINET_ROLE?
  rank              Int? // Lower is more senior (e.g., 1 = PM)
  startedAt         DateTime?
  endAt             DateTime?
  isResigned        Boolean       @default(false)
  isActing          Boolean       @default(false)
  portfolioTitle    String? // Custom/official title, e.g. “Minister for Culture, Tourism and Civil Aviation”
  remarks           String?       @db.MediumText
  appointedBy       String?
  appointmentMethod String? // "Elected", "Nominated", "Coalition Agreement", etc.
  officialLink      String? // Gov source or press release
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @default(now()) @updatedAt
  leaderName        String? // this is when we cant set relation
  partyName         String? // this is when we cant set relation

  // Relations
  department  departments? @relation(fields: [departmentId], references: [id])
  governments governments  @relation(fields: [governmentId], references: [id])
  leaders     leaders?     @relation(fields: [leaderId], references: [id])

  cabinet_events cabinet_events[] // One-to-many with events like achievements or controversies

  @@index([governmentId])
  @@index([leaderId])
  @@index([departmentId])
}

model departments {
  id              Int               @id @default(autoincrement())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @default(now()) @updatedAt
  name            String
  code            String
  description     String            @db.MediumText
  localName       String?
  logo            String?
  coverImage      String?
  website         String?
  fbPage          String?
  twitterPage     String?
  youtubePage     String?
  metadata        Json?
  rank            Int?
  cabinet_members cabinet_members[]

  @@unique([code, name])
}

model leaders {
  id                    Int                     @id @default(autoincrement())
  name                  String
  localName             String?
  description           String?                 @db.Text()
  img                   String?
  address               String?
  contact               String?
  startedAt             DateTime?
  endAt                 DateTime?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  gender                GenderType
  birthDate             DateTime?
  deathDate             DateTime?
  emailAddress          String?
  metadata              Json?
  qualification         String?
  experience            String?                 @db.Text()
  otherDetails          String?                 @db.Text()
  nameOfInst            String?
  ecCandidateID         String                  @unique
  governments           governments[]
  party_leaders         party_leaders[]
  parliament_members    parliament_members[]
  election_results      election_results[]
  cabinet_members       cabinet_members[]
  parliament_government parliament_government[]
  images                Json?
  leaders_images        leaders_images[]
  projects              projects?               @relation(fields: [projectsId], references: [id])
  projectsId            Int?
  projects_by           projects_by[]
  fbPage                String?
  twitterPage           String?
  instagramPage         String?
  youtubePage           String?
  website               String?
}

model parties {
  id                    Int                     @id @default(autoincrement())
  name                  String
  description           String?
  startDate             DateTime
  code                  String                  @unique
  motto                 String?
  endDate               DateTime?
  logo                  String?
  coverImage            String?
  localName             String?
  partyColorCode        String?
  electionSymbol        String?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  party_leaders         party_leaders[]
  election_results      election_results[]
  parliament_members    parliament_members[]
  cabinet_members       cabinet_members[]
  parliament_government parliament_government[]
  projects_by           projects_by[]
  fbPage                String?
  twitterPage           String?
  instagramPage         String?
  youtubePage           String?
  website               String?
}

model party_leaders {
  id        Int        @id @default(autoincrement())
  leaderId  Int?
  partyId   Int?
  startDate DateTime
  endDate   DateTime?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @default(now()) @updatedAt
  position  String
  leader    leaders?   @relation(fields: [leaderId], references: [id])
  party     parties?   @relation(fields: [partyId], references: [id])
  role      PartyRole?
  code      String     @unique

  @@unique([leaderId, partyId])
  @@index([leaderId], map: "party_leaders_leaderId_idx")
  @@index([partyId])
}

model projects {
  id               Int           @id @default(autoincrement())
  name             String
  localName        String?
  description      String?
  type             ProjectType // Mega, Infrastructure, Small
  status           ProjectStatus // Ongoing, Completed, Planned, Cancelled
  sector           Sector?
  address          String?
  startDate        DateTime
  endDate          DateTime?
  plannedStartDate DateTime?
  plannedEndDate   DateTime?
  lastInspectedAt  DateTime?
  budget           Float?
  currency         String? // e.g., "NPR", "USD"
  beneficiaries    Int?
  contractor       String?
  fundingSource    String?
  expectedOutcome  String?
  challenges       String?
  milestones       Json?
  image            String?
  mediaGallery     Json? // Array of image/video URLs
  progress         Int? // 0–100
  link             String?
  leaders          leaders[] // Existing relation
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @default(now()) @updatedAt
  projects_by      projects_by[]
}

enum ProjectType {
  MEGA
  INFRASTRUCTURE
  SMALL
}

enum ProjectStatus {
  PLANNED
  ONGOING
  COMPLETED
  CANCELLED
}

enum Sector {
  ENERGY
  TRANSPORT
  WATER
  IRRIGATION
  URBAN_DEVELOPMENT
  HEALTH
  EDUCATION
  AGRICULTURE
  TELECOMMUNICATION
  OTHER
  CULTURAL_HISTORICAL
}

model projects_by {
  id           Int          @id @default(autoincrement())
  projectId    Int
  leaderId     Int?
  partyId      Int?
  governmentId Int?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @default(now()) @updatedAt
  leaders      leaders?     @relation(fields: [leaderId], references: [id])
  party        parties?     @relation(fields: [partyId], references: [id])
  governments  governments? @relation(fields: [governmentId], references: [id])
  projects     projects?    @relation(fields: [projectId], references: [id])

  @@index([governmentId])
  @@index([projectId])
  @@index([leaderId])
}

model scandals {
  id          Int     @id @default(autoincrement())
  name        String
  description String?
}

model comments {
  id            Int             @id @default(autoincrement())
  content       String
  date          DateTime        @default(now())
  commentOnId   Int
  commentOnType CommentableType
}

model ratings {
  id         Int             @id @default(autoincrement())
  value      Int             @db.TinyInt()
  rateOnId   String
  rateOnType CommentableType
  comment    String          @db.MediumText()
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @default(now()) @updatedAt
  userId     Int
  user       users           @relation(fields: userId, references: id)
  hash       String          @unique

  @@index([userId])
}

model parliaments {
  id                    Int                     @id @default(autoincrement())
  name                  String?
  description           String?                 @db.MediumText()
  startDate             DateTime?
  endDate               DateTime?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @default(now()) @updatedAt
  parliament_members    parliament_members[]
  stateId               Int?
  states                states?                 @relation(fields: [stateId], references: [id])
  houseType             ParliamentHouseType?
  electionId            Int?
  elections             elections?              @relation(fields: [electionId], references: [id])
  governments           governments[]
  localName             String?
  parliament_government parliament_government[]
  website               String?
  twitterPage           String?
  fbPage                String?
  youtubePage           String?

  @@index([stateId])
  @@index([electionId])
}

enum ParliamentHouseType {
  UPPER
  LOWER
}

model parliament_members {
  id           Int                          @id @default(autoincrement())
  memberId     Int?
  member       leaders?                     @relation(fields: [memberId], references: [id])
  parliamentId Int
  parliament   parliaments                  @relation(fields: parliamentId, references: id)
  memberType   ParliamentMemberType
  electionType ParliamentMemberElectionType
  partyId      Int?
  party        parties?                     @relation(fields: [partyId], references: [id])
  leaderName   String? // this is when we cant set relation
  partyName    String? // this is when we cant set relation

  @@index([memberId])
  @@index([parliamentId])
  @@index([partyId])
}

model districts {
  id               Int                @id @default(autoincrement())
  localName        String             @unique
  name             String
  code             String             @unique
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  municipal        municipals[]
  stateId          Int
  states           states             @relation(references: [id], fields: [stateId])
  election_results election_results[]
  wards            wards[]
  user_address     user_address[]

  @@index([stateId])
}

model states {
  id               Int                @id @default(autoincrement())
  localName        String             @unique
  name             String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  districts        districts[]
  municipals       municipals[]
  wards            wards[]
  election_results election_results[]
  user_address     user_address[]
  parliaments      parliaments[]
  // Duplicate field removed
  governments      governments[]
}

model municipals {
  id               Int                @id @default(autoincrement())
  localName        String             @unique
  name             String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  districtId       Int
  districts        districts          @relation(fields: [districtId], references: id)
  stateId          Int
  states           states             @relation(fields: [stateId], references: id)
  wards            wards[]
  election_results election_results[]
  user_address     user_address[]
  // Duplicate field removed
  governments      governments[]

  @@index([districtId])
  @@index([stateId])
}

model wards {
  id               Int                @id @default(autoincrement())
  localName        String
  name             String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  municipalId      Int?
  municipals       municipals?        @relation(fields: [municipalId], references: id)
  election_centres election_centres[]
  election_results election_results[]
  districtId       Int?
  districts        districts?         @relation(fields: [districtId], references: id)
  stateId          Int?
  states           states?            @relation(fields: [stateId], references: id)
  code             String?            @unique
  user_address     user_address[]

  @@index([municipalId])
  @@index([districtId])
  @@index([stateId])
}

model election_centres {
  id           Int            @id @default(autoincrement())
  localName    String
  name         String
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @default(now()) @updatedAt
  wardId       Int
  wards        wards          @relation(fields: [wardId], references: id)
  user_address user_address[]

  @@index([wardId])
}

model candidacy_types {
  id               Int                @id @default(autoincrement())
  name             CandidacyTypeEnum? @unique
  localName        String?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @default(now()) @updatedAt
  election_results election_results[]
}

model election_results {
  id              Int              @id @default(autoincrement())
  electionId      Int
  elections       elections        @relation(fields: [electionId], references: [id])
  leaderId        Int
  leaders         leaders          @relation(fields: [leaderId], references: [id])
  voteCount       Int
  area            String?
  districtId      Int
  districts       districts        @relation(fields: [districtId], references: [id])
  isElected       Boolean?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now()) @updatedAt
  candidacyType   candidacy_types? @relation(fields: [candidacyTypeId], references: [id])
  candidacyTypeId Int?
  wardId          Int?
  ward            wards?           @relation(references: [id], fields: [wardId])
  partyId         Int?
  parties         parties?         @relation(fields: [partyId], references: [id])
  code            String           @unique
  remarks         String?
  municipalId     Int?
  municipals      municipals?      @relation(fields: [municipalId], references: id)
  stateId         Int?
  states          states?          @relation(fields: [stateId], references: id)
  elCode          String?

  @@index([electionId])
  @@index([leaderId])
  @@index([districtId])
  @@index([candidacyTypeId])
  @@index([wardId])
  @@index([partyId])
  @@index([municipalId])
  @@index([stateId])
}

model most_viewed {
  id           Int      @id @default(autoincrement())
  resourceId   Int // Foreign key referencing the resource being viewed
  resourceType String // Foreign key referencing the resource being viewed
  views        Int // Number of views for the resource
  createdAt    DateTime @default(now())
  ipAddress    String? // IP address of the user who viewed the resource
  browser      String? // Browser details of the user who viewed the resource

  // Define the relation to the resource model

  // Define the relation to the user model
  userId Int? // Foreign key referencing the user who viewed the resource
  user   users? @relation(fields: [userId], references: [id])

  @@index([userId])
}

model most_likes {
  id           Int      @id @default(autoincrement())
  resourceId   Int // Foreign key referencing the resource being viewed
  resourceType String // Foreign key referencing the resource being viewed
  createdAt    DateTime @default(now())

  userId Int // Foreign key referencing the user who viewed the resource
  user   users @relation(fields: [userId], references: [id])

  @@index([userId])
}

model user_address {
  id               Int               @id @default(autoincrement())
  userId           Int
  user             users             @relation(fields: [userId], references: [id])
  municipalityId   Int?
  districtId       Int?
  wardId           Int?
  stateId          Int?
  electionCenterId Int?
  address_type     String            @default("PERMANENT")
  municipality     municipals?       @relation(fields: [municipalityId], references: [id])
  district         districts?        @relation(fields: [districtId], references: [id])
  ward             wards?            @relation(fields: [wardId], references: [id])
  state            states?           @relation(fields: [stateId], references: [id])
  electionCenter   election_centres? @relation(fields: [electionCenterId], references: [id])
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @default(now()) @updatedAt

  @@index([municipalityId])
  @@index([wardId])
  @@index([districtId])
  @@index([userId])
  @@index([stateId])
  @@index([electionCenterId])
}

model cabinet_events {
  id              Int                @id @default(autoincrement())
  cabinetMemberId Int
  title           String
  description     String
  type            CABINET_EVENT_TYPE
  date            DateTime
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @default(now()) @updatedAt

  cabinet_member cabinet_members @relation(fields: [cabinetMemberId], references: [id])

  @@index([cabinetMemberId])
}

model contents {
  id              Int            @id @default(autoincrement())
  resourceType    RESOURCE_TYPE
  contentType     CONTENT_TYPE
  contentStatus   CONTENT_STATUS
  resourceId      Int
  content         String?        @db.LongText
  isDeleted       Boolean        @default(false)
  title           String?
  cmsLink         String?
  slug            String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @default(now()) @updatedAt
  metadata        Json?
  eventDate       DateTime?
  eventDueDate    DateTime?
  eventEndDate    DateTime?
  parentContentId Int?
  parentContent   contents?      @relation(name: "ParentContent", fields: [parentContentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  childContents   contents[]     @relation(name: "ParentContent") // Use the same relation name for children
  summary         String?        @db.LongText
  code            String         @unique

  @@index([parentContentId])
  @@fulltext([content])
  @@fulltext([content, title])
}

model kvStore {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

enum MEDIA_TYPE {
  NEWSPAPER
  RADIO
  TELEVISION
  ONLINE
  PRINT
  MAGAZINE
  BLOG
  SOCIAL_MEDIA
  OTHER
}

model medias {
  id            Int        @id @default(autoincrement())
  name          String
  description   String?
  website       String?
  image         String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @default(now()) @updatedAt
  startDate     DateTime?
  endDate       DateTime?
  localName     String?
  years         Int?
  owner         String?
  metadata      Json?
  fbPage        String?
  twitterPage   String?
  instagramPage String?
  youtubePage   String?
  contact       String?
  email         String?
  logo          String?
  coverImage    String?
  address       String?
  mediaType     MEDIA_TYPE
  feedUrl       String?

  parentMediaId Int?
  parentMedia   medias?  @relation(name: "ParentMedia", fields: [parentMediaId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  childMedias   medias[] @relation(name: "ParentMedia") // Use the same relation name for children

  @@index([parentMediaId])
}

model categories {
  id        Int      @id @default(autoincrement())
  name      String
  code      String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  enabled   Boolean  @default(true)
}

model leaders_images {
  id      Int     @id @default(autoincrement())
  url     String  @db.MediumText()
  leaders leaders @relation(fields: [leadersId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  enabled   Boolean  @default(true)
  isDefault Boolean  @default(false)
  leadersId Int

  @@index([leadersId])
}

model compare {
  id           Int           @id @default(autoincrement())
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @default(now()) @updatedAt
  resourceType RESOURCE_TYPE
  code         String        @unique
  userId       Int?
  user         users?        @relation(fields: [userId], references: [id])
  metadata     Json?
  createdCount Int           @default(0)
  viewedCount  Int           @default(0)
}

enum POLL_TYPE {
  RADIO // Single-select
  CHECKBOX // Multi-select
}

enum POLL_CREATED_BY {
  USER
  SYSTEM
}

model polls {
  id               Int             @id @default(autoincrement())
  resourceId       Int
  resourceType     RESOURCE_TYPE
  question         String
  questionLocal    String?
  title            String
  titleLocal       String?
  description      String?
  descriptionLocal String?
  image            String?
  type             POLL_TYPE
  createdBy        POLL_CREATED_BY
  createdByUser    Int? // If created by user
  deadline         DateTime?
  hash             String          @unique
  isDeleted        Boolean         @default(false)
  code             String          @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  options   poll_options[]
  responses poll_responses[]

  user             users?             @relation(fields: [createdByUser], references: [id])
  poll_vote_counts poll_vote_counts[]

  @@index([resourceId, resourceType])
  @@index([createdByUser])
}

model poll_options {
  id        Int     @id @default(autoincrement())
  pollId    Int
  text      String
  textLocal String?
  value     String?
  img       String?

  // Optional link to any real resource (like Leader, Party, etc.)
  resourceId   Int?
  resourceType RESOURCE_TYPE?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  poll             polls              @relation(fields: [pollId], references: [id])
  responses        poll_responses[]
  poll_vote_counts poll_vote_counts[]

  @@index([resourceId, resourceType])
}

model poll_responses {
  id       Int  @id @default(autoincrement())
  pollId   Int
  optionId Int
  userId   Int?

  createdAt DateTime @default(now())

  poll   polls        @relation(fields: [pollId], references: [id])
  option poll_options @relation(fields: [optionId], references: [id])
  user   users?       @relation(fields: [userId], references: [id])

  // Prevent multiple responses for RADIO polls
  @@unique([pollId, userId, optionId])
  @@index([pollId])
  @@index([optionId])
  @@index([userId])
}

model poll_vote_counts {
  pollId     Int
  optionId   Int
  count      Int          @default(0)
  poll       polls        @relation(fields: [pollId], references: [id])
  option     poll_options @relation(fields: [optionId], references: [id])
  percentage Float?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@id([pollId, optionId])
  @@index([optionId])
}
