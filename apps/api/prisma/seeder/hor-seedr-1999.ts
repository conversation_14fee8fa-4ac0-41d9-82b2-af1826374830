import {
  CandidacyTypeEnum,
  ElectionType,
  PartyRole,
  PrismaClient,
} from '@prisma/client';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as transliterate from 'transliteration';
import * as datefns from 'date-fns';

export const makeAbbr = (str) =>
  str
    .split(' ')
    .map((i) => i[0])
    .join('');

const prisma = new PrismaClient();

export async function seedPRElectionX() {
  const year = 2013;
  const election = await prisma.elections.upsert({
    create: {
      electionType: ElectionType.GENERAL_PARLIAMENT_ELECTION,
      name: `${year} Nepal General Election`,
      year: new Date('2013-03-04'),
      //   governmentId: 1,
    },
    update: {
      electionType: ElectionType.GENERAL_PARLIAMENT_ELECTION,
      name: `${year} Nepal General Election`,
    },
    where: {
      name: `${year} Nepal General Election`,
    },
  });
  const folderPath = path.join(__dirname, `../../data/scripts/${year}-hor`);
  const files = await fs.readdir(folderPath);

  for (const file of files) {
    const filePath = path.join(folderPath, file);
    const stats = await fs.stat(filePath);
    const str = filePath;
    const pattern = /(\d+)\.json$/;
    const match = str.match(pattern);

    let areaCode = '';
    if (match && match[1]) {
      areaCode = match[1].slice(-1);
    }
    if (!stats.isFile()) continue;
    const rawResult = await fs.readFile(filePath, 'utf-8');
    const results = JSON.parse(rawResult);
    for (const result of results) {
      try {
        let {
          CandidateName,
          Gender,
          Age,
          PartyID,
          SymbolID,
          SymbolName,
          CandidateID,
          StateName,
          PoliticalPartyName,
          ElectionPost,
          DistrictCd,
          DistrictName,
          States,
          SCConstID,
          CenterConstID,
          SerialNo,
          TotalVoteReceived,
          CastedVote,
          TotalVoters,
          Rank,
          Remarks,
          Samudaya,
          DOB,
          CTZDIST,
          FATHER_NAME,
          SPOUCE_NAME,
          QUALIFICATION,
          EXPERIENCE,
          OTHERDETAILS,
          NAMEOFINST,
          ADDRESS,
          candidateNameNP,
          districtNameNP,
          politicalPartyNameNP,
        } = result;
        areaCode = SCConstID + '';
        if (!CandidateID) {
          CandidateID = `${candidateNameNP}-${districtNameNP}`;
        }
        const districtModel = await prisma.districts.findFirst({
          where: {
            OR: [
              {
                localName: districtNameNP,
              },
              {
                name: DistrictName,
              },
            ],
          },
          include: {
            states: true,
          },
        });
        if (!districtModel || !districtModel.states) {
          console.log('District not found', districtNameNP, DistrictName);
          continue;
        }
        if (!districtModel.states) {
          console.log('State not found', districtNameNP, DistrictName);
          continue;
        }

        const stateModel = districtModel.states;

        let uniquePartyName = politicalPartyNameNP || 'स्वतन्त्र';
        if (politicalPartyNameNP?.trim() === 'स्वतन्त्र') {
          uniquePartyName = `${politicalPartyNameNP} ${
            SymbolName || ''
          }`?.trim();
        }
        let partyModel = await prisma.parties.upsert({
          create: {
            name: PoliticalPartyName || 'स्वतन्त्र',
            localName: politicalPartyNameNP || 'स्वतन्त्र',
            code: uniquePartyName,
            startDate: new Date(),
            electionSymbol: SymbolName,
          },
          update: {},
          where: {
            code: uniquePartyName,
          },
        });

        const existingLeader = await prisma.leaders.findFirst({
          where: {
            OR: [
              {
                localName: candidateNameNP,
              },
              {
                name: CandidateName,
              },
            ],
            // localName: candidateNameNP,
          },
        });
        if (existingLeader) {
          CandidateID = existingLeader.ecCandidateID;
        }
        const leader = await prisma.leaders.upsert({
          create: {
            name: CandidateName,
            gender: Gender === 'महिला' || Gender === 'F' ? 'FEMALE' : 'MALE',
            ecCandidateID: CandidateID + '',
            birthDate: Age
              ? datefns.subYears(new Date(`${year}-01-01`), Age)
              : null,
            experience: EXPERIENCE,
            nameOfInst: NAMEOFINST,
            otherDetails: OTHERDETAILS,
            metadata: {
              fatherName: FATHER_NAME,
              spouseName: SPOUCE_NAME,
            },
            address: ADDRESS,
            contact: '',
            description: OTHERDETAILS,
            localName: candidateNameNP,
          },
          update: {
            birthDate: Age
              ? datefns.subYears(new Date(`${year}-01-01`), Age)
              : null,
            name: CandidateName,
            ecCandidateID: CandidateID + '',
            experience: EXPERIENCE,
            nameOfInst: NAMEOFINST,
            otherDetails: OTHERDETAILS,
            metadata: {
              fatherName: FATHER_NAME,
              spouseName: SPOUCE_NAME,
              Samudaya,
              DOB,
            },
            address: ADDRESS,
            contact: '',
            description: OTHERDETAILS,
            localName: candidateNameNP,
            qualification: QUALIFICATION,
          },
          where: {
            ecCandidateID: CandidateID + '',
          },
        });

        const candicyTypeModel = await prisma.candidacy_types.upsert({
          create: {
            name: CandidacyTypeEnum.LAW_MAKER,
            localName: 'प्रतिनिधि सभा सदस्य',
          },
          update: {
            name: CandidacyTypeEnum.LAW_MAKER,
            localName: 'प्रतिनिधि सभा सदस्य',
          },
          where: {
            name: CandidacyTypeEnum.LAW_MAKER,
          },
        });

        DistrictCd = districtModel?.id || DistrictName;
        const electionCode = `${year}-HOR-${stateModel.id}-${DistrictCd}-${areaCode}-${CandidateID}`;
        const elCode = `${year}-HOR-${stateModel.id}-${DistrictCd}-${areaCode}`;
        await prisma.election_results.upsert({
          create: {
            candidacyTypeId: candicyTypeModel.id,
            voteCount: TotalVoteReceived,
            electionId: election.id,
            leaderId: leader.id,
            isElected:
              Remarks === 'E' ||
              Remarks === 'निर्वाचित' ||
              Remarks === 'Elected' ||
              Remarks === 'Unopposed',
            districtId: districtModel?.id,
            code: electionCode,
            partyId: partyModel.id,
            area: areaCode,
            remarks: Remarks === 'E' ? 'निर्वाचित' : Remarks,
            stateId: stateModel.id,
            elCode,
          },
          update: {
            candidacyTypeId: candicyTypeModel.id,
            voteCount: TotalVoteReceived,
            electionId: election.id,
            leaderId: leader.id,
            isElected:
              Remarks === 'E' ||
              Remarks === 'निर्वाचित' ||
              Remarks === 'Elected' ||
              Remarks === 'Unopposed',
            districtId: districtModel.id,
            code: electionCode,
            partyId: partyModel.id,
            area: areaCode,
            remarks: Remarks === 'E' ? 'निर्वाचित' : Remarks,
            stateId: stateModel.id,
            elCode,
          },
          where: {
            code: electionCode,
          },
        });

        await prisma.party_leaders.upsert({
          create: {
            position: PartyRole.MEMBER,
            role: PartyRole.MEMBER,
            leaderId: leader.id,
            partyId: partyModel.id,
            code: `${partyModel.id}-${leader.id}`,
            startDate: new Date(),
          },
          update: {
            code: `${partyModel.id}-${leader.id}`,
            position: PartyRole.MEMBER,
            role: PartyRole.MEMBER,
            leaderId: leader.id,
            partyId: partyModel.id,
            startDate: new Date(),
          },
          where: {
            code: `${partyModel.id}-${leader.id}`,
          },
        });

        // const imageDownloadPath = path.join(__dirname, '../../../web/public/images/leaders/' + CandidateID + ".jpg");
        // axios
        //   .get(`https://result.election.gov.np/Images/Candidate/${CandidateID}.jpg`, { responseType: 'stream' })
        //   .then((response) => {
        //     response.data.pipe(ofs.createWriteStream(imageDownloadPath))
        //       .on('finish', () => {
        //         console.log('Image downloaded successfully');
        //       })
        //       .on('error', (error) => {
        //         console.error('Error downloading image:', error);
        //       });
        //   })
        //   .catch((error) => {
        //     console.error('Error fetching image:', error);
        //   });

        console.log('Inserted', electionCode);
      } catch (err) {
        console.log(err);
        console.log(result);
        continue;
      }
    }
  }
}
