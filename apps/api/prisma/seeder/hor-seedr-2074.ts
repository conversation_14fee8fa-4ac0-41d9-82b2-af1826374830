import {
  CandidacyTypeEnum,
  ElectionType,
  PartyRole,
  PrismaClient,
} from '@prisma/client';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as transliterate from 'transliteration';
import * as datefns from 'date-fns';

export const makeAbbr = (str) =>
  str
    .split(' ')
    .map((i) => i[0])
    .join('');

const prisma = new PrismaClient();

export async function seedPRElection2074() {
  const election = await prisma.elections.upsert({
    create: {
      electionType: ElectionType.BY_ELECTION,
      name: '2074 General Election',
      year: new Date('13/May/2022'),
      //   governmentId: 1,
    },
    update: {
      electionType: ElectionType.BY_ELECTION,
      name: '2074 General Election',
      year: new Date('13/May/2022'),
    },
    where: {
      name: '2074 General Election',
    },
  });
  const folderPath = path.join(__dirname, '../../data/scripts/2074-hor');
  const files = await fs.readdir(folderPath);

  for (const file of files) {
    const filePath = path.join(folderPath, file);
    const stats = await fs.stat(filePath);
    const str = filePath;
    const pattern = /(\d+)\.json$/;
    const match = str.match(pattern);

    let areaCode = '';
    if (match && match[1]) {
      areaCode = match[1].slice(-1);
    }
    if (!stats.isFile()) continue;
    const rawResult = await fs.readFile(filePath, 'utf-8');
    const results = JSON.parse(rawResult);
    for (const result of results) {
      let {
        CandidateName,
        Gender,
        Age,
        PartyID,
        SymbolID,
        SymbolName,
        CandidateID,
        StateName,
        PoliticalPartyName,
        ElectionPost,
        DistrictCd,
        DistrictName,
        State,
        SCConstID,
        CenterConstID,
        SerialNo,
        TotalVoteReceived,
        CastedVote,
        TotalVoters,
        Rank,
        Remarks,
        Samudaya,
        DOB,
        CTZDIST,
        FATHER_NAME,
        SPOUCE_NAME,
        QUALIFICATION,
        EXPERIENCE,
        OTHERDETAILS,
        NAMEOFINST,
        ADDRESS,
      } = result;
      areaCode = SCConstID;
      if (!CandidateID) {
        CandidateID = `${CandidateName}-${DistrictName}`;
      }
      const stateModel = await prisma.states.findFirst({
        where: {
          id: +State,
        },
      });

      let uniquePartyName = PoliticalPartyName || 'स्वतन्त्र';
      if (PoliticalPartyName?.trim() === 'स्वतन्त्र') {
        uniquePartyName = `${PoliticalPartyName} ${SymbolName || ''}`?.trim();
      }
      let partyModel = await prisma.parties.upsert({
        create: {
          name: PoliticalPartyName || 'स्वतन्त्र',
          localName: PoliticalPartyName || 'स्वतन्त्र',
          code: uniquePartyName,
          startDate: new Date(),
          electionSymbol: SymbolName,
        },
        update: {},
        where: {
          code: uniquePartyName,
        },
      });

      const transliterateLeader = transliterate.transliterate(CandidateName);
      const existingLeader = await prisma.leaders.findFirst({
        where: {
          localName: CandidateName,
        },
      });
      if (existingLeader) {
        CandidateID = existingLeader.ecCandidateID;
      }
      const leader = await prisma.leaders.upsert({
        create: {
          birthDate: Age ? datefns.subYears(new Date('2017-05-13'), Age) : null,
          name: transliterateLeader,
          gender: Gender === 'महिला' ? 'FEMALE' : 'MALE',
          ecCandidateID: CandidateID + '',
          experience: EXPERIENCE,
          nameOfInst: NAMEOFINST,
          otherDetails: OTHERDETAILS,
          metadata: {
            fatherName: FATHER_NAME,
            spouseName: SPOUCE_NAME,
          },
          address: ADDRESS,
          contact: '',
          description: OTHERDETAILS,
          localName: CandidateName,
        },
        update: {
          name: transliterateLeader,
          birthDate: Age ? datefns.subYears(new Date('2017-05-13'), Age) : null,
          ecCandidateID: CandidateID + '',
          experience: EXPERIENCE,
          nameOfInst: NAMEOFINST,
          otherDetails: OTHERDETAILS,
          metadata: {
            fatherName: FATHER_NAME,
            spouseName: SPOUCE_NAME,
            Samudaya,
            DOB,
          },
          address: ADDRESS,
          contact: '',
          description: OTHERDETAILS,
          localName: CandidateName,
          qualification: QUALIFICATION,
        },
        where: {
          ecCandidateID: CandidateID + '',
        },
      });

      const candicyTypeModel = await prisma.candidacy_types.upsert({
        create: {
          name: CandidacyTypeEnum.LAW_MAKER,
          localName: 'प्रतिनिधि सभा सदस्य',
        },
        update: {
          name: CandidacyTypeEnum.LAW_MAKER,
          localName: 'प्रतिनिधि सभा सदस्य',
        },
        where: {
          name: CandidacyTypeEnum.LAW_MAKER,
        },
      });

      const districtModel = await prisma.districts.findFirst({
        where: {
          localName: DistrictName,
        },
      });

      DistrictCd = districtModel?.id || DistrictName;
      const electionCode = `2074-HOR-${State}-${DistrictCd}-${areaCode}-${CandidateID}`;
      const elCode = `2074-HOR-${State}-${DistrictCd}-${areaCode}`;
      await prisma.election_results.upsert({
        create: {
          candidacyTypeId: candicyTypeModel.id,
          voteCount: TotalVoteReceived,
          electionId: election.id,
          leaderId: leader.id,
          isElected:
            Remarks === 'निर्वाचित' ||
            Remarks === 'Elected' ||
            Remarks === 'Unopposed',
          districtId: districtModel?.id,
          code: electionCode,
          partyId: partyModel.id,
          area: areaCode,
          remarks: Remarks,
          stateId: stateModel.id,
          elCode,
        },
        update: {
          candidacyTypeId: candicyTypeModel.id,
          voteCount: TotalVoteReceived,
          electionId: election.id,
          leaderId: leader.id,
          isElected:
            Remarks === 'निर्वाचित' ||
            Remarks === 'Elected' ||
            Remarks === 'Unopposed',
          districtId: districtModel.id,
          code: electionCode,
          partyId: partyModel.id,
          area: areaCode,
          remarks: Remarks,
          stateId: stateModel.id,
          elCode,
        },
        where: {
          code: electionCode,
        },
      });

      await prisma.party_leaders.upsert({
        create: {
          position: PartyRole.MEMBER,
          role: PartyRole.MEMBER,
          leaderId: leader.id,
          partyId: partyModel.id,
          code: `${partyModel.id}-${leader.id}`,
          startDate: new Date(),
        },
        update: {
          code: `${partyModel.id}-${leader.id}`,
          position: PartyRole.MEMBER,
          role: PartyRole.MEMBER,
          leaderId: leader.id,
          partyId: partyModel.id,
          startDate: new Date(),
        },
        where: {
          code: `${partyModel.id}-${leader.id}`,
        },
      });

      // const imageDownloadPath = path.join(__dirname, '../../../web/public/images/leaders/' + CandidateID + ".jpg");
      // axios
      //   .get(`https://result.election.gov.np/Images/Candidate/${CandidateID}.jpg`, { responseType: 'stream' })
      //   .then((response) => {
      //     response.data.pipe(ofs.createWriteStream(imageDownloadPath))
      //       .on('finish', () => {
      //         console.log('Image downloaded successfully');
      //       })
      //       .on('error', (error) => {
      //         console.error('Error downloading image:', error);
      //       });
      //   })
      //   .catch((error) => {
      //     console.error('Error fetching image:', error);
      //   });

      console.log('Inserted', electionCode);
    }
  }
}
