-- Duplicate records in party_leaders
SELECT
    leaderId,
    partyId,
    COUNT(*) AS duplicate_count
FROM
    party_leaders
WHERE
    leaderId IS NOT NULL
    AND partyId IS NOT NULL
GROUP BY
    leaderId,
    partyId
HAVING
    COUNT(*) > 1;

-- Insert missing records in party_leaders from cabinet_members
INSERT IGNORE INTO party_leaders (
    leaderId,
    partyId,
    startDate,
    endDate,
    position,
    code,
    createdAt,
    updatedAt
)
SELECT
    cm.leaderId,
    cm.partyId,
    cm.startedAt,
    cm.endAt,
    'Cabinet Minister' AS position,
    CONCAT (
        'cabinet_leader_',
        cm.leaderId,
        '_',
        cm.partyId,
        '_',
        cm.id
    ),
    NOW (),
    NOW ()
FROM
    cabinet_members cm
WHERE
    DATE (cm.createdAt) = CURRENT_DATE
    AND cm.partyId IS NOT NULL
    AND cm.leaderId IS NOT NULL;