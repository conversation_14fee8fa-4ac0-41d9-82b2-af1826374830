import { registerAs } from '@nestjs/config';

export const unregisteredRedisConfig = {
  connection: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    db: 1,
    retryStrategy: function (times) {
      return Math.max(Math.min(Math.exp(times), 20000), 1000); // Exponential backoff
    },
    maxRetriesPerRequest: null, // Fail after 5 retries
  },
  queue: {
    removeOnComplete: {
      age: 3600 * 12, // keep up to 1 hour
      count: 1000, // keep up to 1000 jobs
    },
    removeOnFail: {
      age: 24 * 7 * 3600, // keep up to 48 hours
    },
    delay: 500,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 60000,
    },
  },
};
export default registerAs('redis', () => {
  return unregisteredRedisConfig.connection;
});
// Removed redundant export to avoid conflicts
