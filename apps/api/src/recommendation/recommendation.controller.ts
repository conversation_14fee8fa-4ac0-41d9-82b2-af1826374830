import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { RecommendationService } from './recommendation.service';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';
import { CacheTTL } from '@nestjs/cache-manager';

@Controller('recommendations')
@UseInterceptors(RequestOriginCacheInterceptor)
@CacheTTL(3600)
export class RecommendationController {
  constructor(private readonly recommendationService: RecommendationService) {}

  @Get('/')
  async getRecommendations(
    @Query('entityType') entityType: 'leaders' | 'parties' | 'governments',
    @Query('entityId') id: string,
    @Query('limit') limit?: string,
  ) {
    const result = await this.recommendationService.getRecommendations(
      entityType,
      id,
    );
    return { entityType, id, recommendations: result };
  }
}
