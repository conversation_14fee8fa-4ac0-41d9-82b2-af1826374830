import { Module } from '@nestjs/common';
import { RecommendationController } from './recommendation.controller';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PrismaService } from 'src/prisma.service';
import { RecommendationService } from './recommendation.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { MediasService } from 'src/medias/medias.service';
import { ProjectsService } from 'src/projects/projects.service';
import { DepartmentsService } from 'src/modules/departments/departments.service';

@Module({
  controllers: [RecommendationController],
  providers: [
    most_viewedService,
    ElectionsService,
    PrismaService,
    RecommendationService,
    LeadersService,
    PartiesService,
    EntityReview,
    RatingsService,
    ContentsService,
    AiService,
    MediasService,
    ProjectsService,
    DepartmentsService,
  ],
})
export class RecommendationModule {}
