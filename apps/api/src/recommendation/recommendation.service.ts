import { Injectable, NotFoundException } from '@nestjs/common';
import { CommentableType } from '@prisma/client';
import { random, shuffle } from 'lodash';
import { MediasService } from 'src/medias/medias.service';
import { DepartmentsService } from 'src/modules/departments/departments.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { PrismaService } from 'src/prisma.service';
import { ProjectsService } from 'src/projects/projects.service';

@Injectable()
export class RecommendationService {
  constructor(
    private prisma: PrismaService,
    private mostViewedService: most_viewedService,
    private leadersService: LeadersService,
    private partiesService: PartiesService,
    private mediaService: MediasService,
    private projectsService: ProjectsService,
    private departmentsService: DepartmentsService,
  ) {}

  async getRecommendations(
    entityType:
      | 'leaders'
      | 'parties'
      | 'governments'
      | 'contents'
      | 'medias'
      | 'departments'
      | 'projects',
    entityId?: string,
  ) {
    const {
      popular,
      trending: trendingOriginal,
      viewed,
    } = await this.trendingEntitiesNew(entityType as CommentableType);

    const trendingItems = trendingOriginal?.length
      ? trendingOriginal
      : popular.length
      ? popular
      : viewed;

    const defaultEntity =
      trendingItems[Math.floor(Math.random() * trendingItems.length)] ||
      trendingItems[0];
    const nextEntityId = entityId || defaultEntity?.id;

    if (entityType === 'contents') {
      const content = await this.prisma.contents.findFirst({
        where: { id: +nextEntityId },
        include: {
          parentContent: true,
          childContents: true,
        },
      });
      if (!content)
        throw new NotFoundException(
          `Content with ID ${nextEntityId} not found`,
        );

      const reocrds = await this.prisma.contents.findMany({
        where: {
          id: { not: +nextEntityId },
          content: {
            search: `${content.content} ${content.title} ${content.parentContent?.title}`,
          },
          contentType: { notIn: ['SUMMARY', 'SUMMARY_NP'] },
        },
        take: 30,
      });
      let childContent =
        content.childContents[
          Math.floor(Math.random() * content.childContents.length)
        ];
      return [
        content.parentContent,
        ...shuffle([...reocrds, childContent]),
      ].filter(Boolean);
    }

    //@ts-expect-error
    const entity = await this.prisma[entityType].findFirst({
      where: { id: +nextEntityId },
      include:
        entityType === 'leaders' || entityType === 'parties'
          ? { party_leaders: true }
          : undefined,
    });

    if (!entity)
      throw new NotFoundException(
        `${entityType} with ID ${nextEntityId} not found`,
      );

    const [sameAddress, sameParty, trending, sameParentMedia] =
      await Promise.all([
        entityType === 'leaders' ||
        entityType === 'medias' ||
        entityType === 'projects'
          ? this.byAddress(
              entityType,
              entity.address ||
                entity.location ||
                entity.name ||
                entity.title ||
                '',
            )
          : [],
        entityType === 'leaders'
          ? this.byParty(entity.party_leaders[0]?.partyId, entityType)
          : this.byParty(nextEntityId, entityType),
        trendingItems,
        entityType === 'medias'
          ? this.byParentOrChildMedia(nextEntityId)
          : entityType === 'projects'
          ? this.closeRecommendationsOfProject(nextEntityId)
          : entityType === 'departments'
          ? this.closeRecommendationsOfDepartment(nextEntityId)
          : [],
      ]);
    if (entityId && entityType === 'leaders') {
      const closeRecommendationsOfLeader =
        await this.closeRecommendationsOfLeader(+entityId);
      return {
        items: [
          ...this.removeDuplicates(closeRecommendationsOfLeader, nextEntityId),
          ...sameAddress.slice(0, 2),
        ],
        totalItems: closeRecommendationsOfLeader.length,
      };
    }
    const combined = [
      ...sameParty,
      ...trending,
      ...sameAddress,
      ...sameParentMedia,
      ...trendingOriginal,
    ];
    const deduplicated = this.removeDuplicates(combined, nextEntityId);
    const scored = this.rankEntities(deduplicated, entity);

    return { items: shuffle(scored).slice(0, 20), totalItems: scored.length };
  }

  private async byParentOrChildMedia(mediaId: string) {
    const randomMedias = await this.prisma.medias.findMany({
      where: {
        id: {
          notIn: [+mediaId],
        },
      },
      take: 10,
      skip: random(0, 50),
    });
    const medias = await this.prisma.medias.findMany({
      where: { parentMediaId: +mediaId },
      take: 30,
    });

    return (
      medias
        .map((e) => ({
          ...e,
          topic: 'SAME_PARENT_MEDIA',
        }))
        //@ts-expect-error
        .concat(randomMedias)
    );
  }

  private async byAddress(entityType: string, address: string) {
    const keywords = Array.from(new Set(address.split(' ').slice(0, 1)));

    const entities = await this.prisma[entityType].findMany({
      where: {
        OR: [
          { address: { contains: address } },
          ...keywords.map((word) => ({ address: { contains: word } })),
        ],
      },
      take: 20,
      include:
        entityType === 'leaders' || entityType === 'parties'
          ? {
              party_leaders: true,
              election_results: {
                where: {
                  isElected: true,
                },
              },
            }
          : undefined,
    });

    return entities.map((e) => ({
      ...e,
      partyId: e.party_leaders?.[0]?.partyId,
      topic: 'SAME_ADDRESS',
    }));
  }

  private async byParty(partyId: string, entityType?: string) {
    if (!partyId) return [];
    if (entityType === 'medias') return [];
    if (entityType === 'projects') return [];
    if (entityType === 'departments') return [];

    const leaders = await this.prisma.party_leaders.findMany({
      where: { partyId: +partyId },
      include: { leader: { include: { leaders_images: true } }, party: true },
      take: 30,
    });

    return leaders.map((e) => ({
      ...e.leader,
      partyId: e.party.id,
      topic: 'SAME_PARTY',
    }));
  }

  private async trendingEntities(entityType: CommentableType) {
    const [mostViewed, mostLiked] =
      //@ts-expect-error
      entityType === 'leaders'
        ? await Promise.all([
            this.leadersService.findAll({ top: 'views', limit: 20 }),
            this.leadersService.findAll({ top: 'rates', limit: 20 }),
          ])
        : //@ts-expect-error
        entityType === 'parties'
        ? await Promise.all([
            this.partiesService.findAll({ top: 'views', limit: 20 }),
            this.partiesService.findAll({ top: 'rates', limit: 20 }),
          ])
        : await Promise.all([
            this.mediaService.findAll({ top: 'views', limit: 20 }),
            this.mediaService.findAll({ top: 'rates', limit: 20 }),
          ]);

    return [
      ...mostViewed.items.map((e) => ({
        ...e,
        partyId: e.party_leaders?.[0]?.partyId,
        topic: 'VIEWED',
      })),
      ...mostLiked.items.map((e) => ({
        ...e,
        partyId: e.party_leaders?.[0]?.partyId,
        topic: 'POPULAR',
      })),
    ];
  }

  private async trendingEntitiesNew(entityType: CommentableType) {
    const [mostViewed, mostLiked] =
      //@ts-expect-error
      entityType === 'leaders'
        ? await Promise.all([
            this.leadersService.findAll({ top: 'views', limit: 50 }),
            this.leadersService.findAll({ top: 'rates', limit: 50 }),
          ])
        : //@ts-expect-error
        entityType === 'parties'
        ? await Promise.all([
            this.partiesService.findAll({ top: 'views', limit: 50 }),
            this.partiesService.findAll({ top: 'rates', limit: 50 }),
          ])
        : //@ts-expect-error
        entityType === 'medias'
        ? await Promise.all([
            this.mediaService.findAll({ top: 'views', limit: 50 }),
            this.mediaService.findAll({ top: 'rates', limit: 50 }),
          ])
        : //@ts-expect-error
        entityType === 'projects'
        ? await Promise.all([
            this.projectsService.findAll({ top: 'views', limit: 50 }),
            this.projectsService.findAll({ top: 'rates', limit: 50 }),
          ])
        : //@ts-expect-error
        entityType === 'departments'
        ? await Promise.all([
            this.departmentsService.findAll({ top: 'views', limit: 50 }),
            this.departmentsService.findAll({ top: 'rates', limit: 50 }),
          ])
        : [];

    const entityMap = new Map<string, any>();

    // Collect viewed data
    for (const e of mostViewed?.items || []) {
      entityMap.set(e.id, {
        ...e,
        views: e.views || 0,
        likes: 0,
        partyId: e.party_leaders?.[0]?.partyId,
      });
    }

    // Collect liked data
    for (const e of mostLiked?.items || []) {
      const existing = entityMap.get(e.id);
      if (existing) {
        existing.likes = e.likes || 0;
      } else {
        entityMap.set(e.id, {
          ...e,
          views: 0,
          likes: e.likes || 0,
          partyId: e.party_leaders?.[0]?.partyId,
        });
      }
    }

    const entities = Array.from(entityMap.values());

    const maxViews = Math.max(...entities.map((e) => e.views || 1));
    const maxLikes = Math.max(...entities.map((e) => e.likes || 1));

    // Calculate trending score
    for (const e of entities) {
      const normalizedViews = e.views / maxViews;
      const normalizedLikes = e.likes / maxLikes;
      e.trendingScore = normalizedViews * 0.7 + normalizedLikes * 0.3;
    }

    const trending = [...entities]
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, 20)
      .map((e) => ({ ...e, topic: 'TRENDING' }));

    const viewed =
      mostViewed?.items.slice(0, 20).map((e) => ({
        ...e,
        partyId: e.party_leaders?.[0]?.partyId,
        topic: 'VIEWED',
      })) || [];

    const popular =
      mostLiked?.items.slice(0, 20).map((e) => ({
        ...e,
        partyId: e.party_leaders?.[0]?.partyId,
        topic: 'POPULAR',
      })) || [];

    return { trending, viewed, popular };
  }

  private removeDuplicates(entities: any[], excludeId: string) {
    const seen = new Set();
    return entities.filter((e) => {
      if (e.id === excludeId || seen.has(e.id)) return false;
      seen.add(e.id);
      return true;
    });
  }

  private rankEntities(entities: any[], currentEntity: any) {
    return entities
      .map((e) => {
        let score = 0;
        const addressWords = (e.address || '').split(' ');
        if (addressWords.some((word) => currentEntity.address?.includes(word)))
          score += 1;
        if (e.partyId === currentEntity.partyId) score += 3;
        if (e.rating?.average > 2) score += 2;
        if (e.views > 100) score += 1;
        if (e.topic === 'TRENDING') score += 5;

        return { ...e, score };
      })
      .sort((a, b) => b.score - a.score);
  }

  async closeRecommendationsOfLeader(leaderId: number) {
    // 1. Get top-rated leaders
    const topRated = await this.leadersService.findAll({
      top: 'rates',
      limit: 10,
    });
    const topViewed = await this.leadersService.findAll({
      top: 'views',
      limit: 10,
    });

    // 2. Fetch target leader with related info
    const leader = await this.prisma.leaders.findUnique({
      where: { id: leaderId },
      include: {
        leaders_images: true,
        party_leaders: true,
        election_results: {
          include: {
            elections: true,
          },
        },
      },
    });

    if (!leader?.birthDate) return [];

    const birthYear = new Date(leader.birthDate).getFullYear();
    const partyId = leader.party_leaders[0]?.partyId;
    const electionIds = leader.election_results.map((er) => er.electionId);
    const regionCode = leader.election_results[0]?.elCode ?? null;

    // 3. AGE_GROUP_COMPETITOR: Enhanced age-based competitor logic
    const ageCompetitorCandidates = await this.prisma.leaders.findMany({
      where: {
        id: { not: leader.id },
        birthDate: {
          gte: new Date(`${birthYear - 5}`),
          lte: new Date(`${birthYear + 5}`),
        },
        election_results: {
          some: {
            electionId: { in: electionIds },
            ...(regionCode && { elCode: regionCode }),
          },
        },
        party_leaders: partyId
          ? {
              none: {
                partyId: partyId,
              },
            }
          : undefined,
      },
      include: {
        leaders_images: true,
        election_results: true,
        party_leaders: true,
      },
      take: 50,
    });

    const ageGroupCompetitors = shuffle(ageCompetitorCandidates)
      .sort((a, b) => {
        const aVotes = a.election_results[0]?.voteCount || 0;
        const bVotes = b.election_results[0]?.voteCount || 0;
        return Math.abs(bVotes - aVotes);
      })
      .slice(0, 3)
      .map((l) => ({
        ...l,
        topic: 'AGE_GROUP_COMPETITOR',
      }));

    // 4. SAME_PARTY
    const sameParty = partyId
      ? await this.prisma.party_leaders
          .findMany({
            where: {
              partyId,
              leaderId: { not: leader.id },
            },
            include: { leader: { include: { leaders_images: true } } },
            take: 20,
          })
          .then((results) =>
            shuffle(results)
              .slice(0, 2)
              .map((r) => ({
                ...r.leader,
                topic: 'SAME_PARTY',
              })),
          )
      : [];

    // 5. ELECTION_COMPETITOR
    const electionCompetitors = leader.election_results.length
      ? await this.prisma.election_results
          .findMany({
            where: {
              elCode: {
                in: leader.election_results.map((item) => item.elCode),
              },
              leaderId: { not: leader.id },
            },
            include: {
              leaders: {
                include: {
                  leaders_images: true,
                },
              },
            },
            orderBy: {
              voteCount: 'desc',
            },
            take: 20,
          })
          .then((results) =>
            shuffle(results)
              .slice(0, 5)
              .map((r) => ({
                ...r.leaders,
                topic: 'ELECTION_COMPETITOR',
              })),
          )
      : [];

    // 6. POPULAR
    const popularLeaders = shuffle(topRated.items)
      .slice(0, 10)
      .map((item) => ({
        ...item,
        topic: 'POPULAR',
      }));

    const mostViewedLeaders = shuffle(topViewed.items)
      .slice(0, 10)
      .map((item) => ({
        ...item,
        topic: 'MOST_VIEWED',
      }));

    // 7. Combine and deduplicate
    const combined = [
      ...mostViewedLeaders,
      ...ageGroupCompetitors,
      ...sameParty,
      ...electionCompetitors,
      ...popularLeaders,
    ];

    const seen = new Map<number, any>();
    for (const l of combined) {
      if (!seen.has(l.id)) {
        seen.set(l.id, l);
      }
    }

    // Optional: Shuffle the final results for natural variation
    return shuffle(Array.from(seen.values()));
  }

  async closeRecommendationsOfProject(projectId: number) {
    const project = await this.prisma.projects.findUnique({
      where: { id: +projectId },
    });

    if (!project) return [];

    const randomProjects = await this.prisma.projects.findMany({
      where: {
        id: { not: +projectId },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: random(0, 45),
      take: 5,
    });

    const sameSector = await this.prisma.projects.findMany({
      where: {
        id: { not: +projectId },
        sector: project.sector,
      },
      take: 5,
    });

    const sameStatus = await this.prisma.projects.findMany({
      where: {
        id: { not: +projectId },
        status: project.status,
      },
      take: 5,
    });

    const sameType = await this.prisma.projects.findMany({
      where: {
        id: { not: +projectId },
        type: project.type,
      },
      take: 5,
    });

    const combined = [
      ...sameSector,
      ...sameStatus,
      ...sameType,
      ...randomProjects,
    ];

    const seen = new Map<number, any>();
    for (const p of combined) {
      if (!seen.has(p.id)) {
        seen.set(p.id, p);
      }
    }

    return shuffle(Array.from(seen.values()));
  }

  async closeRecommendationsOfDepartment(departmentId: number) {
    const department = await this.prisma.departments.findUnique({
      where: { id: +departmentId },
      include: { cabinet_members: true }, // include current leaders
    });

    if (!department) return [];

    const currentLeaderIds = department.cabinet_members.map((m) => m.leaderId);

    // Parallel fetch for performance
    const [randomDepartments, similarDepartments, pastDepartments] =
      await Promise.all([
        this.prisma.departments.findMany({
          where: { id: { not: +departmentId } },
          orderBy: { createdAt: 'desc' },
          skip: random(0, 15),
          take: 10,
        }),

        this.prisma.departments.findMany({
          where: {
            id: { not: +departmentId },
            description: department.description
              ? { contains: department.description }
              : undefined,
          },
          take: 5,
          include: { cabinet_members: true },
        }),

        currentLeaderIds.length > 0
          ? this.prisma.departments.findMany({
              where: {
                id: { not: +departmentId },
                cabinet_members: {
                  some: {
                    leaderId: { in: currentLeaderIds },
                  },
                },
              },
              take: 5,
              include: { cabinet_members: true },
            })
          : [],
      ]);

    const combined: any[] = [];
    const seen = new Set<number>();

    for (const dept of [
      ...similarDepartments,
      ...pastDepartments,
      ...randomDepartments,
    ]) {
      if (!seen.has(dept.id)) {
        seen.add(dept.id);
        combined.push(dept);
      }
    }

    return shuffle(combined);
  }
}
