import { BullModule } from '@nestjs/bullmq';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { unregisteredRedisConfig } from 'config/redis.configuration';
import {
  QUEUE_AI_IMAGE_FETCHER,
  QUEUE_AI_SUMMARY,
  QUEUE_SEARCH_RESULT,
} from 'src/constants';

@Global()
@Module({
  imports: [
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_AI_SUMMARY,

      useFactory: async (configService: ConfigService) => {
        return {
          connection: {
            ...unregisteredRedisConfig.connection,
            port: Number(unregisteredRedisConfig.connection.port),
          },
          queue: unregisteredRedisConfig.queue,
          name: QUEUE_AI_SUMMARY,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_AI_IMAGE_FETCHER,

      useFactory: async (configService: ConfigService) => {
        return {
          connection: {
            ...unregisteredRedisConfig.connection,
            port: Number(unregisteredRedisConfig.connection.port),
          },
          queue: unregisteredRedisConfig.queue,
          name: QUEUE_AI_IMAGE_FETCHER,
        };
      },
    }),
    BullModule.registerQueueAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      name: QUEUE_SEARCH_RESULT,

      useFactory: async (configService: ConfigService) => {
        return {
          connection: {
            ...unregisteredRedisConfig.connection,
            port: Number(unregisteredRedisConfig.connection.port),
          },
          queue: unregisteredRedisConfig.queue,
          name: QUEUE_SEARCH_RESULT,
        };
      },
    }),
  ],
  exports: [BullModule],
})
export class QueueModule {}
