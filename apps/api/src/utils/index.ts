import { format, formatDistance } from 'date-fns';
import { forOwn, set } from 'lodash';
import { parser } from 'html-metadata-parser';
import { MetaResult } from 'src/interfaces/ILinkPreviewAPI';

export function deserializeFromDotNotation(dotObject) {
  const result = {};
  forOwn(dotObject, (value, key) => {
    set(result, key, value);
  });
  return result;
}

export function isAppEnvDev() {
  return process.env.NODE_ENV !== 'production';
}

export const DEFAULT_SUMMARY = 'Summary not available.';

export function getImageUrlWithFallback(img: string | null, itemId: string) {
  return img || `https://nepaltracks.com/images/leaders/${itemId}.jpg`;
}

export const formatAppDate = (date?: string | Date) => {
  if (!date) return undefined;
  return format(new Date(date), 'yyyy/MM/dd');
};

export const formatToStandardReadableDate = (date?: string | Date) => {
  return `${formatAppDate(date)}, ${formatDistance(
    new Date(date),
    new Date(),
  )}`;
};

export const getMetadata = async (url: string): Promise<MetaResult | null> => {
  try {
    // @ts-ignore
    const result = (await parser(url)) as MetaResult;
    return result;
  } catch (err) {
    console.log(err);
    return null;
  }
};
export const apiIdWrapper = (apiId: string) => `API_ID_${apiId}`;
