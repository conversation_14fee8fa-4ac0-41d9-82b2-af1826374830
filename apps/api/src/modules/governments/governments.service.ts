import { Injectable } from '@nestjs/common';
import { CreateGovernmentDto } from './dto/create-government.dto';
import { UpdateGovernmentDto } from './dto/update-government.dto';
import { PrismaService } from 'src/prisma.service';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import {
  CandidacyTypeEnum,
  CommentableType,
  GOVERNMENT_TYPE,
  governments,
  Prisma,
} from '@prisma/client';
import { groupBy, head, pickBy, toLower, toUpper, uniqBy } from 'lodash';
import { addYears } from 'date-fns';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { DEFAULT_SUMMARY } from 'src/utils';
import { EntityReview } from '../ratings/EntityReview';
import { EntityTypeEnum } from 'src/constants';

@Injectable()
export class GovernmentsService {
  constructor(
    private prismaService: PrismaService,
    private contentsService: ContentsService,
    private aiService: AiService,
    private entityReview: EntityReview,
  ) {}

  async getOverviewOfGovernment(id: number, level: string) {
    let {
      cabinet_members,
      elections,
      head: headOfState,
      ...government
    } = await this.prismaService.governments.findFirst({
      where: {
        id,
      },
      include: {
        head: true,
        elections: true,
        cabinet_members: {
          orderBy: {
            department: {
              rank: 'asc', // or 'desc' depending on sort direction
            },
          },
          include: {
            party: true,
            leaders: {
              include: {
                leaders_images: true,
                party_leaders: {
                  include: {
                    party: true,
                  },
                },
              },
            },
            department: true,
          },
        },
      },
    });

    const numberOfMembersByParty = groupBy(
      cabinet_members,
      (member) => member.party?.code || member.partyName,
    );
    const stats = await this.contentsService.getStats(id, 'GOVERNMENT');
    const response = {
      government,
      stats,
      coaltion: Object.keys(numberOfMembersByParty).map((patyCode) => {
        return {
          party: head(numberOfMembersByParty[patyCode])?.party || {
            id: 0,
            name: patyCode,
            localName: patyCode,
          },
          members: numberOfMembersByParty[patyCode]
            .sort((a, b) => (b.rank || 0) - (a.rank || 0))
            .map(({ leaders, ...rest }) => {
              const { department, ...cabinet_member } = rest;
              if (!leaders) {
                delete leaders?.party_leaders;
              }

              return { leader: leaders, cabinet_member, department };
            }),
        };
      }),
    };

    const { summary, summaryNP } = await this.withSummary(government, response);
    return { ...response, summary, summaryNP };
  }

  async withSummary(government: governments, response: any) {
    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: government.id,
        resourceType: 'GOVERNMENT',
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    let summary = (
      await this.contentsService.getContents({
        resourceId: government.id,
        resourceType: 'GOVERNMENT',
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    if (!summary || !summaryNP) {
      summary = DEFAULT_SUMMARY;
      await this.aiService.queueSummaryBuilder(
        {
          id: government.id,
          type: EntityTypeEnum.Government,
        },
        EntityTypeEnum.Government + government.id,
      );
    }
    return { summary, summaryNP };
  }
  async getMinistriesOfGovernment(arg0: number, level: string) {
    const departments = await this.prismaService.cabinet_members.findMany({
      where: {
        governmentId: arg0,
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        department: true,
      },
      orderBy: {
        rank: 'desc', // or 'desc' if you want reverse order
      },
    });
    return departments;
  }
  getOverviewfGovernment(arg0: number, level: string) {
    throw new Error('Method not implemented.');
  }
  create(createGovernmentDto: CreateGovernmentDto) {
    return 'This action adds a new government';
  }

  async findAll(
    level: string,
    { top, ...query }: PaginationSortSearchDto = {},
  ) {
    if (level === 'federal' || level === 'monarchy') {
      return this.prismaService.index(
        'governments',
        {
          //@ts-expect-error
          government_type: {
            in: [GOVERNMENT_TYPE.FEDERAL, GOVERNMENT_TYPE.MONARCHY],
          },
        },
        {
          include: { head: true },
          orderBy: {
            startedAt: 'desc',
          },
          ...query,
        },
      );
    }
    if (level === 'provincial') {
    }
    if (level === 'local') {
      const muncipals = await this.prismaService.index(
        'municipals',
        {
          searchFields: ['name', 'localName'],
        },
        {
          // @ts-expect-error
          include: {
            states: true,
            districts: true,
            election_results: {
              where: {
                remarks: {
                  not: null,
                },
              },
              include: {
                candidacyType: true,
                leaders: {
                  include: {
                    leaders_images: true,
                  },
                },
              },
            },
          },
          ...query,
        },
      );
      muncipals.items = muncipals.items.map((item) => {
        return {
          ...item,
        };
      });
      return muncipals;
    }
    return [];
    // return this.prismaService.governments.findMany();
  }

  async findOne(id: number, level: GOVERNMENT_TYPE) {
    const nextLevel = toUpper(level);
    const government = await this.prismaService.governments.findFirst({
      where: {
        id,
        // government_type: nextLevel as GOVERNMENT_TYPE,
      },
      include: {
        head: true,
        elections: true,
        cabinet_members: {
          include: {
            leaders: {
              include: {
                leaders_images: true,
              },
            },
            department: true,
          },
        },
      },
    });

    if (!government) return null;
    const projects = await this.prismaService.projects_by.findMany({
      where: {
        governmentId: government?.id,
      },
      include: {
        projects: true,
      },
    });

    if (nextLevel === 'LOCAL') {
      if (government) return government;

      const municipal = await this.prismaService.municipals.findFirst({
        where: {
          id,
        },
        include: {
          election_results: {
            where: {
              isElected: true,
            },
            include: {
              elections: true,
              candidacyType: true,
            },
          },
        },
      });
      const headOfLocalGovernment = municipal?.election_results?.find(
        (result) =>
          result.candidacyType?.name === CandidacyTypeEnum.MAYOR ||
          result.candidacyType?.name === CandidacyTypeEnum.CHAIRPERSON,
      );
      const goverment = await this.prismaService.governments.create({
        data: {
          municipalId: id,
          name: municipal?.name || 'Unknown',
          government_type: GOVERNMENT_TYPE.LOCAL,
          startedAt: municipal.election_results[0].elections?.year,
          endAt: addYears(municipal.election_results[0].elections?.year, 5),
          headId: headOfLocalGovernment?.leaderId,
        },
        include: {
          head: true,
        },
      });

      return {
        goverment,
        election: municipal.election_results[0],
        head: headOfLocalGovernment,
        electedMembers: municipal.election_results,
        projects: projects.map((item) => item.projects),
      };
    }
    const { summary, summaryNP, ...overview } =
      await this.getOverviewOfGovernment(government.id, level);
    return {
      ...government,
      governmentOverview: overview,
      summary,
      summaryNP,
      projects: projects.map((item) => item.projects),
    };
  }

  update(id: number, updateGovernmentDto: UpdateGovernmentDto) {
    return `This action updates a #${id} government`;
  }

  remove(id: number) {
    return this.prismaService.governments.delete({
      where: {
        id,
      },
    });
  }
  async index(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = entityId
      ? {
          where: {},
        }
      : {};

    return this.entityReview.findAll(
      {
        model: CommentableType.Government,
        name: 'governments',
      },
      {
        searchFields: ['name'],
        ...nextQuery,
        //@ts-expect-error
        include: {
          head: true,
        },
        ...additionalWhere,
      },
    );
  }

  async analytics(query: PaginationSortSearchDto & { partyId?: number }) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let recentlyAdded = await this.prismaService.governments.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        createdAt: { gt: oneWeekAgo },
      },
      include: {
        head: true,
      },
    });
    let limit = 10;
    if (query.partyId) {
      limit = 1000;
    }
    let most_viewed = await this.index({
      top: 'views',
      limit: limit,
    });
    let mostLiked = await this.index({
      top: 'rates',
      limit: limit,
    });
    let lessLiked = await this.index({
      top: 'rates',
      limit: limit,
      sort_by: 'asc',
    });
    const hotParliamentLeaders = await this.getPopularCabinetMembers(
      query,
      +query.partyId,
    );

    return {
      lessLiked,
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
      hotCabinetMembers: hotParliamentLeaders,
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async getPopularCabinetMembers(
    pagination: PaginationSortSearchDto,
    governmentId?: number,
  ) {
    const limit = parseInt(pagination.limit as any) || 10;
    const page = parseInt(pagination.page as any) || 1;
    const offset = (page - 1) * limit;

    const governmentFilter = governmentId
      ? Prisma.sql`AND  gcm.governmentId = ${governmentId}`
      : Prisma.empty;

    const resultRaw = await this.prismaService.$queryRaw<
      Array<{
        id: number;
        name: string;
        localName: string;
        img: string | null;
        address: string | null;
        gender: string | null;
        birthDate: Date | null;
        ecCandidateID: string | null;
        governmentName: string;
        governmentId: number;
        total_views: bigint;
        average_rating: number | null;
        total_ratings: bigint;
        total_content: bigint;
        leaders_images: string;
      }>
    >(Prisma.sql`
      SELECT
        l.id,
        l.name,
        l.localName,
        l.img,
        l.address,
        l.gender,
        l.birthDate,
        l.ecCandidateID,
        g.name AS governmentName,
        gcm.startedAt as governmentStartedAt,
        gcm.endAt as governmentEndAt,
        g.id AS governmentId,
        gcm.startedAt,
        gcm.endAt,
        gcm.role,
        gcm.rank,
        d.id as departmentId,
        d.name as departmentName,

  
        COALESCE(mv.total_views, 0) AS total_views,
        ROUND(r.avg_rating, 2) AS average_rating,
        COALESCE(r.total_ratings, 0) AS total_ratings,
        COALESCE(c.total_content, 0) AS total_content,
  
        COALESCE(
          JSON_ARRAYAGG(
            JSON_OBJECT('id', li.id, 'url', li.url, 'enabled', li.enabled, 'isDefault', li.isDefault)
          ),
          JSON_ARRAY()
        ) AS leaders_images
  
        FROM cabinet_members gcm
        JOIN leaders l ON gcm.leaderId = l.id
        JOIN governments g ON g.id = gcm.governmentId
        join departments d ON d.id = gcm.departmentId
      LEFT JOIN leaders_images li ON li.leadersId = l.id AND li.enabled = TRUE
  
      LEFT JOIN (
        SELECT resourceId, SUM(views) AS total_views
        FROM most_viewed
        WHERE resourceType = 'leaders'
        GROUP BY resourceId
      ) mv ON mv.resourceId = l.id
  
      LEFT JOIN (
        SELECT rateOnId, AVG(value) AS avg_rating, COUNT(id) AS total_ratings
        FROM ratings
        WHERE rateOnType = 'LEADER'
        GROUP BY rateOnId
      ) r ON r.rateOnId = l.id
  
      LEFT JOIN (
        SELECT resourceId, COUNT(id) AS total_content
        FROM contents
        WHERE resourceType = 'LEADER'
          AND contentType NOT IN ('SUMMARY', 'SUMMARY_NP', 'NEWS')
        GROUP BY resourceId
      ) c ON c.resourceId = l.id
  
      WHERE 1=1
      ${governmentFilter}
  
      GROUP BY
  gcm.id,
  gcm.governmentId,
  gcm.leaderId,
  gcm.departmentId,
  gcm.role,
  gcm.rank,
  gcm.startedAt,
  gcm.endAt,
  gcm.isResigned,
  gcm.isActing,
  gcm.portfolioTitle,
  gcm.remarks,
  gcm.appointedBy,
  gcm.appointmentMethod,
  gcm.officialLink,
  gcm.createdAt,
  gcm.updatedAt,

  d.id,
  d.name,

  l.id,
  l.name,
  l.localName,
  l.img,
  l.address,
  l.gender,
  l.birthDate,
  l.ecCandidateID,

  mv.total_views,
  r.avg_rating,
  r.total_ratings,
  c.total_content
  
      ORDER BY
        total_views DESC,
        average_rating DESC,
        total_content DESC
      LIMIT ${limit} OFFSET ${offset};
    `);

    const data = resultRaw.map((row) => ({
      ...row,
      total_views: Number(row.total_views),
      total_ratings: Number(row.total_ratings),
      total_content: Number(row.total_content),
      leaders_images: row.leaders_images,
    }));
    const dedupIdsSet = new Set();
    const dedup = data.filter((item) => {
      if (dedupIdsSet.has(item.id)) return false;
      dedupIdsSet.add(item.id);
      return item.total_views + item.total_ratings + item.total_content > 0;
    });

    return {
      items: dedup,
      page,
      limit,
    };
  }
}
