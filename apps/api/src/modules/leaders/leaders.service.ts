import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { CreateLeaderDto } from './dto/create-leader.dto';
import { UpdateLeaderDto } from './dto/update-leader.dto';
import { PrismaService } from 'src/prisma.service';
import { RatingsService } from '../ratings/ratings.service';
import { PaginationSortSearchDto } from './dto/index.dto';
import { EntityReview } from '../ratings/EntityReview';
import { CommentableType, ElectionType } from '@prisma/client';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { DEFAULT_SUMMARY } from 'src/utils';
import { EntityTypeEnum } from 'src/constants';
import { random, shuffle } from 'lodash';

@Injectable()
export class LeadersService {
  constructor(
    private prismaService: PrismaService,
    private entityReview: EntityReview,
    private ratingService: RatingsService,
    private contentsService: ContentsService,
    private aiService: AiService,
  ) {}

  async getLeadersByIds(ids: number[]) {
    return this.prismaService.leaders.findMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
  }
  async findAll(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = entityId
      ? {
          where: {
            party_leaders: {
              some: {
                partyId: +entityId,
              },
            },
          },
        }
      : {};

    return this.entityReview.findAll(
      {
        model: CommentableType.Leader,
        name: 'leaders',
      },
      {
        ...nextQuery,
        //@ts-expect-error
        include: {
          leaders_images: true,
          party_leaders: {
            include: {
              party: true,
            },
          },
        },
        ...additionalWhere,
      },
    );
  }

  async findOne(id: number) {
    const leader = await this.prismaService.leaders.findFirstOrThrow({
      where: {
        id,
      },
      include: {
        leaders_images: true,
        party_leaders: {
          include: {
            party: true,
          },
        },
      },
    });

    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: leader.id + '',
      rateOnType: 'Leader',
    });
    const stats = await this.contentsService.getStats(leader.id, 'LEADER');
    let summary = (
      await this.contentsService.getContents({
        resourceId: leader.id,
        resourceType: 'LEADER',
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: leader.id,
        resourceType: 'LEADER',
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    const cabinetTenures = await this.prismaService.cabinet_members.findMany({
      where: {
        leaderId: leader.id,
      },
      include: {
        department: true,
        governments: true,
      },
    });

    const parliamentTenures =
      await this.prismaService.parliament_members.findMany({
        where: {
          memberId: leader.id,
        },
        include: {
          parliament: true,
          party: true,
          member: true,
        },
      });

    const electionResults = await this.prismaService.election_results.findMany({
      where: {
        leaderId: leader.id,
      },
      include: {
        elections: true,
        districts: true,
        municipals: true,
        states: true,
        ward: true,
        candidacyType: true,
      },
    });

    const projects = await this.prismaService.projects_by.findMany({
      where: {
        leaderId: leader.id,
      },
      include: {
        projects: true,
      },
    });

    const response = { ...leader, stats, rating };
    if (!summary || !summaryNP) {
      try {
        summary = DEFAULT_SUMMARY;
        await this.aiService.queueSummaryBuilder(
          {
            id: leader.id,
            type: EntityTypeEnum.Leader,
          },
          EntityTypeEnum.Leader + leader.id,
        );
      } catch (e) {
        console.log(e);
      }
    }
    return {
      ...response,
      summary,
      summaryNP,
      electionResults,
      projects: projects.map((item) => item.projects),
      tenures: { cabinetTenures, parliamentTenures },
    };
  }

  update(id: number, updateLeaderDto: UpdateLeaderDto) {
    return this.prismaService.leaders.update({
      where: {
        id,
      },
      data: {
        ...updateLeaderDto,
      },
    });
  }

  remove(id: number) {
    return this.prismaService.leaders.delete({
      where: {
        id,
      },
    });
  }

  async electionInformation(id: number) {
    const data = await this.prismaService.leaders.findFirstOrThrow({
      where: {
        id,
      },
      include: {
        election_results: {
          include: {
            parties: true,
            candidacyType: true,
            districts: true,
            elections: true,
            ward: {
              include: {
                states: true,
                municipals: true,
              },
            },
          },
        },
      },
    });

    return data;
  }

  async findCandidateElectionsOpponents(id: number) {
    const electionData = await this.prismaService.leaders.findFirstOrThrow({
      where: {
        id,
      },
      include: {
        leaders_images: true,
        election_results: {
          include: {
            parties: true,
            candidacyType: true,
            districts: true,
            elections: true,
            ward: {
              include: {
                states: true,
                municipals: true,
              },
            },
          },
        },
      },
    });
    const allOpponents = await Promise.all(
      electionData.election_results.map(async (result) => {
        const opponents = await this.prismaService.election_results.findMany({
          where: {
            electionId: result.electionId,
            elCode: result.elCode,
            candidacyTypeId: result.candidacyTypeId,
          },
          include: {
            leaders: {
              include: {
                leaders_images: true,
              },
            },
            parties: true,
            candidacyType: true,
            districts: true,
            elections: true,
            ward: {
              include: {
                states: true,
                municipals: true,
              },
            },
          },
          orderBy: {
            voteCount: 'desc',
          },
        });
        return opponents;
      }),
    );

    const mergedOpponents = allOpponents
      .flat()
      .sort((a, b) => b.voteCount - a.voteCount);
    const uniqueOpponents = Array.from(
      new Set(mergedOpponents.map((o) => o.id)),
    ).map((id) => mergedOpponents.find((o) => o.id === id));
    const opponents = uniqueOpponents.slice(0, 10);
    return opponents.filter((opponent) => opponent.leaderId !== id);
  }
  leadersParty(leaderId: number) {
    return this.prismaService.party_leaders.findMany({
      distinct: 'partyId',
      where: {
        leaderId,
      },
      include: {
        party: true,
      },
    });
  }

  async analytics(query: { partyId?: number } & PaginationSortSearchDto) {
    let limit = +query.limit || 10;
    let page = query.page || '1';

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const now = new Date();

    // For young leaders: random cutoff between 20 and 55 (OK as is)
    const youngAgeCutoff = query.category === 'young' ? 55 : random(20, 40);

    // For old leaders: random cutoff between MIN_OLD_AGE and 65
    const MIN_OLD_AGE = 50; // set minimum age to avoid too young results

    const oldAgeCutoff = query.category === 'old' ? 65 : random(55, 100); // random between 50 and 65

    const youngCutoffDate = new Date(now);
    youngCutoffDate.setFullYear(now.getFullYear() - youngAgeCutoff);

    const oldCutoffDate = new Date(now);
    oldCutoffDate.setFullYear(now.getFullYear() - oldAgeCutoff);

    // Now query remains the same as before

    // Fetch young leaders: birthDate > cutoff (younger than cutoff)
    const youngLeaders =
      !query.category || query.category === 'young'
        ? await this.prismaService.election_results.findMany({
            where: {
              isElected: true,
              leaders: {
                birthDate: {
                  gt: youngCutoffDate,
                },
              },
            },
            include: {
              leaders: {
                include: {
                  leaders_images: true,
                },
              },
              candidacyType: true,
            },
            orderBy: {
              voteCount: 'desc',
            },
            take: limit,
            skip: +page * limit,
          })
        : [];

    // Fetch old leaders: birthDate < cutoff (older than cutoff)
    const oldestLeaders =
      !query.category || query.category === 'old'
        ? await this.prismaService.election_results.findMany({
            where: {
              isElected: true,
              leaders: {
                birthDate: {
                  lt: oldCutoffDate,
                },
              },
            },
            include: {
              leaders: {
                include: {
                  leaders_images: true,
                },
              },
              candidacyType: true,
            },
            orderBy: {
              voteCount: 'desc',
            },
            take: limit,
            skip: +page * limit,
          })
        : [];

    let recentlyAdded =
      !query.category || query.category === 'recentlyAdded'
        ? await this.prismaService.leaders.findMany({
            take: limit,
            skip: +page * limit,
            orderBy: {
              createdAt: 'desc',
            },
            where: {
              createdAt: { gt: oneWeekAgo },
            },
          })
        : [];

    if (query.partyId) {
      limit = 1000;
    }
    let most_viewed =
      !query.category || query.category === 'most_viewed'
        ? await this.findAll({
            top: 'views',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };

    let mostLiked =
      !query.category || query.category === 'mostLiked'
        ? await this.findAll({
            top: 'rates',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };

    let lessLiked =
      !query.category || query.category === 'lessLiked'
        ? await this.findAll({
            top: 'rates',
            limit: limit,
            page,
            sort_by: 'asc',
          })
        : { items: [], totalItems: 0 };

    if (query.partyId) {
      //@ts-expect-error
      most_viewed = {
        items: most_viewed.items.filter((item) =>
          item.party_leaders?.some(
            (party) => +party.partyId === +query.partyId,
          ),
        ),
      };
      //@ts-expect-error
      mostLiked = {
        items: mostLiked.items.filter((item) =>
          item.party_leaders?.some(
            (party) => +party.partyId === +query.partyId,
          ),
        ),
      };
      recentlyAdded = [];
    }

    return {
      most_viewed,
      recentlyAdded: {
        items: recentlyAdded,
      },
      mostLiked,
      lessLiked,

      young: {
        items: shuffle(
          youngLeaders.map((item) => ({
            ...item.leaders,
            candidacyType: item.candidacyType,
          })),
        ),
        totalItems: 10000,
      },
      old: {
        items: shuffle(
          oldestLeaders.map((item) => ({
            ...item.leaders,
            candidacyType: item.candidacyType,
          })),
        ),
        totalItems: 10000,
      },
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async getBirthdayLeaders() {
    const today = new Date();
    const month = today.getMonth() + 1; // JavaScript months are 0-indexed
    const day = today.getDate();

    const leaders = await this.prismaService.leaders.findMany({
      where: {
        birthDate: {
          not: null,
        },
      },
      include: {
        party_leaders: {
          include: {
            party: true,
          },
        },
      },
    });

    // Filter leaders whose birthday is today (regardless of year)
    const birthdayLeaders = leaders.filter((leader) => {
      if (!leader.birthDate) return false;

      const birthDate = new Date(leader.birthDate);
      return birthDate.getMonth() + 1 === month && birthDate.getDate() === day;
    });

    // Get ratings for each leader
    const leaderIds = birthdayLeaders.map((leader) => leader.id + '');
    const ratingsMap = await this.ratingService.ratingsOfEntities(
      leaderIds,
      CommentableType.Leader,
    );

    // Add ratings to leaders
    return birthdayLeaders.map((leader) => ({
      ...leader,
      rating: ratingsMap[leader.id] || { average: 0, count: 0 },
    }));
  }

  async getBirthdayLeadersRaw() {
    const today = new Date();
    const month = today.getMonth() + 1;
    const day = today.getDate();

    const leaders = await this.prismaService.$queryRawUnsafe(`
      SELECT l.*, p.localName as partyShortName
      FROM leaders l
      LEFT JOIN party_leaders pl ON pl.leaderId = l.id
      LEFT JOIN parties p ON p.id = pl.partyId
      WHERE MONTH(l.birthDate) = ${month}
        AND DAY(l.birthDate) = ${day}
    `);

    return leaders;
  }

  async getPopularityGraph(resourceid: number) {
    const ratingGraph = await this.ratingService.getPopularityReport(
      resourceid + '',
      CommentableType.Leader,
    );
    return {
      ratingGraph,
    };
  }
}
