import { IsOptional, IsString } from 'class-validator';

export class PaginationSortSearchDto {
  @IsOptional()
  page?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  // @ApiPropertyOptional({
  //   description:
  //     'sorts the result, provide in format like "id:desc" or "id:asc"',
  // })
  sort_by?: string;

  @IsOptional()
  @IsString()
  // @ApiPropertyOptional({
  //   description: 'Number of result to be fetched, default is 10',
  // })
  limit?: number;

  @IsOptional()
  @IsString()
  top?:
    | 'views'
    | 'rates'
    | 'likes'
    | 'elected'
    | 'local-election'
    | 'general-election'
    | 'provincial-election';

  @IsOptional()
  @IsString()
  entityId?: string;

  // @IsOptional()
  // @IsString()
  // entityType?: string

  category?:
    | 'most_viewed'
    | 'mostLiked'
    | 'recentlyAdded'
    | 'young'
    | 'old'
    | 'lessLiked';
}
