import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
} from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { CreateAnalyticsDto } from './dto/create-analytics.dto';
import { UpdateAnalyticsDto } from './dto/update-analytics.dto';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';

@Controller('analytics')
@UseInterceptors(RequestOriginCacheInterceptor)
export class AnalyticsController {

}
