import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { CreateRatingDto } from './dto/create-rating.dto';
import { UpdateRatingDto } from './dto/update-rating.dto';
import { PrismaService } from 'src/prisma.service';
import { RatingParams } from './dto/RatingParams';
import { IUser } from 'src/interfaces/IUser';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { LeadersService } from '../leaders/leaders.service';
import { CommentableType, ratings } from '@prisma/client';
import { subMonths } from 'date-fns';
import { startCase } from 'lodash';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RatingsService {
  constructor(private prismaService: PrismaService) {}
  async create(
    createRatingDto: CreateRatingDto,
    context: RatingParams,
    user: IUser,
  ) {
    const now = new Date();
    const oneMonthAgo = subMonths(now, 1);

    const existingRating = await this.prismaService.ratings.findFirst({
      where: {
        userId: user.userId,
        rateOnId: context.rateOnId,
        rateOnType: context.rateOnType,
        createdAt: {
          gte: oneMonthAgo, // rating exists within the last month
        },
      },
    });

    if (existingRating) {
      throw new Error(
        `You can only rate a ${startCase(context.rateOnType)} once per month.`,
      );
    }

    const rating = await this.prismaService.ratings.create({
      data: {
        comment: createRatingDto.comment,
        rateOnId: context.rateOnId,
        rateOnType: context.rateOnType,
        value: createRatingDto.value,
        userId: user.userId,
        hash: uuidv4(),
      },
    });

    return rating;
  }
  findAll(context: RatingParams, query: PaginationSortSearchDto) {
    return this.prismaService.index(
      'ratings',
      {
        searchFields: ['name', 'localName'],
        //@ts-expect-error
        rateOnId: context.rateOnId,
        rateOnType: context.rateOnType,
      },
      {
        ...query,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              profileImage: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
    );
  }

  findOne(id: number) {
    return `This action returns a #${id} rating`;
  }

  async update(
    id: number,
    updateRatingDto: UpdateRatingDto,
    context: RatingParams,
    user: IUser,
  ) {
    const rating = await this.prismaService.ratings.findFirstOrThrow({
      where: { id },
    });
    if (
      !rating ||
      (rating.userId === user.userId &&
        // rating.rateOnId === context.rateOnId &&
        rating.rateOnType === context.rateOnType)
    )
      return null;

    return this.prismaService.ratings.update({
      where: {
        id,
      },
      data: {
        ...updateRatingDto,
      },
    });
  }

  async remove(id: number, user: IUser) {
    const rating = await this.prismaService.ratings.findFirstOrThrow({
      where: { id },
    });
    if (rating.userId !== user.userId) return null;
    return this.prismaService.ratings.delete({
      where: {
        id: id,
      },
    });
  }

  async ratingAnalysis(context: RatingParams) {
    const stats = await this.prismaService.ratings.aggregate({
      where: {
        rateOnId: context.rateOnId,
        rateOnType: context.rateOnType,
      },
      _avg: {
        value: true,
      },
      _count: {
        id: true,
      },
    });
    let deepRateInfo;
    if (!context.excludeDeepAnalysis) {
      deepRateInfo = await this.prismaService.ratings.groupBy({
        by: ['value'],
        where: {
          rateOnId: context.rateOnId,
          rateOnType: context.rateOnType,
        },
        _count: {
          value: true,
        },
      });
    }
    const result = {
      average: Number(stats._avg.value).toFixed(2),
      count: stats._count.id,
      rates: context.excludeDeepAnalysis
        ? null
        : deepRateInfo.reduce(
            (prev, cur) => ({
              ...prev,
              [cur.value]: {
                total: cur._count.value,
                percent: Math.round((cur._count.value / stats._count.id) * 100),
              },
            }),
            {},
          ),
    };
    return result;
  }

  async topRated(
    context: Omit<RatingParams, 'rateOnId'>,
    query: PaginationSortSearchDto = {},
    options?: {
      minAvg?: number;
      maxAvg?: number;
    },
  ) {
    const { minAvg, maxAvg } = options || {};

    const having: any = {};

    if (minAvg !== undefined && maxAvg !== undefined) {
      having.value = { _avg: { gte: minAvg, lte: maxAvg } };
    } else if (minAvg !== undefined) {
      having.value = { _avg: { gte: minAvg } };
    } else if (maxAvg !== undefined) {
      having.value = { _avg: { lte: maxAvg } };
    }
    const results = await this.prismaService.paginatedGroupBy(
      'ratings',
      {
        by: ['rateOnId'],
        having: Object.keys(having).length > 0 ? having : undefined,
        where: {
          rateOnType: context.rateOnType,
        },
        _count: {
          value: true,
        },
        _avg: {
          value: true,
        },
        orderBy: {
          _avg: {
            value: 'desc',
          },
        },
      },
      //@ts-expect-error
      {
        ...query,
      },
    );
    return {
      items: results.items,
      totalItems: results.totalItems,
    };
  }

  async ratingsOfEntities(entityIds: string[], entityType: CommentableType) {
    const ratings = await this.prismaService.ratings.groupBy({
      by: ['rateOnId', 'rateOnType'],
      _count: {
        id: true,
      },
      _avg: {
        value: true,
      },
      orderBy: {
        rateOnId: 'asc',
      },
      where: {
        rateOnId: {
          in: entityIds.map((item) => item + ''),
        },
        rateOnType: entityType,
      },
    });

    const resultMap = ratings.reduce((result, item) => {
      result[`${item.rateOnId}-${item.rateOnType}`] = {
        average: +Number(item._avg.value).toFixed(2),
        count: item._count.id,
      };
      return result;
    }, {});
    return resultMap;
  }

  async ratingsByUser(userId: number) {
    const ratings = await this.prismaService.index(
      'ratings',
      {
        //@ts-expect-error
        userId: userId,
      },
      {
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              profileImage: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
    );
    ratings.items = await this.getEnrichedContents(ratings.items);
    return ratings;
  }

  async getEnrichedContents(ratings: ratings[]) {
    // Group contents by resourceType
    const grouped = ratings.reduce((acc, content) => {
      if (!acc[content.rateOnType]) acc[content.rateOnType] = [];
      acc[content.rateOnType].push(+content.rateOnId);
      return acc;
    }, {} as Record<string, number[]>);

    // Load related resources by type
    const [
      governments,
      leaders,
      parties,
      parliaments,
      departments,
      elections,
      municipals,
      wards,
      contents,
    ] = await Promise.all([
      this.prismaService.governments.findMany({
        where: {
          id: { in: grouped['GOVERNMENT'] || grouped['Government'] || [] },
        },
      }),
      this.prismaService.leaders.findMany({
        where: { id: { in: grouped['LEADER'] || grouped['Leader'] || [] } },
      }),
      this.prismaService.parties.findMany({
        where: { id: { in: grouped['PARTY'] || grouped['Party'] || [] } },
      }),
      this.prismaService.parliaments.findMany({
        where: { id: { in: grouped['PARLIAMENT'] || [] } },
      }),
      this.prismaService.departments.findMany({
        where: {
          id: { in: grouped['DEPARTMENT'] || grouped['Department'] || [] },
        },
      }),
      this.prismaService.elections.findMany({
        where: { id: { in: grouped['ELECTION'] || [] } },
      }),
      this.prismaService.municipals.findMany({
        where: { id: { in: grouped['MUNICIPAL'] || [] } },
      }),
      this.prismaService.wards.findMany({
        where: { id: { in: grouped['WARD'] || [] } },
      }),
      this.prismaService.contents.findMany({
        where: { id: { in: grouped['CONTENT'] || [] } },
      }),
    ]);

    // Maps for fast access
    const resourceMaps = {
      GOVERNMENT: new Map(governments.map((g) => [g.id, g])),
      LEADER: new Map(leaders.map((l) => [l.id, l])),
      PARTY: new Map(parties.map((p) => [p.id, p])),
      PARLIAMENT: new Map(parliaments.map((p) => [p.id, p])),
      DEPARTMENT: new Map(departments.map((d) => [d.id, d])),
      ELECTION: new Map(elections.map((e) => [e.id, e])),
      MUNICIPAL: new Map(municipals.map((e) => [e.id, e])),
      WARD: new Map(wards.map((e) => [e.id, e])),
      CONTENT: new Map(contents.map((e) => [e.id, e])),
    };

    // Merge related resource into content
    const enriched = ratings.map((content) => {
      const resourceMap =
        resourceMaps[content.rateOnType as keyof typeof resourceMaps] ||
        resourceMaps[
          content.rateOnType.toUpperCase() as keyof typeof resourceMaps
        ];
      const resource = resourceMap?.get(+content.rateOnId) ?? null;

      return { ...content, resource };
    });

    return enriched;
  }

  async analytics(query: any) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentlyAdded = await this.prismaService.ratings.findMany({
      take: 100,
      where: {
        createdAt: { gt: oneWeekAgo },
      },
      orderBy: {
        createdAt: 'desc',
      },
      distinct: ['userId'],
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
      },
    });

    const unique = new Map();
    const nextRecentlyAdded = [];

    for (const rating of recentlyAdded) {
      const key = `${rating.rateOnType}_${rating.rateOnId}`;
      if (!unique.has(key)) {
        unique.set(key, true);
        nextRecentlyAdded.push(rating);
      }
      if (nextRecentlyAdded.length >= 10) break;
    }

    const nextContents = await this.getEnrichedContents(nextRecentlyAdded);
    console.log(nextContents);
    return {
      most_viewed: [],
      recentlyAdded: { items: nextContents.filter((item) => !!item.resource) },
      mostLiked: [],
    };
  }

  async getPopularityReport(resourceId: string, resourceType: CommentableType) {
    const rawResult = await this.prismaService.$queryRaw<
      Array<{
        week: Date;
        total_ratings: bigint;
        average_rating: number;
      }>
    >`
      SELECT
        DATE_SUB(DATE(createdAt), INTERVAL WEEKDAY(createdAt) DAY) AS week,
        COUNT(*) AS total_ratings,
        ROUND(AVG(value), 2) AS average_rating
      FROM ratings
      WHERE rateOnId = ${resourceId}
        AND rateOnType = ${resourceType}
      GROUP BY week
      ORDER BY week;
    `;

    const result = rawResult.map((row) => ({
      ...row,
      total_ratings: Number(row.total_ratings), // Convert BigInt to Number
    }));

    return result;
  }

  async findByHash(hash: string) {
    const rating = await this.prismaService.ratings.findFirst({
      where: {
        hash,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            profileImage: true,
          },
        },
      },
    });

    if (!rating) return null;
    const enrichedRatings = await this.getEnrichedContents([rating]);
    return enrichedRatings[0];
  }
}
