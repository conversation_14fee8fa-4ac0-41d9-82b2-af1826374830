import { Injectable } from '@nestjs/common';
import { RatingsService } from './ratings.service';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { PrismaService } from 'src/prisma.service';
import { sortBy } from 'lodash';
import { CommentableType, leaders, most_viewed, ratings } from '@prisma/client';
import { most_viewedService } from '../most-viewed/most-viewed.service';

@Injectable()
export class EntityReview {
  constructor(
    private prismaService: PrismaService,
    private ratingService: RatingsService,
    private most_viewedService: most_viewedService,
  ) {}

  async findAll(
    resources: {
      name: 'parties' | 'leaders' | 'governments' | any;
      model: CommentableType;
    },
    {
      uniqueIdentifier,
      searchFields,
      top,
      where,
      getWhere,
      sort_by,
      ...query
    }: PaginationSortSearchDto & {
      uniqueIdentifier?: string;
      where: any;
      getWhere?: (filterIds: number[], top: string) => object;
      searchFields?: string[];
    },
  ) {
    let filterIds = [];
    let topRated: { items: []; totalItems: number } = {
      items: [],
      totalItems: 0,
    };
    if (top === 'rates') {
      topRated = await this.ratingService.topRated(
        {
          rateOnType: resources.model,
        },
        { ...query },
        sort_by === 'asc'
          ? {
              maxAvg: 2,
              minAvg: 1,
            }
          : {
              maxAvg: 5,
              minAvg: 3,
            },
      );
      filterIds.push(...topRated.items.map((item: ratings) => +item.rateOnId));
    }
    if (top === 'views') {
      topRated = await this.most_viewedService.topViewed(
        {
          rateOnType: resources.model,
        },
        query,
      );
      filterIds.push(
        ...topRated.items.map((item: most_viewed) => +item.resourceId),
      );
    }
    // if (resources.name === "ratings") {
    //     filterIds = filterIds.map(id => id + "")
    // }

    const leaders = await this.prismaService.index(
      resources.name,
      {
        searchFields: searchFields ? searchFields : ['name', 'localName'],
        id: {
          in: top ? filterIds.map((item) => +item) : undefined,
        },
        ...where,
        ...(getWhere?.(filterIds, top) || {}),
      },
      //@ts-ignore
      {
        //@ts-ignore
        ...query,
        page: top ? 1 : query.page ? +query.page : 1,
      },
    );
    const resultMap = await this.ratingService.ratingsOfEntities(
      leaders.items.map((item) => item[uniqueIdentifier || 'id'] + ''),
      resources.model,
    );

    const viewsMap =
      topRated?.items?.reduce(
        (ex, cur: { resourceId: string }) => ({
          ...ex,
          [cur.resourceId]: cur,
        }),
        {},
      ) || {};

    const nextItems = leaders.items.map((item: leaders) => ({
      ...item,
      rating: resultMap[`${item[uniqueIdentifier || 'id']}-${resources.model}`],
      views: viewsMap[item[uniqueIdentifier || 'id']],
    }));

    return {
      items: sortBy(nextItems, [
        function (o) {
          if (top === 'views') return -o?.views?._sum?.views;
          return -o?.rating?.average || o.id;
        },
      ]),
      totalItems: top ? topRated.totalItems : leaders.totalItems,
    };
  }
}
