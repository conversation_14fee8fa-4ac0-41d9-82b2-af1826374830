import { Injectable } from '@nestjs/common';
import { CreateElectionDto } from './dto/create-election.dto';
import { UpdateElectionDto } from './dto/update-election.dto';
import { PrismaService } from 'src/prisma.service';
import { filter, groupBy, includes, sortBy, sumBy } from 'lodash';
import { RatingsService } from '../ratings/ratings.service';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { CandidacyTypeEnum, CommentableType } from '@prisma/client';
import { ILeaderElectionFilter } from 'src/interfaces/ILeaderElectionFilter';
import { EntityReview } from '../ratings/EntityReview';

@Injectable()
export class ElectionsService {
  getElectionById(electionId: any) {
    return this.prismaService.elections.findFirst({
      where: { id: +electionId },
    });
  }
  getCandidacyTypeById(candidacyTypeId: any) {
    throw new Error('Method not implemented.');
  }
  constructor(
    private prismaService: PrismaService,
    private ratingService: RatingsService,
    private entityReview: EntityReview,
  ) {}

  create(s: CreateElectionDto) {
    return 'This action adds a new election';
  }

  findAll() {
    return this.prismaService.elections.findMany({
      orderBy: {
        year: 'desc',
      },
    });
  }

  async getPartyWins({
    electionId,
    partyId,
  }: {
    electionId?: number;
    partyId?: number;
  }) {
    // 🟩 States where a party won
    const results = await this.prismaService.election_results.findMany({
      where: {
        electionId,
        partyId,
        // isElected: true,
      },
      select: {
        elCode: true,
        code: true,
        candidacyTypeId: true,
        area: true,
        isElected: true,
        voteCount: true,
        municipalId: true,
        electionId: true,
        wardId: true,
        districtId: true,
        partyId: true,
        stateId: true,
        states: { select: { name: true, localName: true } },
        municipals: { select: { name: true, localName: true } },
        ward: { select: { name: true, localName: true } },
        districts: { select: { name: true, localName: true } },
      },
    });

    const voteCounts = await this.prismaService.election_results.groupBy({
      by: ['districtId'],
      where: {
        electionId,
      },
      _sum: {
        voteCount: true,
      },
      orderBy: {
        _sum: {
          voteCount: 'desc',
        },
      },
    });
    const votesGroupByDistrict = groupBy(voteCounts, 'districtId');
    const winByState = groupBy(results, 'stateId');
    const winByDistrict = groupBy(results, 'districtId');
    const winByMunicipals = groupBy(results, 'municipalId');
    const ward = groupBy(results, 'wardId');
    delete winByDistrict['null'];
    delete winByState['null'];
    delete winByMunicipals['null'];
    delete ward['null'];

    const districtRecords = sortBy(
      Object.keys(winByDistrict).map((districtCode) => {
        const elections = sortBy(
          winByDistrict[districtCode],
          'voteCount',
        ).reverse();

        const district = winByDistrict[districtCode]?.[0].districts;
        const wins = elections.reduce((acc, cur) => {
          if (cur.isElected) return acc + 1;
          return acc;
        }, 0);
        const losses = elections.length - wins;
        const votesRecieved = sumBy(elections, 'voteCount');
        const allTotalVotes =
          votesGroupByDistrict[districtCode][0]._sum.voteCount;

        const voteReceivedPercentage = (votesRecieved / allTotalVotes) * 100;
        const voteLossPercentage =
          allTotalVotes - (votesRecieved / allTotalVotes) * 100;

        return {
          ...district,
          wins,
          voteReceivedPercentage,
          voteLossPercentage,
          allTotalVotes,
          topPerformer: elections[0],
          votesRecieved,
          losses: losses,
          winPercentage: (wins / elections.length) * 100,
          lossPercentage: (losses / elections.length) * 100,
          elections,
        };
      }),
      'votesRecieved',
    ).reverse();

    return {
      // state: (winByState),
      district: {
        top: districtRecords.slice(0, 10),
        bottom: districtRecords.reverse().slice(0, 10),
      },
      // municipals: (winByMunicipals),
      // ward: (ward),
    };
  }

  async getAllParties(
    electionId: number,
    options?: { onlyWin?: boolean; partyIds?: string[]; includeZero?: boolean },
  ) {
    // Step 1: Get vote total and running places
    const partyResults = await this.prismaService.election_results.groupBy({
      where: {
        electionId,
        partyId:
          options?.partyIds && options.partyIds.length > 0
            ? {
                in: options?.partyIds
                  ? options.partyIds.map(Number)
                  : undefined,
              }
            : undefined,
      },
      by: ['partyId'],
      _sum: {
        voteCount: true,
      },
      _count: {
        partyId: true,
      },
      orderBy: {
        _sum: {
          voteCount: 'desc',
        },
      },
    });

    const partyIds = partyResults.map((result) => result.partyId);

    // Step 2: Get actual win count (isElected: true)
    const winCounts = await this.prismaService.election_results.groupBy({
      where: {
        electionId,
        partyId:
          options?.partyIds && options.partyIds.length > 0
            ? {
                in: options?.partyIds
                  ? options.partyIds.map(Number)
                  : undefined,
              }
            : undefined,
        isElected: true,
      },
      by: ['partyId'],
      _count: {
        partyId: true,
      },
    });

    const winsMap = new Map<number, number>();
    winCounts.forEach((item) => {
      winsMap.set(item.partyId, item._count.partyId);
    });

    // Step 3: Get party metadata
    const partyDetails = await this.prismaService.parties.findMany({
      where: {
        id: { in: partyIds },
      },
    });

    // Step 4: Combine all data
    const result = partyResults
      .map((result) => {
        const partyDetail = partyDetails.find(
          (detail) => detail.id === result.partyId,
        );
        const wonPlaces = winsMap.get(result.partyId) || 0;

        return {
          partyId: result.partyId,
          voteCount: result._sum.voteCount || 0,
          runningPlaces: result._count.partyId,
          wonPlaces,
          party: partyDetail,
        };
      })
      .filter((item) => {
        if (options?.onlyWin && !options?.includeZero) {
          return item.wonPlaces > 0;
        }
        return true;
      });

    return result;
  }

  getAllCandidates(
    electionIdParam: number,
    {
      electedType,
      stateId,
      municipalId,
      districtId,
      partyId,
      wardId,
      electionId,
      candidacyTypeId,
      area,
      ...query
    }: PaginationSortSearchDto & ILeaderElectionFilter,
  ) {
    return this.prismaService.index(
      'election_results',
      {
        // @ts-expect-error
        partyId: !!partyId ? +partyId : undefined,
        stateId: stateId && +stateId,
        municipalId: municipalId && +municipalId,
        districtId: districtId && +districtId,
        wardId: wardId && +wardId,
        candidacyTypeId: candidacyTypeId && +candidacyTypeId,
        area: area && area,
        electionId:
          (!!electionIdParam ? +electionIdParam : undefined) ||
          (electionId ? +electionId : undefined),
        isElected:
          electedType === 'all' ? undefined : electedType === 'elected',
      },
      {
        include: {
          elections: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          parties: true,
          municipals: true,
          states: true,
          ward: true,
          districts: true,
          candidacyType: true,
        },
        ...query,
      },
    );
  }

  async getElectionByCodeAndId(
    id: number,
    code: string,
    candidacyTypeId: number,
  ) {
    const {
      candidacyType,
      elections,
      municipals,
      states,
      districts,
      ward,
      ...result
    } = await this.prismaService.election_results.findFirstOrThrow({
      where: {
        electionId: id,
        elCode: code,
        candidacyTypeId,
      },
      include: {
        ward: true,
        districts: true,
        municipals: true,
        states: true,
        elections: true,
        candidacyType: true,
      },
    });

    const previousElectionsSameArea =
      await this.prismaService.election_results.findMany({
        where: {
          isElected: true,
          candidacyTypeId,
          area: result.area ?? undefined,
          districtId: result.districtId ?? undefined,
          municipalId: result.municipalId ?? undefined,
          stateId: result.stateId ?? undefined,
          electionId: {
            notIn: [result.electionId],
          },
        },
        include: {
          leaders: true,
          parties: true,
          ward: true,
          districts: true,
          municipals: true,
          states: true,
          elections: true,
          candidacyType: true,
        },
        orderBy: {
          voteCount: 'desc',
        },
      });

    const allResults = await this.prismaService.election_results.findMany({
      where: {
        electionId: id,
        elCode: code,
        candidacyTypeId,
      },
      orderBy: {
        voteCount: 'desc',
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        parties: true,
      },
    });
    const ratings = await this.ratingService.ratingAnalysis({
      rateOnId: `${elections.id}-${allResults[0].elCode}-${allResults[0].candidacyTypeId}`,
      rateOnType: 'ELECTION_SUB',
    });
    const totalVote = allResults.reduce(
      (acc, cur) => (acc += cur.voteCount),
      0,
    );
    const sampleResult = allResults[0];

    let nearybyFilter: { [key: string]: any } = {
      where: {},
    };
    if (
      includes(
        [
          CandidacyTypeEnum.MAYOR,
          CandidacyTypeEnum.CHAIRPERSON,
          CandidacyTypeEnum.DEPUTY_MAYOR,
        ],
        candidacyType?.name,
      )
    ) {
      nearybyFilter = {
        // distinct: ['area'],
        where: {
          electionId: id,
          candidacyTypeId,
          districtId: sampleResult.districtId,
          isElected: true,
          municipalId: { not: sampleResult.municipalId },
        },
      };
    } else if (
      includes(
        [
          CandidacyTypeEnum.WARD_CHAIRPERSON,
          CandidacyTypeEnum.WARD_MEMBER,
          CandidacyTypeEnum.WARD_MEMBER_DALIT,
          CandidacyTypeEnum.WARD_MEMBER_FEMALE,
        ],
        candidacyType?.name,
      )
    ) {
      nearybyFilter = {
        where: {
          electionId: id,
          candidacyTypeId,
          stateId: sampleResult.stateId,
          municipalId: sampleResult.municipalId,
          isElected: true,
          wardId: { not: sampleResult.wardId },
        },
      };
    } else if (
      includes(
        [CandidacyTypeEnum.PROVINCIAL_LAW_MAKER, CandidacyTypeEnum.LAW_MAKER],
        candidacyType?.name,
      )
    ) {
      nearybyFilter = {
        distinct: ['area'],
        where: {
          electionId: id,
          candidacyTypeId,
          stateId: sampleResult.stateId,
          isElected: true,
          districtId: sampleResult.districtId,
          area: { not: sampleResult.area },
        },
      };
    }

    let relatedElections = [];
    if (
      includes(
        [
          CandidacyTypeEnum.MAYOR,
          CandidacyTypeEnum.CHAIRPERSON,
          CandidacyTypeEnum.WARD_CHAIRPERSON,
          CandidacyTypeEnum.WARD_MEMBER,
          CandidacyTypeEnum.WARD_MEMBER_DALIT,
          CandidacyTypeEnum.WARD_MEMBER_FEMALE,
        ],
        candidacyType?.name,
      )
    ) {
      relatedElections = await this.prismaService.election_results.findMany({
        where: {
          electionId: id,
          stateId: sampleResult.stateId,
          municipalId: sampleResult.municipalId,
          wardId: sampleResult.wardId,
          isElected: true,
          candidacyTypeId: {
            not: sampleResult.candidacyTypeId,
          },
        },
        distinct: ['candidacyTypeId'],
        include: {
          ward: true,
          municipals: true,
          candidacyType: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          districts: true,
          parties: true,
        },
      });
    } else if (
      includes(
        [CandidacyTypeEnum.PROVINCIAL_LAW_MAKER, CandidacyTypeEnum.LAW_MAKER],
        candidacyType?.name,
      )
    ) {
      relatedElections = await this.prismaService.election_results.findMany({
        where: {
          electionId: id,
          stateId: sampleResult.stateId,
          isElected: true,
        },
        distinct: ['districtId'],
        include: {
          ward: true,
          candidacyType: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          districts: true,
          parties: true,
        },
      });
    }

    const { where: nextFilterWhere, ...nextFilter } = nearybyFilter;
    const nearbyResults = await this.prismaService.election_results.findMany({
      where: {
        ...nextFilterWhere,
      },
      orderBy: {
        voteCount: 'desc',
      },
      include: {
        municipals: true,
        candidacyType: true,
        ward: true,
        districts: true,
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        parties: true,
      },
      take: 120,
      ...nextFilter,
    });

    const previousElections =
      await this.prismaService.election_results.findMany({
        select: {
          id: true,
          electionId: true,
          elCode: true,
          leaderId: true,
          code: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          candidacyType: true,
          voteCount: true,
          isElected: true,
          elections: true,
          ward: true,
          districts: true,
          municipals: true,
          states: true,
          area: true,
        },
        where: {
          leaderId: {
            in: allResults.map((item) => item.leaderId),
          },
          electionId: {
            notIn: [elections.id],
          },
        },
      });
    const mapPreviousResultByLeaderId = new Map();
    previousElections.forEach((result) => {
      if (!mapPreviousResultByLeaderId.has(result.leaderId)) {
        mapPreviousResultByLeaderId.set(result.leaderId, []);
      }

      mapPreviousResultByLeaderId.set(result.leaderId, [
        ...mapPreviousResultByLeaderId.get(result.leaderId),
        result,
      ]);
    });

    allResults.forEach((item) => {
      //@ts-expect-error
      item.previousElections = mapPreviousResultByLeaderId.get(item.leaderId);
    });

    return {
      previousElectionsSameArea,
      relatedElections,
      nearbyResults,
      stats: {
        totalVote,
        percentages: allResults.reduce((acc, cur) => {
          const per = (cur.voteCount / totalVote) * 100;
          return {
            ...acc,
            [cur.leaderId]: +per.toFixed(2),
          };
        }, {}),
      },
      ward,
      elections,
      municipals,
      states,
      districts,
      allResults,
      candidacyType,
      ratings,
    };
  }
  async findOne(id: number, filter?: { partyId?: number }) {
    const election = await this.prismaService.elections.findFirst({
      where: {
        id,
      },
    });
    const mostVotedLeaders = await this.prismaService.election_results.findMany(
      {
        where: {
          electionId: id,
          partyId: filter?.partyId ? +filter.partyId : undefined,
        },
        include: {
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          parties: true,
          municipals: true,
          states: true,
          ward: true,
          districts: true,
          candidacyType: true,
        },
        orderBy: {
          voteCount: 'desc',
        },
        take: 10,
      },
    );
    const topPartiesWithDetails = await this.getTopParties(id);
    // Output the top parties with vote count and party names
    const mostWin = await this.getMostWin(id);

    return {
      mostWin,
      topPartiesWithDetails,
      mostVotedLeaders,
      election,
    };
  }

  update(id: number, updateElectionDto: UpdateElectionDto) {
    return `This action updates a #${id} election`;
  }

  remove(id: number) {
    return `This action removes a #${id} election`;
  }

  async getTopParties(electionId: number) {
    const topParties = await this.prismaService.election_results.groupBy({
      where: {
        electionId,
      },
      by: ['partyId'],
      _sum: {
        voteCount: true,
      },
      orderBy: {
        _sum: {
          voteCount: 'desc', // Sort by sum of vote count in descending order
        },
      },
      take: 10, // Limit to top 10 parties
    });

    // Fetch additional details of the top parties (e.g., party names)
    const topPartyIds = topParties.map((party) => party.partyId);
    const topPartyDetails = await this.prismaService.parties.findMany({
      where: {
        id: { in: topPartyIds },
      },
    });

    // Combine vote count and party details
    return topParties.map((party) => {
      const partyDetails = topPartyDetails.find(
        (detail) => detail.id === party.partyId,
      );
      return {
        partyId: party.partyId,
        value: party._sum.voteCount,
        party: partyDetails || 'Unknown Party', // Use a placeholder if party name not found
      };
    });
  }

  async getMostWin(electionId: number) {
    const topParties = await this.prismaService.election_results.groupBy({
      where: {
        isElected: true,
        electionId,
      },
      by: ['partyId'],
      _count: {
        remarks: true,
      },
      orderBy: {
        _count: {
          remarks: 'desc', // Sort by sum of vote count in descending order
        },
      },
      take: 10, // Limit to top 10 parties
    });

    // Fetch additional details of the top parties (e.g., party names)
    const topPartyIds = topParties.map((party) => party.partyId);
    const topPartyDetails = await this.prismaService.parties.findMany({
      where: {
        id: { in: topPartyIds },
      },
    });

    // Combine vote count and party details
    return topParties.map((party) => {
      const partyDetails = topPartyDetails.find(
        (detail) => detail.id === party.partyId,
      );
      return {
        partyId: party.partyId,
        value: party._count.remarks,
        party: partyDetails || 'Unknown Party', // Use a placeholder if party name not found
      };
    });
  }

  private candidacyTypeCache = new Map<CandidacyTypeEnum, any>();

  async loadCandidacyTypes() {
    const candidacyTypes = await this.prismaService.candidacy_types.findMany();
    candidacyTypes.forEach((type) => {
      this.candidacyTypeCache.set(type.name, type.id);
    });
  }

  async getCandidacyTypeByName(name: CandidacyTypeEnum) {
    if (!this.candidacyTypeCache.size) {
      await this.loadCandidacyTypes();
    }
    return this.candidacyTypeCache.get(name);
  }
  async getCandidacyTypeByNames(names: CandidacyTypeEnum[]): Promise<number[]> {
    if (!this.candidacyTypeCache.size) {
      await this.loadCandidacyTypes();
    }
    return names
      .map((item) => this.candidacyTypeCache.get(item))
      .filter(Boolean);
  }

  async getIdsOfWardCandidacyTypes() {
    return this.getCandidacyTypeByNames([
      CandidacyTypeEnum.WARD_CHAIRPERSON,
      CandidacyTypeEnum.DEPUTY_CHAIRPERSON,
      CandidacyTypeEnum.WARD_MEMBER,
      CandidacyTypeEnum.WARD_MEMBER_DALIT,
      CandidacyTypeEnum.WARD_MEMBER_FEMALE,
      CandidacyTypeEnum.CHAIRPERSON,
    ]);
  }

  async getIdsOfProvincialCandidacyTypes() {
    return this.getCandidacyTypeByNames([
      CandidacyTypeEnum.PROVINCIAL_LAW_MAKER,
    ]);
  }

  async getIdsOfCentreCandidacyTypes() {
    return this.getCandidacyTypeByNames([CandidacyTypeEnum.LAW_MAKER]);
  }
  async getIdsOfMuncipalCandidacyTypes() {
    return this.getCandidacyTypeByNames([
      CandidacyTypeEnum.MAYOR,
      CandidacyTypeEnum.DEPUTY_MAYOR,
    ]);
  }

  async index(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = entityId ? {} : {};

    return this.entityReview.findAll(
      {
        model: CommentableType.ELECTION,
        name: 'elections',
      },
      {
        searchFields: ['name'],
        ...nextQuery,
        //@ts-expect-error
        include: {},
        ...additionalWhere,
      },
    );
  }

  async analytics(query: { partyId?: number }) {
    let limit = 10;
    if (query.partyId) {
      limit = 1000;
    }
    let most_viewed = await this.index({
      top: 'views',
      limit: limit,
    });
    let mostLiked = await this.index({
      top: 'rates',
      limit: limit,
    });

    let lessLiked = await this.index({
      top: 'rates',
      limit: limit,
      sort_by: 'asc',
    });

    return {
      most_viewed,
      // recentlyAdded: { items: recentlyAdded },
      mostLiked,
      lessLiked,
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async subIndex(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = {};

    const records = await this.entityReview.findAll(
      {
        model: CommentableType.ELECTION_SUB,
        name: 'election_results',
      },
      {
        ...nextQuery,
        //@ts-expect-error
        include: {
          candidacyType: true,
          elections: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          parties: true,
          municipals: true,
          ward: true,
          districts: true,
          states: true,
        },

        getWhere: (filterIds, top) => {
          return {
            id: undefined,
            elCode: top
              ? {
                  in: filterIds.map((item) => {
                    return item;
                  }),
                }
              : undefined,
          };
        },
        distinct: ['elCode'],
        uniqueIdentifier: 'elCode',
        ...additionalWhere,
      },
    );

    return records;
  }

  async subAnalytics(query: { partyId?: number }) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let recentlyAdded = await this.prismaService.election_results.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        createdAt: { gt: oneWeekAgo },
      },
      distinct: ['electionId'],
    });
    let limit = 10;
    if (query.partyId) {
      limit = 1000;
    }
    let most_viewed = await this.subIndex({
      top: 'views',
      limit: limit,
    });
    let mostLiked = await this.subIndex({
      top: 'rates',
      limit: limit,
    });

    let lessLiked = await this.subIndex({
      top: 'rates',
      limit: limit,
      sort_by: 'asc',
    });

    return {
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
      lessLiked,
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  getAllSubElections(query: PaginationSortSearchDto = {}) {
    const electionResults = this.prismaService.index(
      'election_results',
      {},
      {
        //@ts-expect-error
        include: {},
        distinct: ['elCode'],
        ...query,
      },
    );
    return electionResults;
  }
}
