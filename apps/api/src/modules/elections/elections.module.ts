import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ElectionsService } from './elections.service';
import { ElectionsController } from './elections.controller';
import { PrismaService } from 'src/prisma.service';
import { RatingsService } from '../ratings/ratings.service';
import { EntityReview } from '../ratings/EntityReview';
import { most_viewedService } from '../most-viewed/most-viewed.service';
import { ElectionsAnalyticsService } from './election-analytics';
import { ElectionsOverviewService } from './election-overview';

@Module({
  controllers: [ElectionsController],
  providers: [
    ElectionsService,
    PrismaService,
    RatingsService,
    EntityReview,
    most_viewedService,
    ElectionsAnalyticsService,
    ElectionsOverviewService,
  ],
})
export class ElectionsModule {}
