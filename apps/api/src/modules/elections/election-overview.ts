import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { countBy, groupBy, sumBy } from 'lodash';
import { PrismaService } from 'nestjs-prisma';
import { ElectionsService } from './elections.service';
import { Cache } from 'cache-manager';

type LeaderTrend = {
  electionYear: number;
  voteCount: number;
  voteDelta: number | null;
  voteTrend: 'rise' | 'fall' | 'same' | null;
  seatWon: 0 | 1;
  partyName: string;
  partyId: number;
  partyLogo?: string;
  districtName: string;
  constituencyNumber: string;
};

type LeaderTrendData = {
  ecCandidateID: string;
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  img?: string;
  trends: LeaderTrend[];
};

@Injectable()
export class ElectionsOverviewService {
  constructor(
    private prisma: PrismaService,
    private electionsService: ElectionsService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  async getTopLeadersAcrossElections(
    leaderIds: string[] = [],
  ): Promise<LeaderTrendData[]> {
    const prisma = this.prisma;
    const elections = await prisma.elections.findMany({
      where: { electionType: 'GENERAL_PARLIAMENT_ELECTION' },
      orderBy: { year: 'asc' },
      select: { id: true, year: true },
    });

    const topLeaderIds = new Set<number>(leaderIds.map(Number));

    const leaderMap = new Map<number, LeaderTrendData>();

    for (const election of elections) {
      const results = await prisma.election_results.findMany({
        where: {
          electionId: election.id,
          leaderId:
            leaderIds && leaderIds.length > 0
              ? { in: Array.from(topLeaderIds) }
              : undefined,
        },
        include: {
          leaders: true,
          parties: true,
          districts: true,
        },
        orderBy: { voteCount: 'desc' },
        take: 5,
      });

      const grouped = new Map<number, typeof results>();

      for (const r of results) {
        if (!r.leaderId) continue;
        if (!grouped.has(r.leaderId)) grouped.set(r.leaderId, []);
        grouped.get(r.leaderId)!.push(r);
      }

      const sortedLeaders = [...grouped.entries()]
        .map(([leaderId, contests]) => ({
          leaderId,
          contests,
          voteCount: contests.reduce((sum, c) => sum + (c.voteCount ?? 0), 0),
        }))
        .sort((a, b) => b.voteCount - a.voteCount)
        .slice(0, 5);

      for (const { leaderId, contests, voteCount } of sortedLeaders) {
        topLeaderIds.add(leaderId);

        const best = contests.sort(
          (a, b) => (b.voteCount ?? 0) - (a.voteCount ?? 0),
        )[0];
        const won = contests.some((c) => c.isElected);
        const isIndependent = best.parties?.name
          ?.toLowerCase()
          .includes('independent');

        if (!leaderMap.has(leaderId)) {
          leaderMap.set(leaderId, {
            leaderId,
            leaderName: best.leaders.name,
            leaderLocalName: best.leaders.localName,
            ecCandidateID: best.leaders.ecCandidateID,
            img: best.leaders.img ?? undefined,
            trends: [],
          });
        }

        leaderMap.get(leaderId)!.trends.push({
          electionYear: election.year.getFullYear(),
          voteCount,
          voteDelta: null,
          voteTrend: null,
          seatWon: won ? 1 : 0,
          partyName: isIndependent
            ? 'Independent'
            : best.parties?.name ?? 'Unknown',
          partyId: isIndependent ? 0 : best.parties?.id ?? 0,
          partyLogo: isIndependent
            ? '/logos/independent.png'
            : best.parties?.logo,
          districtName: best.districts?.name ?? 'N/A',
          constituencyNumber: best.area ?? 'N/A',
        });
      }
    }

    // Fetch all additional participations of top leaders
    const allResults = await prisma.election_results.findMany({
      where: {
        leaderId: { in: Array.from(topLeaderIds) },
      },
      include: {
        elections: true,
        leaders: true,
        parties: true,
        districts: true,
      },
    });

    for (const r of allResults) {
      const year = r.elections.year.getFullYear();
      const leader = leaderMap.get(r.leaderId);
      if (!leader) continue;

      const alreadyExists = leader.trends.some((t) => t.electionYear === year);
      if (alreadyExists) continue;

      const isIndependent = r.parties?.name
        ?.toLowerCase()
        .includes('independent');

      leader.trends.push({
        electionYear: year,
        voteCount: r.voteCount,
        voteDelta: null,
        voteTrend: null,
        seatWon: r.isElected ? 1 : 0,
        partyName: isIndependent ? 'Independent' : r.parties?.name ?? 'Unknown',
        partyId: isIndependent ? 0 : r.partyId ?? 0,
        partyLogo: isIndependent ? '/logos/independent.png' : r.parties?.logo,
        districtName: r.districts?.name ?? 'N/A',
        constituencyNumber: r.area ?? 'N/A',
      });
    }

    // Finalize trends: sort + compute deltas
    for (const leader of leaderMap.values()) {
      leader.trends.sort((a, b) => a.electionYear - b.electionYear);
      for (let i = 1; i < leader.trends.length; i++) {
        const prev = leader.trends[i - 1];
        const curr = leader.trends[i];
        curr.voteDelta = curr.voteCount - prev.voteCount;
        curr.voteTrend =
          curr.voteDelta > 0 ? 'rise' : curr.voteDelta < 0 ? 'fall' : 'same';
      }
    }

    return Array.from(leaderMap.values());
  }

  async getPartyTrendsOverYears(partyIds: string[] = []): Promise<any[]> {
    const elections = await this.prisma.elections.findMany({
      orderBy: { year: 'asc' },
      select: { id: true, year: true },
    });

    const allPartyStats: Record<number, any[]> = {}; // electionId -> stats
    const allPartyIds = new Set<number>();
    const includeIndependentKey = 0;

    for (const election of elections) {
      const partyStats = await this.electionsService.getAllParties(
        election.id,
        {
          onlyWin: false,
          includeZero: true,
          partyIds,
        },
      );

      const normalizedStats: Record<string | number, any> = {};

      for (const stat of partyStats) {
        const name = stat.party?.name?.toLowerCase().trim() || '';
        const isIndependent = name.startsWith('independent');

        const key = isIndependent ? includeIndependentKey : stat.partyId;
        if (!normalizedStats[key]) {
          normalizedStats[key] = {
            partyId: isIndependent ? includeIndependentKey : stat.partyId,
            name: isIndependent ? 'Independent' : stat.party?.name,
            localName: isIndependent ? 'स्वतन्त्र' : stat.party?.localName,
            voteCount: 0,
            seatCount: 0,
            color: isIndependent ? '#999999' : stat.party?.partyColorCode,
            logo: isIndependent ? '/logos/independent.png' : stat.party?.logo,
          };
        }

        normalizedStats[key].voteCount += stat.voteCount ?? 0;
        normalizedStats[key].seatCount += stat.wonPlaces ?? 0;
      }

      allPartyStats[election.id] = Object.values(normalizedStats);
      for (const stat of Object.values(normalizedStats)) {
        allPartyIds.add(stat.partyId);
      }
    }

    const results = [];

    for (const election of elections) {
      const partyStats = allPartyStats[election.id];
      const totalVotes = sumBy(partyStats, (p) => p.voteCount ?? 0);
      const totalSeats = sumBy(partyStats, (p) => p.seatCount ?? 0);

      const statMap = new Map(partyStats.map((p) => [p.partyId, p]));

      for (const partyId of allPartyIds) {
        const stat = statMap.get(partyId);
        if (!stat) continue;

        results.push({
          electionYear: election.year.getFullYear(),
          partyId,
          name: stat.name,
          localName: stat.localName,
          voteCount: stat.voteCount,
          votePercent: totalVotes ? (stat.voteCount / totalVotes) * 100 : 0,
          seatCount: stat.seatCount,
          seatPercent: totalSeats ? (stat.seatCount / totalSeats) * 100 : 0,
          logo: stat.logo,
          color: stat.color,
        });
      }
    }

    return results;
  }

  async getCandidacyTypes() {
    return this.prisma.candidacy_types.findMany();
  }
  async getAllResults(filter?: any) {
    const cacheKey = 'all-election-results';
    const cachedData = await this.cacheManager.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    const allResults = await this.prisma.election_results.findMany({
      where: filter,
      include: {
        parties: true,
        leaders: true,
        elections: true,
        districts: true,
      },
    });
    await this.cacheManager.set(cacheKey, allResults, 60 * 60 * 24);
    return allResults;
  }

  async getElectionsOverview() {
    const elections = await this.prisma.elections.findMany({
      orderBy: { year: 'asc' },
      where: { electionType: 'GENERAL_PARLIAMENT_ELECTION' },
      select: {
        id: true,
        name: true,
        year: true,
        localName: true,
        electionType: true,
      },
    });

    const electionMap = new Map<number, Date>(
      elections.map((e) => [e.id, e.year]),
    );

    const allResults = (await this.getAllResults({
      electionId: {
        in: Array.from(electionMap.keys()),
      },
    })) as any[];

    const partyStats = this.getPartyTrends(allResults);
    const leaderStats = await this.getTopLeadersAcrossElections();
    const consistentWinners = this.getConsistentLeaders(allResults);
    const newPartyLaunches = this.getNewParty(allResults);
    const fadingParties = this.getFadingParties(allResults);
    const leaderSwitches = this.getLeaderSwitching(allResults);
    const biggestMargins = this.getBiggestWins(allResults);
    const closestContests = this.getClosestContests(allResults);
    const voteSeatTrends = this.getVoteSeatTrends(allResults);
    const partyTrendsSeries = await this.getPartyTrendsOverYears();
    const repeatLosers = this.getRepeatLosers(allResults);
    const bigDebutWinners = this.getBigDebutWinners(allResults);
    const consistentParties = this.getConsistentParties(allResults);
    const strugglingParties = this.getStrugglingParties(allResults);
    const topWinners = this.getTopWinners(allResults);
    const topPerformingLeaders = this.getTopPerformingLeaders(allResults);
    const topLeadersPerElection = this.getTopLeadersPerElection(allResults);
    const topPartiesPerElection = this.getTopPartiesPerElection(allResults);

    return {
      electionsList: elections,
      partyTrends: partyStats,
      leaderTrends: leaderStats,
      consistentWinners,
      newPartyLaunches,
      fadingParties,
      leaderSwitches,
      biggestWinMargins: biggestMargins,
      closeContests: closestContests,
      voteSeatTrends,
      partyTrendsSeries,
      repeatLosers,
      consistentParties,
      bigDebutWinners,
      strugglingParties,
      topWinners,
      topPerformingLeaders,
      topLeadersPerElection,
      topPartiesPerElection,
    };
  }

  public getPartyTrends(results: any[]) {
    const grouped = groupBy(results, (r) => {
      const partyName = r.parties?.name?.toLowerCase()?.trim();
      const isIndependent =
        partyName === 'independent' || partyName?.startsWith('independent');
      const partyKey = isIndependent ? 'independent' : r.partyId;
      return `${r.electionId}-${partyKey}`;
    });

    // Create aggregated trend entries
    const trends = Object.entries(grouped).map(([key, records]) => {
      const sample = records[0];
      const partyName = sample.parties?.name
        ?.toLowerCase()
        ?.startsWith('independent')
        ? 'Independent'
        : sample.parties?.name;

      const partyId = partyName === 'Independent' ? -1 : sample.partyId;

      return {
        electionId: sample.electionId,
        electionYear: new Date(sample.elections?.year),
        partyId,
        partyName,
        color: sample.parties?.partyColorCode,
        localName: sample.parties?.localName,
        logo: sample.parties?.logo,
        electionSymbol: sample.parties?.electionSymbol,
        electionType: sample.elections?.electionType,
        votes: sumBy(records, (r) => r.voteCount ?? 0),
        seats: records.filter((r) => r.isElected).length,
      };
    });

    // Sort by (votes + seats) as a rough performance metric, then slice top 50
    return trends
      .sort((a, b) => {
        const valA = a.votes + a.seats * 100000; // seats weigh more
        const valB = b.votes + b.seats * 100000;
        return valB - valA;
      })
      .slice(0, 50);
  }

  private getLeaderTrends(results: any[]) {
    const grouped = groupBy(
      results.filter((r) => r.leaderId),
      (r) => r.leaderId,
    );
    return Object.entries(grouped)
      .map(([leaderId, records]) => {
        return {
          leaderId: Number(leaderId),
          name: records[0].leaders?.name,
          results: records.map((r) => ({
            year: new Date(r.elections?.year).getFullYear(),
            won: r.isElected,
          })),
        };
      })
      .filter((r) => r.results.length >= 3)
      .slice(0, 100);
  }

  private getConsistentLeaders(results: any[]) {
    const allCandidates = results.filter((r) => r.leaderId);
    const groupedByLeader = groupBy(allCandidates, (r) => r.leaderId);

    return Object.entries(groupedByLeader)
      .map(([leaderId, contests]) => {
        const wins = contests.filter((r) => r.isElected).length;
        const total = contests.length;
        const losses = total - wins;
        const winRatio = total > 0 ? (wins / total) * 100 : 0;

        return {
          leaderId: Number(leaderId),
          name: contests[0].leaders?.name,
          leader: contests[0].leaders,
          winCount: wins,
          lossCount: losses,
          totalContests: total,
          winRatio: Number(winRatio.toFixed(1)),
        };
      })
      .filter((r) => r.winCount >= 3)
      .sort((a, b) => b.winRatio - a.winRatio || b.winCount - a.winCount);
  }

  private getNewParty(results: any[]) {
    const partyGroups = groupBy(results, (r) => r.partyId);

    // Get each party's first election year
    const partyMeta = Object.entries(partyGroups).map(([partyId, entries]) => {
      const years = entries.map((r) =>
        new Date(r.elections?.year).getFullYear(),
      );
      const firstYear = Math.min(...years);
      const firstYearEntries = entries.filter(
        (r) => new Date(r.elections?.year).getFullYear() === firstYear,
      );

      return {
        electionName: entries[0].elections?.name,
        partyId: Number(partyId),
        name: entries[0].parties?.name,
        localName: entries[0].parties?.localName,
        logo: entries[0].parties?.logo,
        electionSymbol: entries[0].parties?.electionSymbol,
        color: entries[0].parties?.partyColorCode,
        firstYear,
        seats: firstYearEntries.filter((r) => r.isElected).length,
        votes: sumBy(firstYearEntries, (r) => r.voteCount ?? 0),
      };
    });

    // Group by firstYear
    const groupedByFirstYear = groupBy(partyMeta, (p) => p.firstYear);

    // For each year, get top 3 debuting parties based on seats (or votes)
    const topNewParties = Object.entries(groupedByFirstYear).flatMap(
      ([year, parties]) =>
        parties
          .sort((a, b) => b.seats - a.seats || b.votes - a.votes)
          .slice(0, 8),
    );

    return topNewParties;
  }

  private getFadingParties(results: any[]) {
    const grouped = groupBy(results, (r) => r.partyId);

    return Object.entries(grouped)
      .map(([partyId, res]) => {
        const elections = res
          .filter((r) => r.elections?.year)
          .map((r) => ({
            year: new Date(r.elections.year).getFullYear(),
            isElected: r.isElected,
            voteCount: r.voteCount ?? 0,
          }));

        const lastYear = Math.max(...elections.map((e) => e.year));
        const seatTrend = countBy(
          elections.filter((e) => e.isElected),
          (e) => e.year,
        );
        const voteTrendMap = new Map<number, number>();

        for (const e of elections) {
          voteTrendMap.set(
            e.year,
            (voteTrendMap.get(e.year) || 0) + e.voteCount,
          );
        }

        return {
          partyId: Number(partyId),
          name: res[0].parties?.name ?? 'Unknown Party',
          localName: res[0].parties?.localName ?? '',
          logo: res[0].parties?.logo ?? null,
          color: res[0].parties?.partyColorCode ?? '#ccc',
          lastYear,
          recentSeats: res.filter((r) => r.isElected).length,
          seatTrend: Object.entries(seatTrend).map(([year, count]) => ({
            year: Number(year),
            seats: count,
          })),
          voteTrend: Array.from(voteTrendMap.entries()).map(
            ([year, votes]) => ({
              year,
              votes,
            }),
          ),
        };
      })
      .filter((p) => p.lastYear < 2022 && p.recentSeats > 0)
      .sort((a, b) => b.lastYear - a.lastYear);
  }

  private getLeaderSwitching(results: any[]) {
    const leadersWithParties = results.filter((r) => r.leaderId && r.partyId);
    const grouped = groupBy(leadersWithParties, (r) => r.leaderId);
    return Object.entries(grouped)
      .map(([leaderId, res]) => {
        const uniqueParties = new Set(res.map((r) => r.partyId));
        return {
          leaderId: Number(leaderId),
          name: res[0].leaders?.name,
          switches: uniqueParties.size - 1,
          leader: res[0].leaders,
          parties: Array.from(uniqueParties).map((p) => {
            const party = res.find((r) => r.partyId === p);
            return {
              partyId: p,
              name: party.parties?.name,
              localName: party.parties?.localName,
              logo: party.parties?.logo,
              color: party.parties?.partyColorCode,
            };
          }),
        };
      })
      .filter((r) => r.switches > 0)
      .sort((a, b) => b.switches - a.switches)
      .slice(0, 20);
  }

  private getBiggestWins(results: any[]) {
    const grouped = groupBy(results, (r) => r.elCode); // elCode = unique constituency contest
    const margins: any[] = [];

    for (const group of Object.values(grouped)) {
      const sorted = group.sort(
        (a, b) => (b.voteCount ?? 0) - (a.voteCount ?? 0),
      );

      if (sorted.length >= 2) {
        const margin = (sorted[0].voteCount ?? 0) - (sorted[1].voteCount ?? 0);

        margins.push({
          leader: sorted[0].leaders,
          year: new Date(sorted[0].elections?.year).getFullYear(),
          district: sorted[0].districts?.name,
          constituency: sorted[0].constituencyNumber ?? null,
          elCode: sorted[0].elCode,
          margin,
          winner: sorted[0].leaders?.name,
          runnerUp: sorted[1].leaders?.name,
          winnerParty: sorted[0].parties?.name,
          runnerUpParty: sorted[1].parties?.name,
          winnerVotes: sorted[0].voteCount,
          runnerUpVotes: sorted[1].voteCount,
        });
      }
    }

    return margins.sort((a, b) => b.margin - a.margin).slice(0, 10);
  }

  private getClosestContests(results: any[]) {
    // Assuming `elCode` is a unique identifier for each constituency contest
    const grouped = groupBy(results, (r) => r.elCode);

    const margins: any[] = [];

    for (const group of Object.values(grouped)) {
      const sorted = group.sort(
        (a, b) => (b.voteCount ?? 0) - (a.voteCount ?? 0),
      );

      if (sorted.length >= 2) {
        const margin = (sorted[0].voteCount ?? 0) - (sorted[1].voteCount ?? 0);

        margins.push({
          leader: sorted[0].leaders,
          year: new Date(sorted[0].elections?.year).getFullYear(),
          district: sorted[0].districts?.name,
          constituency: sorted[0].constituencyNumber ?? null,
          elCode: sorted[0].elCode,
          margin,
          winner: sorted[0].leaders?.name,
          runnerUp: sorted[1].leaders?.name,
          winnerParty: sorted[0].parties?.name,
          runnerUpParty: sorted[1].parties?.name,
          winnerVotes: sorted[0].voteCount,
          runnerUpVotes: sorted[1].voteCount,
        });
      }
    }

    return margins.sort((a, b) => a.margin - b.margin).slice(0, 10);
  }

  private getVoteSeatTrends(results: any[]) {
    const grouped = groupBy(results, (r) => `${r.partyId}-${r.electionId}`);
    return Object.entries(grouped).map(([key, records]) => {
      const sample = records[0];
      return {
        partyId: sample.partyId,
        partyName: sample.parties?.name,
        year: new Date(sample.elections?.year).getFullYear(),
        votes: sumBy(records, (r) => r.voteCount ?? 0),
        seats: records.filter((r) => r.isElected).length,
      };
    });
  }

  private getStrugglingParties(results: any[]) {
    const grouped = groupBy(results, (r) => r.partyId);

    return Object.entries(grouped)
      .map(([partyId, res]) => {
        const elections = new Set(
          res.map((r) => new Date(r.elections?.year).getFullYear()),
        );
        const wins = res.filter((r) => r.isElected).length;

        return {
          partyId: Number(partyId),
          name: res[0].parties?.name,
          elections: elections.size,
          wins,
          ratio: (wins / elections.size) * 100,
          party: res[0].parties,
        };
      })
      .filter((r) => r.elections >= 3 && r.ratio < 33)
      .sort((a, b) => a.ratio - b.ratio);
  }

  private getConsistentParties(results: any[]) {
    const grouped = groupBy(results, (r) => r.partyId);

    return Object.entries(grouped)
      .map(([partyId, res]) => {
        const years = new Set(
          res.map((r) => new Date(r.elections?.year).getFullYear()),
        );
        const winYears = new Set(
          res
            .filter((r) => r.isElected)
            .map((r) => new Date(r.elections?.year).getFullYear()),
        );

        return {
          partyId: Number(partyId),
          name: res[0].parties?.name,
          winYears: Array.from(winYears),
          totalYears: Array.from(years),
          consistencyRatio: (winYears.size / years.size) * 100,
          party: res[0].parties,
        };
      })
      .filter((r) => r.totalYears.length >= 3)
      .sort((a, b) => b.consistencyRatio - a.consistencyRatio);
  }

  private getBigDebutWinners(results: any[]) {
    const grouped = groupBy(
      results.filter((r) => r.leaderId && r.isElected),
      (r) => r.leaderId,
    );

    return Object.entries(grouped)
      .map(([leaderId, res]) => {
        const years = [
          ...new Set(res.map((r) => new Date(r.elections.year).getFullYear())),
        ];
        const debutYear = Math.min(...years);
        const debutEntries = res.filter(
          (r) => new Date(r.elections.year).getFullYear() === debutYear,
        );
        const totalVotes = sumBy(debutEntries, (r) => r.voteCount ?? 0);

        return {
          leaderId: Number(leaderId),
          name: res[0].leaders?.name,
          leader: res[0].leaders,
          debutYear,
          debutVotes: totalVotes,
        };
      })
      .filter((r) => r.debutVotes >= 50000) // tune this threshold
      .sort((a, b) => b.debutVotes - a.debutVotes);
  }

  private getRepeatLosers(results: any[]) {
    const grouped = groupBy(
      results.filter((r) => r.leaderId),
      (r) => r.leaderId,
    );

    return Object.entries(grouped)
      .map(([leaderId, res]) => {
        const losses = res.filter((r) => !r.isElected).length;
        const total = res.length;
        const wins = total - losses;
        return {
          leaderId: Number(leaderId),
          name: res[0].leaders?.name,
          leader: res[0].leaders,
          losses,
          total,
          wins,
        };
      })
      .filter((r) => r.losses >= 3 && r.total > r.losses)
      .sort((a, b) => b.losses - a.losses);
  }

  private async getTopWinners(results: any[]) {
    const result = await this.getConsistentLeaders(results);
    return result
      .filter((l) => l.winCount >= 5)
      .sort((a, b) => b.winCount - a.winCount);
  }

  private getTopPerformingLeaders(results: any[]) {
    const grouped = groupBy(
      results.filter((r) => r.leaderId),
      (r) => r.leaderId,
    );

    const leaderStats = Object.entries(grouped).map(([leaderId, contests]) => {
      const totalWins = contests.filter((r) => r.isElected).length;
      const totalContests = contests.length;
      const winRatio =
        totalContests > 0 ? (totalWins / totalContests) * 100 : 0;

      const debutYear = Math.min(
        ...contests.map((r) => new Date(r.elections?.year).getFullYear()),
      );

      return {
        leaderId: Number(leaderId),
        name: contests[0].leaders?.name,
        debutYear,
        totalWins,
        totalContests,
        winRatio,
        leader: contests[0].leaders,
      };
    });

    // Filter top performers: win ratio >= 50 and at least 2 contests
    return leaderStats
      .filter((l) => l.totalContests >= 2 && l.winRatio >= 50)
      .sort((a, b) => b.winRatio - a.winRatio || b.totalWins - a.totalWins)
      .slice(0, 20); // adjust as needed
  }

  private getTopLeadersPerElection(results: any[]) {
    const groupedByElection = groupBy(
      results.filter((r) => r.leaderId),
      (r) => r.electionId,
    );

    const leaderHistory = groupBy(
      results.filter((r) => r.leaderId),
      (r) => r.leaderId,
    );

    const leaderElectionYears = Object.fromEntries(
      Object.entries(leaderHistory).map(([leaderId, contests]) => [
        leaderId,
        Math.min(
          ...contests.map((c) => new Date(c.elections?.year).getFullYear()),
        ),
      ]),
    );

    const topLeadersByElection = Object.entries(groupedByElection).map(
      ([electionId, records]) => {
        const electionYear = new Date(records[0].elections.year).getFullYear();

        const leaders = Object.values(groupBy(records, (r) => r.leaderId)).map(
          (group) => {
            const first = group[0];
            const totalVotes = sumBy(group, (r) => r.voteCount ?? 0);
            const won = group.some((r) => r.isElected);
            const debut = leaderElectionYears[first.leaderId] === electionYear;

            return {
              leaderId: first.leaderId,
              leader: first.leaders,
              totalVotes,
              won,
              debut,
              electionId: Number(electionId),
              electionName: records[0].elections.name,
              electionYear,
            };
          },
        );

        const firstTimers = leaders
          .filter((l) => l.debut)
          .sort((a, b) => b.totalVotes - a.totalVotes)
          .slice(0, 4);

        const topPerformers = leaders
          .filter((l) => !firstTimers.find((f) => f.leaderId === l.leaderId))
          .sort((a, b) => b.totalVotes - a.totalVotes)
          .slice(0, 4);

        const combined = [...firstTimers, ...topPerformers];

        // ✅ Final sorting by votes
        return combined.sort((a, b) => b.totalVotes - a.totalVotes);
      },
    );

    return topLeadersByElection.flat();
  }

  private getTopPartiesPerElection(results: any[]) {
    const groupedByElection = groupBy(results, (r) => r.electionId);
    const partyHistory = groupBy(results, (r) => r.partyId);

    const partyFirstYear: Record<number, number> = Object.fromEntries(
      Object.entries(partyHistory).map(([partyId, records]) => [
        Number(partyId),
        Math.min(
          ...records.map((r) => new Date(r.elections?.year).getFullYear()),
        ),
      ]),
    );

    const topPartiesGrouped = Object.entries(groupedByElection).map(
      ([electionId, records]) => {
        const electionYear = new Date(records[0].elections.year).getFullYear();

        const parties = Object.values(groupBy(records, (r) => r.partyId)).map(
          (group) => {
            const first = group[0];
            const totalVotes = sumBy(group, (r) => r.voteCount ?? 0);
            const totalSeats = group.filter((r) => r.isElected).length;
            const debut = partyFirstYear[first.partyId] === electionYear;

            return {
              party: first.parties,
              partyId: first.partyId,
              name: first.parties?.name ?? 'Unknown',
              localName: first.parties?.localName ?? '',
              logo: first.parties?.logo ?? null,
              color: first.parties?.partyColorCode ?? '#ccc',
              electionName: records[0].elections.name,
              totalVotes,
              totalSeats,
              debut,
              electionId: Number(electionId),
              electionYear,
            };
          },
        );

        const debutParties = parties
          .filter((p) => p.debut)
          .sort(
            (a, b) =>
              b.totalSeats - a.totalSeats || b.totalVotes - a.totalVotes,
          )
          .slice(0, 4);

        const topPerformers = parties
          .filter((p) => !debutParties.find((d) => d.partyId === p.partyId))
          .sort(
            (a, b) =>
              b.totalSeats - a.totalSeats || b.totalVotes - a.totalVotes,
          )
          .slice(0, 4);

        const combined = [...debutParties, ...topPerformers];

        // Final sort within this election group
        return combined.sort(
          (a, b) => b.totalSeats - a.totalSeats || b.totalVotes - a.totalVotes,
        );
      },
    );

    // Flatten to return array
    return topPartiesGrouped.flat();
  }
}
