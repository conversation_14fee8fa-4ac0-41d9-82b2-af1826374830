import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { ElectionsService } from './elections.service';
import { CreateElectionDto } from './dto/create-election.dto';
import { UpdateElectionDto } from './dto/update-election.dto';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';
import { EntityReview } from '../ratings/EntityReview';
import { CommentableType } from '@prisma/client';
import { ElectionsAnalyticsService } from './election-analytics';
import { ElectionsOverviewService } from './election-overview';

@Controller('elections')
@UseInterceptors(RequestOriginCacheInterceptor)
@UsePipes(ValidationPipe)
export class ElectionsController {
  constructor(
    private readonly electionsService: ElectionsService,
    private entityReview: EntityReview,
    private electionsAnalyticsService: ElectionsAnalyticsService,
    private ElectionsOverviewService: ElectionsOverviewService,
  ) {}

  @Post()
  create(@Body() createElectionDto: CreateElectionDto) {
    return this.electionsService.create(createElectionDto);
  }

  @Get()
  findAll() {
    return this.electionsService.findAll();
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number }) {
    return this.electionsService.analytics(query);
  }

  @Get('/overview')
  electionOverview() {
    return this.ElectionsOverviewService.getElectionsOverview();
  }

  @Get('/candidacy-types')
  candidacyTypes() {
    return this.ElectionsOverviewService.getCandidacyTypes();
  }

  @Get('/trends')
  async electionTrends(
    @Query('type') type: 'party' | 'leader' = 'party',
    @Query('id') ids?: string,
  ) {
    let leaderTrends = [],
      partyTrends = [],
      partyTrendsSeries = [];

    if (type === 'leader') {
      leaderTrends =
        await this.ElectionsOverviewService.getTopLeadersAcrossElections(
          ids ? ids.split(',') : [],
        );
    }
    if (type === 'party') {
      const allResults = (await this.ElectionsOverviewService.getAllResults({
        partyId: {
          in: ids ? ids.split(',').map(Number) : undefined,
        },
      })) as any[];
      partyTrends = this.ElectionsOverviewService.getPartyTrends(allResults);
      partyTrendsSeries =
        await this.ElectionsOverviewService.getPartyTrendsOverYears(
          ids ? ids.split(',') : [],
        );
    }

    return {
      leaderTrends,
      partyTrends,
      partyTrendsSeries,
    };
  }

  @Get('/candidates')
  allElectionCandidates(
    @Param('id') id: string,
    @Query()
    query: PaginationSortSearchDto & {
      electedType: 'all' | 'elected' | 'not-elected';
      partyId: string;
    },
  ) {
    return this.electionsService.getAllCandidates(+id, query);
  }

  @Get('/:electionId/analytics')
  electionAnalytics(@Param('electionId') electionId: number) {
    return this.electionsAnalyticsService.getFullAnalytics(+electionId);
  }

  @Get('/sub/analytics')
  subAnalytics(@Query() query: { partyId?: number }) {
    return this.electionsService.subAnalytics(query);
  }

  @Get('/:id/code/:code/position/:positionId')
  getElectionByCodeAndId(
    @Param('id') id: string,
    @Param('code') elCode: string,
    @Param('positionId') positionId: string,
  ) {
    return this.electionsService.getElectionByCodeAndId(
      +id,
      elCode,
      +positionId,
    );
  }

  @Get('/results')
  getAllResults(@Query() query: PaginationSortSearchDto = {}) {
    return this.electionsService.getAllSubElections(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Query('partyId') partyId: string) {
    return this.electionsService.findOne(+id, {
      partyId: partyId ? +partyId : undefined,
    });
  }

  @Get(':id/candidates')
  electionCandidates(
    @Param('id') id: string,
    @Query()
    query: PaginationSortSearchDto & {
      electedType: 'all' | 'elected' | 'not-elected';
      partyId: string;
    },
  ) {
    return this.electionsService.getAllCandidates(+id, query);
  }

  @Get(':id/parties')
  async electionParties(@Param('id') id: string) {
    const electionsParties = await this.electionsService.getAllParties(+id);
    return electionsParties.map(({ party, ...election }) => ({
      party,
      ...election,
    }));
  }

  async index(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = entityId
      ? {
          where: {},
        }
      : {};

    return this.entityReview.findAll(
      {
        model: CommentableType.PARLIAMENT,
        name: 'elections',
      },
      {
        ...nextQuery,
        //@ts-expect-error
        include: {},
        ...additionalWhere,
      },
    );
  }
}
