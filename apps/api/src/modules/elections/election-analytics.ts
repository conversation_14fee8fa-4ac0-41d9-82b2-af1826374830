import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma.service';
import { ElectionsService } from './elections.service';
import { sortBy } from 'lodash';

@Injectable()
export class ElectionsAnalyticsService {
  constructor(
    private prisma: PrismaService,
    private electionService: ElectionsService,
  ) {}

  // 1. Election Summary
  async getElectionSummary(electionId: number) {
    console.log(electionId);
    const election = await this.prisma.elections.findUnique({
      where: { id: electionId },
      include: { election_results: true },
    });

    const totalVotes = election.election_results.reduce(
      (acc, cur) => acc + (cur.voteCount || 0),
      0,
    );
    const totalCandidates = election.election_results.length;
    const totalElected = election.election_results.filter(
      (r) => r.isElected,
    ).length;
    const majorityNeeded = Math.floor(totalElected / 2) + 1;
    const totalParties = new Set(
      election.election_results.map((r) => r.partyId),
    ).size;

    return {
      name: election.name,
      localName: election.localName,
      year: election.year,
      totalVotes,
      totalCandidates,
      totalElected,
      majorityNeeded,
      totalParties,
    };
  }

  // 2. Party Analytics
  async getPartyAnalytics(electionId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
      include: { parties: true, leaders: true },
    });

    const grouped = new Map<number, any>();

    for (const res of results) {
      if (!res.partyId || !res.parties) continue;
      const existing = grouped.get(res.partyId) || {
        partyId: res.partyId,
        partyName: res.parties.name,
        colorCode: res.parties.partyColorCode,
        voteCount: 0,
        seatsWon: 0,
        candidates: [],
      };

      existing.voteCount += res.voteCount || 0;
      if (res.isElected) existing.seatsWon += 1;
      existing.candidates.push({
        candidateId: res.leaderId,
        name: res.leaders?.name,
        votes: res.voteCount,
        isElected: res.isElected,
      });

      grouped.set(res.partyId, existing);
    }

    const totalVotes = Array.from(grouped.values()).reduce(
      (acc, cur) => acc + cur.voteCount,
      0,
    );

    return Array.from(grouped.values()).map((party) => ({
      ...party,
      votePercentage: ((party.voteCount / totalVotes) * 100).toFixed(2),
    }));
  }

  // 3. Top Candidates
  async getTopCandidates(electionId: number, limit = 10) {
    return this.prisma.election_results.findMany({
      where: { electionId },
      orderBy: { voteCount: 'desc' },
      take: limit,
      include: { leaders: true, districts: true, parties: true },
    });
  }

  // 4. Closest Contests
  async getClosestContests(electionId: number, limit = 10) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
      include: {
        districts: true,
        leaders: true,
        parties: true,
      },
    });

    // Group by elCode (unique constituency code)
    const elCodeGroups = new Map<string, typeof results>();

    for (const result of results) {
      if (!result.elCode) continue;
      const group = elCodeGroups.get(result.elCode) || [];
      group.push(result);
      elCodeGroups.set(result.elCode, group);
    }

    const margins: any[] = [];

    for (const [elCode, candidates] of elCodeGroups) {
      const sorted = candidates.sort(
        (a, b) => (b.voteCount || 0) - (a.voteCount || 0),
      );
      if (sorted.length >= 2) {
        const margin = (sorted[0].voteCount || 0) - (sorted[1].voteCount || 0);
        margins.push({
          elCode,
          district: sorted[0].districts?.name || 'Unknown',
          winner: sorted[0],
          runnerUp: sorted[1],
          margin,
        });
      }
    }

    return margins.sort((a, b) => a.margin - b.margin).slice(0, limit);
  }

  // 5. Party Comparison with Previous Election
  async getPartyComparison(electionId: number) {
    const curr = await this.getPartyAnalytics(electionId);
    const currentElection = await this.prisma.elections.findUnique({
      where: { id: electionId },
    });

    if (!currentElection?.parentElectionId)
      return { current: curr, previous: [], delta: [] };

    const prev = await this.getPartyAnalytics(currentElection.parentElectionId);

    const delta = curr.map((currParty) => {
      const prevParty = prev.find((p) => p.partyId === currParty.partyId);
      return {
        partyId: currParty.partyId,
        partyName: currParty.partyName,
        voteChange: currParty.voteCount - (prevParty?.voteCount || 0),
        seatChange: currParty.seatsWon - (prevParty?.seatsWon || 0),
      };
    });

    return { current: curr, previous: prev, delta };
  }

  // 6. Gender Stats
  async getGenderStats(electionId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
      include: { leaders: true },
    });

    const stats = {
      male: 0,
      female: 0,
      other: 0,
      electedMale: 0,
      electedFemale: 0,
      electedOther: 0,
    };

    for (const res of results) {
      const gender = res.leaders?.gender?.toLowerCase();
      if (!gender) continue;

      stats[gender]++;
      if (res.isElected)
        stats[`elected${gender[0].toUpperCase()}${gender.slice(1)}`]++;
    }

    return stats;
  }

  // 7. First-time Winners vs Incumbents
  async getFirstTimeWinners(electionId: number) {
    const elected = await this.prisma.election_results.findMany({
      where: { electionId, isElected: true },
      select: { leaderId: true },
    });

    const leaderIds = elected.map((e) => e.leaderId);

    const prevWins = await this.prisma.election_results.findMany({
      where: {
        isElected: true,
        electionId: { not: electionId },
        leaderId: { in: leaderIds },
      },
      select: { leaderId: true },
    });

    const prevSet = new Set(prevWins.map((e) => e.leaderId));
    const firstTimers = leaderIds.filter((id) => !prevSet.has(id));

    return {
      totalElected: leaderIds.length,
      firstTimeWinners: firstTimers.length,
      repeatWinners: leaderIds.length - firstTimers.length,
    };
  }

  // 8. Candidacy Type Breakdown
  async getCandidacyTypeBreakdown(electionId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
      include: { candidacyType: true },
    });

    const stats = {};

    for (const r of results) {
      const type = r.candidacyType?.name || 'Unknown';
      stats[type] = stats[type] || { total: 0, elected: 0 };
      stats[type].total++;
      if (r.isElected) stats[type].elected++;
    }

    return stats;
  }

  // 9. District-Level Summary
  async getDistrictSummaries(electionId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
      include: { districts: true, parties: true, leaders: true },
    });

    const map = new Map<number, any>();

    for (const r of results) {
      const dId = r.districtId;
      const district = map.get(dId) || {
        name: r.districts.name,
        candidates: [],
        totalVotes: 0,
        electedLeader: null,
      };

      district.totalVotes += r.voteCount || 0;
      district.candidates.push({
        name: r.leaders?.name,
        party: r.parties?.name,
        votes: r.voteCount,
        isElected: r.isElected,
      });

      if (r.isElected) district.electedLeader = r.leaders?.name;

      map.set(dId, district);
    }

    return Array.from(map.values());
  }

  // 10. Time Series Trend for a Party
  async getPartyTrend(partyId: number) {
    const elections = await this.prisma.elections.findMany({
      where: { electionType: 'GENERAL_PARLIAMENT_ELECTION' },
      orderBy: { year: 'asc' },
    });

    const trend = [];

    for (const election of elections) {
      const results = await this.prisma.election_results.findMany({
        where: { electionId: election.id, partyId },
      });

      const voteCount = results.reduce((a, b) => a + (b.voteCount || 0), 0);
      const seatsWon = results.filter((r) => r.isElected).length;

      trend.push({
        electionId: election.id,
        name: election.name,
        year: election.year,
        voteCount,
        seatsWon,
      });
    }

    return trend;
  }

  // 11. Leader History
  async getLeaderHistory(leaderId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { leaderId },
      include: { elections: true, districts: true, parties: true },
      orderBy: { electionId: 'asc' },
    });

    return results.map((r) => ({
      election: r.elections.name,
      year: r.elections.year,
      district: r.districts.name,
      party: r.parties?.name,
      isElected: r.isElected,
      votes: r.voteCount,
    }));
  }

  // 12. Wasted Votes
  async getWastedVotes(electionId: number) {
    const results = await this.prisma.election_results.findMany({
      where: { electionId },
    });

    const totalVotes = results.reduce((a, b) => a + (b.voteCount || 0), 0);
    const winningVotes = results
      .filter((r) => r.isElected)
      .reduce((a, b) => a + (b.voteCount || 0), 0);

    const wasted = totalVotes - winningVotes;

    return {
      totalVotes,
      winningVotes,
      wastedVotes: wasted,
      wastedPercentage: ((wasted / totalVotes) * 100).toFixed(2),
    };
  }
  async getAllParties(electionId: number, options?: { onlyWin: boolean }) {
    const partyResults = await this.prisma.election_results.groupBy({
      where: {
        electionId,
      },
      by: ['partyId'],
      _sum: {
        voteCount: true,
      },
      _count: {
        partyId: true,
        isElected: true, // this gives count of `true` rows
      },
      orderBy: {
        _sum: {
          voteCount: 'desc',
        },
      },
    });

    const partyIds = partyResults.map((result) => result.partyId);
    const partyDetails = await this.prisma.parties.findMany({
      where: {
        id: { in: partyIds },
      },
    });

    const result = partyResults
      .map((result) => {
        const partyDetail = partyDetails.find(
          (detail) => detail.id === result.partyId,
        );
        return {
          partyId: result.partyId,
          voteCount: result._sum.voteCount || 0,
          runningPlaces: result._count.partyId,
          wonPlaces: result._count.isElected, // ✅ corrected from your earlier bug
          party: partyDetail || { name: 'Unknown Party' },
        };
      })
      .filter((item) => {
        if (options?.onlyWin) {
          return item.wonPlaces > 0;
        }
        return true;
      });

    return result;
  }

  async getNewAndRepeatWinners(electionId: number) {
    // 1. Get current election year
    const currentElection = await this.prisma.elections.findUnique({
      where: { id: electionId },
      select: { year: true },
    });

    if (!currentElection) {
      throw new Error('Election not found');
    }

    const currentYear = currentElection.year;

    // 2. Get all elected leaders in this election
    const currentElected = await this.prisma.election_results.findMany({
      where: {
        electionId,
        isElected: true,
      },
      include: {
        leaders: true,
      },
    });

    const leaderIds = currentElected.map((res) => res.leaderId);

    // 3. Get all previous wins (exclude current & future elections)
    const previousWins = await this.prisma.election_results.findMany({
      where: {
        leaderId: { in: leaderIds },
        isElected: true,
        elections: {
          year: { lt: currentYear },
        },
      },
      include: {
        elections: {
          select: { year: true },
        },
      },
    });

    // 4. Map previous win stats per leader
    const prevWinsMap = new Map<number, { count: number; years: string[] }>();

    for (const win of previousWins) {
      const prev = prevWinsMap.get(win.leaderId) || { count: 0, years: [] };
      prev.count += 1;
      prev.years.push(win.elections.year.toISOString().split('T')[0]);
      prevWinsMap.set(win.leaderId, prev);
    }

    // 5. Split & enrich leader data
    const firstTimers = [];
    const repeatWinners = [];

    for (const res of currentElected) {
      const leader = res.leaders;
      const voteCount = res.voteCount ?? 0;
      const stats = prevWinsMap.get(res.leaderId);

      if (stats) {
        repeatWinners.push({
          ...leader,
          voteCount,
          repeatCount: stats.count,
          previousYears: stats.years,
        });
      } else {
        firstTimers.push({
          ...leader,
          voteCount,
          repeatCount: 0,
          previousYears: [],
        });
      }
    }

    // 6. Sort both by vote count
    const topFirstTimers = firstTimers
      .sort((a, b) => b.voteCount - a.voteCount)
      .slice(0, 20);

    const topRepeatWinners = repeatWinners
      .sort((a, b) => b.voteCount - a.voteCount)
      .slice(0, 20);

    return {
      firstTimers: topFirstTimers,
      repeatWinners: topRepeatWinners,
    };
  }

  async comparePartyPerformance(currentElectionId: number) {
    const currentElection = await this.prisma.elections.findUnique({
      where: { id: currentElectionId },
      select: { year: true, electionType: true },
    });

    if (!currentElection) return this.emptyPerformance();

    const previous = await this.prisma.elections.findFirst({
      where: {
        electionType: currentElection.electionType,
        year: { lt: currentElection.year },
      },
      orderBy: { year: 'desc' },
      select: { id: true },
    });

    if (!previous) return this.emptyPerformance();

    const [currentStats, previousStats] = await Promise.all([
      this.electionService.getAllParties(currentElectionId),
      this.electionService.getAllParties(previous.id),
    ]);

    const totalCurrentVotes = currentStats.reduce(
      (sum, p) => sum + (p.voteCount || 0),
      0,
    );
    const totalCurrentSeats = currentStats.reduce(
      (sum, p) => sum + (p.wonPlaces || 0),
      0,
    );
    const totalPreviousVotes = previousStats.reduce(
      (sum, p) => sum + (p.voteCount || 0),
      0,
    );
    const totalPreviousSeats = previousStats.reduce(
      (sum, p) => sum + (p.wonPlaces || 0),
      0,
    );

    const prevMap = new Map<number, any>();
    previousStats.forEach((p) => prevMap.set(p.partyId, p));

    const newParties: any[] = [];
    const emergingParties: any[] = [];
    const improvingParties: any[] = [];
    const decliningParties: any[] = [];

    for (const curr of currentStats) {
      const prev = prevMap.get(curr.partyId);

      const votePercent = (curr.voteCount / totalCurrentVotes) * 100;
      const seatPercent = (curr.wonPlaces / totalCurrentSeats) * 100;

      if (!prev) {
        newParties.push({
          ...curr,
          votePercent,
          seatPercent,
          previousVotes: 0,
          previousVotePercent: 0,
          previousSeats: 0,
          previousSeatPercent: 0,
        });
      } else {
        const voteDiff = curr.voteCount - prev.voteCount;
        const voteDiffPercent = (voteDiff / (prev.voteCount || 1)) * 100;

        const seatDiff = curr.wonPlaces - prev.wonPlaces;
        const seatDiffPercent = (seatDiff / (prev.wonPlaces || 1)) * 100;

        const previousVotePercent = (prev.voteCount / totalPreviousVotes) * 100;
        const previousSeatPercent = (prev.wonPlaces / totalPreviousSeats) * 100;

        const stats = {
          ...curr,
          votePercent,
          seatPercent,
          previousVotes: prev.voteCount,
          previousVotePercent,
          previousSeats: prev.wonPlaces,
          previousSeatPercent,
          voteDiff,
          voteDiffPercent,
          seatDiff,
          seatDiffPercent,
        };

        if (prev.wonPlaces <= 1 && curr.wonPlaces >= 5) {
          emergingParties.push(stats);
        } else if (seatDiff > 0 || voteDiff > 5000) {
          improvingParties.push(stats);
        } else if (seatDiff < 0 || voteDiff < -5000) {
          decliningParties.push(stats);
        }
      }
    }

    return {
      newParties: sortBy(newParties, (p) => -p.voteCount),
      emergingParties: sortBy(emergingParties, (p) => -p.seatDiff),
      improvingParties: sortBy(improvingParties, (p) => -p.seatDiff),
      decliningParties: sortBy(decliningParties, (p) => p.seatDiff),
    };
  }

  private emptyPerformance() {
    return {
      newParties: [],
      emergingParties: [],
      improvingParties: [],
      decliningParties: [],
    };
  }

  async getFullAnalytics(electionId: number) {
    const [
      summary,
      partyAnalytics,
      topCandidates,
      closestContests,
      partyComparison,
      genderStats,
      firstTimeWinners,
      candidacyBreakdown,
      districtSummaries,
      wastedVotes,
      newAndRepeatWinners,
      comparePartyPerformance,
    ] = await Promise.all([
      this.getElectionSummary(electionId),
      this.getPartyAnalytics(electionId),
      this.getTopCandidates(electionId),
      this.getClosestContests(electionId),
      this.getPartyComparison(electionId),
      this.getGenderStats(electionId),
      this.getFirstTimeWinners(electionId),
      this.getCandidacyTypeBreakdown(electionId),
      this.getDistrictSummaries(electionId),
      this.getWastedVotes(electionId),
      this.getNewAndRepeatWinners(electionId),
      this.comparePartyPerformance(electionId),
    ]);

    return {
      summary,
      partyAnalytics,
      topCandidates,
      closestContests,
      partyComparison,
      genderStats,
      firstTimeWinners,
      candidacyBreakdown,
      districtSummaries,
      wastedVotes,
      newAndRepeatWinners,
      comparePartyPerformance,
    };
  }
}
