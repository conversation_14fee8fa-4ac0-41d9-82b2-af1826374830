import { Injectable } from '@nestjs/common';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { PrismaService } from 'src/prisma.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import {
  CommentableType,
  CONTENT_TYPE,
  Prisma,
  RESOURCE_TYPE,
} from '@prisma/client';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { RatingsService } from '../ratings/ratings.service';
import { ContentsService } from 'src/contents/contents.service';

@Injectable()
export class DepartmentsService {
  constructor(
    private prismaService: PrismaService,
    private entityReview: EntityReview,
    private ratingService: RatingsService,
    private contentsService: ContentsService,
  ) {}

  findAll(query: PaginationSortSearchDto = {}) {
    return this.entityReview.findAll(
      {
        model: CommentableType.DEPARTMENT,
        name: 'departments',
      },
      {
        ...query,
        //@ts-expect-error
        include: {},
      },
    );
  }

  async findOne(id: number) {
    const department = await this.prismaService.departments.findFirst({
      where: { id },
      include: {
        cabinet_members: {
          include: {
            party: true,
            leaders: {
              include: {
                leaders_images: true,
                cabinet_members: {
                  where: {
                    departmentId: { not: id }, // Exclude current department
                  },
                  include: {
                    department: {
                      select: {
                        id: true,
                        name: true,
                        localName: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: department.id + '',
      rateOnType: RESOURCE_TYPE.DEPARTMENT,
    });

    const stats = await this.contentsService.getStats(
      department.id,
      RESOURCE_TYPE.DEPARTMENT,
    );

    // Optionally flatten related departments served by leaders
    const otherDepartments = Array.from(
      new Map(
        department.cabinet_members
          .flatMap((cm) =>
            cm.leaders.cabinet_members.map((cm2) => cm2.department),
          )
          .filter((d) => !!d) // remove nulls
          .map((dep) => [dep.id, dep]), // deduplicate
      ).values(),
    );

    return {
      ...department,
      stats,
      rating,
      otherDepartments,
    };
  }

  update(id: number, updateDepartmentDto: UpdateDepartmentDto) {
    // return `This action updates a #${id} department`;
  }

  remove(id: number) {
    // return `This action removes a #${id} department`;
  }

  async getTopContentLeadersByDepartment(
    departmentId: number,
    contents: CONTENT_TYPE[],
    pagination: PaginationSortSearchDto,
  ) {
    const limit = parseInt(pagination.limit as any) || 10;
    const page = parseInt(pagination.page as any) || 1;
    const offset = (page - 1) * limit;

    const dataRaw = await this.prismaService.$queryRaw<
      Array<{
        id: number;
        name: string;
        scandal_count: bigint;
        leaders_images: string; // JSON string
      }>
    >(Prisma.sql`
      SELECT 
        l.*,
        COUNT(c.id) AS scandal_count,
        (
          SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
              'id', li.id,
              'url', li.url,
              'enabled', li.enabled,
              'isDefault', li.isDefault
            )
          )
          FROM leaders_images li
          WHERE li.leadersId = l.id
            AND li.enabled = TRUE
        ) AS leaders_images
      FROM leaders l
      JOIN cabinet_members cm ON cm.leaderId = l.id
      JOIN contents c ON c.resourceId = l.id
      WHERE cm.departmentId = ${departmentId}
        AND c.resourceType = 'LEADER'
        AND c.contentType IN (${Prisma.join(contents)})
      GROUP BY l.id
      ORDER BY scandal_count DESC
      LIMIT ${limit} OFFSET ${offset};
    `);

    const data = dataRaw.map((item) => ({
      ...item,
      scandal_count: Number(item.scandal_count),
      leaders_images: item.leaders_images || '[]',
    }));

    const totalRaw = await this.prismaService.$queryRaw<
      Array<{ total: bigint }>
    >(Prisma.sql`
      SELECT COUNT(DISTINCT l.id) as total
      FROM leaders l
      JOIN cabinet_members cm ON cm.leaderId = l.id
      JOIN contents c ON c.resourceId = l.id
      WHERE cm.departmentId = ${departmentId}
        AND c.resourceType = 'LEADER'
        AND c.contentType IN (${Prisma.join(contents)});
    `);

    const total = Number(totalRaw[0]?.total || 0);

    return {
      items: data,
      totalItems: total,
      page,
      limit,
    };
  }

  async getMostRepeatedLeaders(limit: number = 10, departmentId?: number) {
    const result = await this.prismaService.$queryRaw<
      Array<{
        leaderId: number;
        leaderName: string;
        appointment_count: bigint;
        leaders_images: string; // JSON string of image objects
      }>
    >(Prisma.sql`
      SELECT
        l.*,
        COUNT(cm.id) AS appointment_count,
        (
          SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
              'id', li.id,
              'url', li.url,
              'enabled', li.enabled,
              'isDefault', li.isDefault
            )
          )
          FROM leaders_images li
          WHERE li.leadersId = l.id AND li.enabled = true
        ) AS leaders_images
      FROM leaders l
      JOIN cabinet_members cm ON cm.leaderId = l.id
      ${
        departmentId
          ? Prisma.sql`WHERE cm.departmentId = ${departmentId}`
          : Prisma.empty
      }
      GROUP BY l.id, l.name
      HAVING appointment_count > 1
      ORDER BY appointment_count DESC
      LIMIT ${limit};
    `);

    return result.map((row) => ({
      ...row,
      appointment_count: Number(row.appointment_count),
      leaders_images: row.leaders_images ? row.leaders_images : [],
    }));
  }

  async analytics(query: { partyId?: number } & PaginationSortSearchDto) {
    let limit = +query.limit || 10;
    let page = query.page || '1';

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let most_viewed = await this.findAll({
      top: 'views',
      limit: limit,
      page,
    });
    let mostLiked = await this.findAll({
      top: 'rates',
      limit: limit,
      page,
    });

    let lessLiked = await this.findAll({
      top: 'rates',
      limit: limit,
      page,
      sort_by: 'asc',
    });

    const repeatedLeaders = await this.getMostRepeatedLeaders(
      limit,
      +query.partyId,
    );

    return {
      lessLiked,
      most_viewed,
      recentlyAdded: { items: [] },
      mostLiked,
      repeatedLeaders: { items: repeatedLeaders },
    };
  }
}
