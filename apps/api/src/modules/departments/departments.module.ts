import { Module } from '@nestjs/common';
import { DepartmentsService } from './departments.service';
import { DepartmentsController } from './departments.controller';
import { PrismaService } from 'src/prisma.service';
import { EntityReview } from '../ratings/EntityReview';
import { RatingsService } from '../ratings/ratings.service';
import { most_viewedService } from '../most-viewed/most-viewed.service';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';

@Module({
  controllers: [DepartmentsController],
  providers: [
    DepartmentsService,
    PrismaService,
    EntityReview,
    RatingsService,
    most_viewedService,
    ContentsService,
    AiService,
  ],
})
export class DepartmentsModule {}
