import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  Query,
} from '@nestjs/common';
import { DepartmentsService } from './departments.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { FindOneParams } from 'src/dtos/FindOneParams';
import { CONTENT_TYPE } from '@prisma/client';

@Controller('departments')
@UseInterceptors(RequestOriginCacheInterceptor)
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Get()
  findAll(@Query() query: PaginationSortSearchDto = {}) {
    return this.departmentsService.findAll(query);
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number }) {
    return this.departmentsService.analytics(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.departmentsService.findOne(+id);
  }

  @Get('/:id/leaders/top')
  topContentLeadersByDepartment(
    @Param() params: FindOneParams,
    @Query() query: PaginationSortSearchDto & { contentType?: string },
  ) {
    const contents = query.contentType?.split(',') || [];
    return this.departmentsService.getTopContentLeadersByDepartment(
      +params.id,
      contents as CONTENT_TYPE[],
      query,
    );
  }
  @Get('/:id/leaders/most-repeated')
  mostRepeatedLeaders(@Param('id') id: string) {
    return this.departmentsService.getMostRepeatedLeaders(10, +id);
  }
}
