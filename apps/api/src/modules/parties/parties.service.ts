import { Injectable } from '@nestjs/common';
import { CreatePartyDto } from './dto/create-party.dto';
import { UpdatePartyDto } from './dto/update-party.dto';
import { PrismaService } from 'src/prisma.service';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { RatingsService } from '../ratings/ratings.service';
import { sortBy } from 'lodash';
import { EntityReview } from '../ratings/EntityReview';
import { CommentableType, CONTENT_TYPE, Prisma } from '@prisma/client';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { DEFAULT_SUMMARY } from 'src/utils';
import { ElectionsService } from '../elections/elections.service';
import { EntityTypeEnum } from 'src/constants';

@Injectable()
export class PartiesService {
  getPartyById(partyId: any) {
    return this.prismaService.parties.findFirst({
      where: {
        id: +partyId,
      },
    });
  }
  constructor(
    private prismaService: PrismaService,
    private ratingService: RatingsService,
    private entityReview: EntityReview,
    private contentsService: ContentsService,
    private aiService: AiService,
    private electionService: ElectionsService,
  ) {}

  party_leaders(partyId: number, query: PaginationSortSearchDto) {
    return this.prismaService.index(
      'party_leaders',
      {
        // @ts-expect-error
        partyId,
      },
      {
        include: {
          leader: true,
        },
        ...query,
      },
    );

    // return this.prismaSerivce.party.findFirst({
    //   where: {
    //     id: arg0,
    //   },
    //   include: {
    //     PartyLeader: {
    //       include: {
    //         leader: true
    //       },
    //       take: 10
    //     },
    //   },
    // });
  }

  party_leadersElected(
    partyIdx: number,
    {
      electionId,
      electedType,
      stateId,
      municipalId,
      districtId,
      wardId,
      partyId,
      ...query
    }: PaginationSortSearchDto & {
      electionId: number;
      stateId?: string;
      partyId?: string;
      municipalId?: string;
      districtId?: string;
      wardId?: string;
      electedType: 'all' | 'elected' | 'not-elected';
    },
  ) {
    return this.prismaService.index(
      'election_results',
      {
        // @ts-expect-error
        partyId: !!partyIdx ? partyIdx : partyId || undefined,
        stateId: stateId && +stateId,
        municipalId: municipalId && +municipalId,
        districtId: districtId && +districtId,
        wardId: wardId && +wardId,
        electionId: !!electionId ? +electionId : undefined,
        remarks:
          electedType === 'all'
            ? undefined
            : {
                [electedType === 'elected' ? 'not' : 'equals']: null,
              },
      },
      {
        include: {
          elections: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          parties: true,
          municipals: true,
          states: true,
          ward: true,
          districts: true,
          candidacyType: true,
        },
        ...query,
      },
    );

    // return this.prismaService.election_results.findMany({
    //   where: {
    //     partyId,
    //     // electionId
    //     remarks: {},
    //   },
    //   include: {
    //      leaders: {

    //     parties: true,
    //   },
    // });
  }

  create(createPartyDto: CreatePartyDto) {
    return {};
  }

  async findAll(query: PaginationSortSearchDto = {}, where?: any) {
    return this.entityReview.findAll(
      {
        model: CommentableType.Party,
        name: 'parties',
      },
      Object.assign(query, { where: where }),
    );
  }

  async findOne(id: number) {
    const result = await this.prismaService.parties.findFirst({
      where: {
        id,
      },
    });
    if (!result) return null;
    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: result.id + '',
      rateOnType: 'Party',
    });
    const stats = await this.contentsService.getStats(result.id, 'PARTY');
    let summary = (
      await this.contentsService.getContents({
        resourceId: result.id,
        resourceType: 'PARTY',
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: result.id,
        resourceType: 'PARTY',
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    const response = { ...result, stats, rating };
    if (!summary || !summaryNP) {
      summary = DEFAULT_SUMMARY;

      await this.aiService.queueSummaryBuilder(
        {
          id: result.id,
          type: EntityTypeEnum.Party,
        },
        EntityTypeEnum.Party + result.id,
      );

      this.aiService
        .generateOverviewSummaryOfParty(response)
        .then((generatedSummary) => {
          const code = `${result.id}-PARTY-SUMMARY`;
          this.aiService
            .transliterate(generatedSummary)
            .then((generatedSummary) => {
              const code = `${result.id}-PARTY-SUMMARY`;
              this.contentsService
                .saveContent({
                  resourceId: result.id,
                  resourceType: 'PARTY',
                  contentType: 'SUMMARY_NP',
                  contentStatus: 'PUBLISHED',
                  content: generatedSummary,
                  title: 'Summary of ' + result.name,
                  code: code + 'np',
                })
                .catch((e) => console.log(e));
            })
            .catch((e) => console.log(e));

          this.contentsService
            .saveContent({
              resourceId: result.id,
              resourceType: 'PARTY',
              contentType: 'SUMMARY',
              contentStatus: 'PUBLISHED',
              content: generatedSummary,
              title: 'Summary of ' + result.name,
              code,
            })
            .catch((e) => console.log(e));
        })
        .catch((e) => console.log(e));
    }

    const projects = await this.prismaService.projects_by.findMany({
      where: {
        partyId: result.id,
      },
      include: {
        projects: true,
      },
    });

    return {
      ...response,
      projects: projects.map((item) => item.projects),
      summary,
      summaryNP,
    };
  }

  update(id: number, updatePartyDto: UpdatePartyDto) {
    return {};
  }

  remove(id: number) {
    return {};
  }

  async analytics(
    filter: { partyId?: number; electionId?: number } & PaginationSortSearchDto,
  ) {
    let limit = +filter.limit || 10;
    let page = filter.page || '1';

    let regionWins = filter.electionId
      ? await this.electionService.getPartyWins({
          electionId: +filter.electionId,
          partyId: +filter.partyId,
        })
      : {};
    if (filter.electionId) {
      return {
        regionWins,
      };
    }

    let recentlyAdded =
      !filter.category || filter.category === 'recentlyAdded'
        ? await this.prismaService.parties.findMany({
            take: limit,
            skip: +page * limit,
            orderBy: {
              createdAt: 'desc',
            },
          })
        : [];

    let most_viewed =
      !filter.category || filter.category === 'most_viewed'
        ? await this.findAll({
            top: 'views',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };

    let mostLiked =
      !filter.category || filter.category === 'mostLiked'
        ? await this.findAll({
            top: 'rates',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };

    let lessLiked =
      !filter.category || filter.category === 'mostLiked'
        ? await this.findAll({
            top: 'rates',
            limit: limit,
            page,
            sort_by: 'asc',
          })
        : { items: [], totalItems: 0 };
    return {
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
      lessLiked,
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async getTopContentLeaders(
    partyId: number,
    contents: CONTENT_TYPE[],
    pagination: PaginationSortSearchDto,
  ) {
    const limit = parseInt(pagination.limit as any) || 10;
    const page = parseInt(pagination.page as any) || 1;
    const offset = (page - 1) * limit;

    const dataRaw = await this.prismaService.$queryRaw<
      Array<{
        id: number;
        name: string;
        scandal_count: bigint;
        leaders_images: string; // JSON array string
      }>
    >(
      Prisma.sql`
        SELECT 
          l.*,
          COUNT(c.id) AS scandal_count,
          (
            SELECT JSON_ARRAYAGG(
              JSON_OBJECT(
                'id', li.id,
                'url', li.url,
                "enabled", li.enabled,
                "isDefault", li.isDefault
              )
            )
            FROM leaders_images li
            WHERE li.leadersId = l.id
            and li.enabled=true
          ) AS leaders_images
        FROM leaders l
        JOIN party_leaders pl ON pl.leaderId = l.id
        JOIN contents c ON c.resourceId = l.id
        WHERE pl.partyId = ${partyId}
          AND c.resourceType = 'LEADER'
          AND c.contentType IN (${Prisma.join(contents)})
        GROUP BY l.id
        ORDER BY scandal_count DESC
        LIMIT ${limit} OFFSET ${offset};
      `,
    );

    const data = dataRaw.map((item) => ({
      ...item,
      scandal_count: Number(item.scandal_count),
      leaders_images: item.leaders_images || '[]',
    }));

    const totalRaw = await this.prismaService.$queryRaw<
      Array<{ total: bigint }>
    >(
      Prisma.sql`
        SELECT COUNT(DISTINCT l.id) as total
        FROM leaders l
        JOIN party_leaders pl ON pl.leaderId = l.id
        JOIN contents c ON c.resourceId = l.id
        WHERE pl.partyId = ${partyId}
          AND c.resourceType = 'LEADER'
          AND c.contentType IN (${Prisma.join(contents)});
      `,
    );

    const total = Number(totalRaw[0]?.total || 0);

    return {
      items: data,
      totalItems: total,
      page,
      limit,
    };
  }
}
