import {
  IsA<PERSON>y,
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Min<PERSON><PERSON><PERSON>,
} from 'class-validator';
import { isArray } from 'lodash';
import { IUserAddress } from 'src/interfaces/IUserAddress';

export class UpdateProfileDto {
  @IsString()
  @MinLength(2)
  @IsOptional()
  firstName?: string;

  @IsString()
  @MinLength(2)
  @IsOptional()
  lastName?: string;

  @IsUrl()
  @IsOptional()
  profileImage?: string;

  @IsString()
  @IsOptional()
  defaultPaymentMethodId?: string;

  @IsArray()
  @IsOptional()
  addresses?: IUserAddress[];

  @IsString()
  @IsOptional()
  isProfileImageDeleting?: string;
}
