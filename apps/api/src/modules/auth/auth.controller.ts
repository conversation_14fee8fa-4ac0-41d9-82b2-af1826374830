import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  Res,
  UnauthorizedException,
  UnprocessableEntityException,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterDto } from './dtos/register.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UnverifiedUserCreatedEvent } from 'src/events/UnverifiedUserCreatedEvent';
import { JwtAuthGuard } from './jwt-auth.guard';
import { UpdateProfileDto } from './dtos/UpdateProfile.dto';
import { users } from '@prisma/client';
import { LoginDto } from './dtos/login.dto';
import { Throttle } from '@nestjs/throttler';
import { TurnstileCaptcha } from 'nest-cloudflare-turnstile';
import { AuthGuard } from '@nestjs/passport';
import { isAppEnvDev } from 'src/utils';
import { Response } from 'express';

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private eventEmitter: EventEmitter2,
  ) {}

  @Post('login')
  @Throttle(10, 60000 * 2)
  @TurnstileCaptcha()
  @UsePipes(ValidationPipe)
  async login(@Body() auth: LoginDto) {
    try {
      const user = await this.authService.login(auth);

      return user;
    } catch (err) {
      throw new UnauthorizedException(err.message);
    }
  }

  @Post('register')
  @Throttle(10, 60000 * 2)
  @UsePipes(ValidationPipe)
  @TurnstileCaptcha()
  async register(@Body() body: RegisterDto) {
    try {
      const { password, ...user } = await this.authService.register(body);
      this.eventEmitter.emit(
        UnverifiedUserCreatedEvent.event,
        new UnverifiedUserCreatedEvent(user as users),
      );
      delete user.verifyCode;
      return user;
    } catch (err) {
      if (err.code === 'P2002') {
        throw new BadRequestException('Email already exists.');
      }

      throw new BadRequestException();
    }
  }

  @Get('verify/:hash')
  async verifyEmail(@Param() param: { hash: string }, @Res() res: any) {
    try {
      const user = await this.authService.verifyEmail(param.hash);

      res.redirect(
        'https://nepaltracks.com/verify/success?email=' + user.email,
      );
    } catch (err) {
      throw new UnauthorizedException('Invalid verification code');
    }
  }
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  profile(@Req() req) {
    if (!req.user) throw new UnauthorizedException();
    return this.authService.getProfile(req.user.userId as unknown as number);
  }

  @Post('profile')
  @UseGuards(JwtAuthGuard)
  @UsePipes(ValidationPipe)
  async updateProfile(@Body() body: UpdateProfileDto, @Req() req) {
    try {
      const { verifyCode, password, ...user } =
        await this.authService.updateProfile(req.user.userId, body);

      return user;
    } catch (err) {
      console.log(err);
      throw new UnprocessableEntityException();
    }
  }

  // Google
  @Get('social/google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {}

  @Get('google/redirect')
  @UseGuards(AuthGuard('google'))
  googleAuthRedirect(@Req() req) {
    return this.authService.loginWithSocial(req.user);
  }

  // Facebook
  @Get('social/facebook')
  @UseGuards(AuthGuard('facebook'))
  async facebookAuth() {}

  @Get('facebook/redirect')
  @UseGuards(AuthGuard('facebook'))
  facebookAuthRedirect(@Req() req) {
    return this.authService.loginWithSocial(req.user);
  }

  @Get('social/twitter')
  @UseGuards(AuthGuard('twitter'))
  async twitterAuth() {}

  @Get('twitter/redirect')
  @UseGuards(AuthGuard('twitter'))
  async twitterAuthRedirect(@Req() req, @Res() res: Response) {
    const response = await this.authService.loginWithSocial(req.user);
    const host = isAppEnvDev()
      ? 'http://localhost:3232'
      : 'https://nepaltracks.com';
    const redirectUrl = `${host}/auth/social/login/callback/twitter#token=${response.access_token}`;
    return res.redirect(redirectUrl);
  }
}
