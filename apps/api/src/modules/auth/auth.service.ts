import { Injectable } from '@nestjs/common';
import { RegisterDto } from './dtos/register.dto';
import bcrypt from 'bcrypt';
import { PrismaService } from 'src/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { UpdateProfileDto } from './dtos/UpdateProfile.dto';
import { LoginDto } from './dtos/login.dto';
@Injectable()
export class AuthService {
  constructor(
    private prismaService: PrismaService,
    private jwtService: JwtService,
  ) {}
  async validateUser(username: string, password: string) {
    const user = await this.prismaService.users.findFirst({
      where: { email: username },
    });
    if (!user) throw new Error('Username/Password combination not found.');
    if (!user.isVerified) throw new Error('User is not verified.');

    const isMatched = await bcrypt.compare(password, user.password);

    if (user && isMatched) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(loginCredentials: LoginDto): Promise<{
    accessToken: string;
  }> {
    const user = await this.validateUser(
      loginCredentials.username,
      loginCredentials.password,
    );
    if (!user) {
      return null;
    }
    const accessToken = await this.jwtService.sign(user);
    return {
      accessToken,
    };
  }

  async register(register: RegisterDto) {
    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash(register.password, salt);
    return this.prismaService.users.create({
      data: {
        email: register.email,
        firstName: register.firstName,
        // idpProvider: IDP_PROVIDER.EMAIL,
        isVerified: false,
        lastName: register.lastName,
        password: hashedPassword,
        stripeId: '',
        verifyCode: uuidv4(),
      },
    });
  }

  async verifyEmail(hash: string) {
    const user = await this.prismaService.users.findFirstOrThrow({
      where: {
        verifyCode: hash,
      },
    });

    if (user.isVerified) {
      throw new Error('User has been verified already');
    }

    await this.prismaService.users.update({
      data: {
        isVerified: true,
        verifyCode: null,
      },
      where: {
        id: user.id,
      },
    });
    return user;
  }

  async getProfile(userId: number) {
    const { password, verifyCode, ...user } =
      await this.prismaService.users.findFirstOrThrow({
        where: {
          id: userId,
        },
        include: {
          user_address: true,
        },
      });
    return user;
  }

  async updateProfile(
    userId: number,
    { addresses, ...payload }: UpdateProfileDto,
  ) {
    if (addresses) {
      for (const address of addresses) {
        const addressData = {
          userId: userId,
          address_type: address.address_type || 'PERMANENT',
          municipalityId: isNaN(+address.municipalId)
            ? undefined
            : +address.municipalId,
          districtId: isNaN(Number(address.districtId))
            ? undefined
            : Number(address.districtId),
          wardId: isNaN(+address.wardId) ? undefined : +address.wardId,
          stateId: isNaN(+address.stateId) ? undefined : +address.stateId,
          electionCenterId: isNaN(+address.electionCenterId)
            ? undefined
            : +address.electionCenterId,
        };

        const existingAddress = !address.id
          ? null
          : await this.prismaService.user_address.findFirst({
              where: {
                id: address.id,
                userId,
              },
            });

        let addressResult;
        if (existingAddress) {
          addressResult = await this.prismaService.user_address.update({
            where: {
              id: address.id,
            },
            data: addressData,
          });
        } else {
          addressResult = await this.prismaService.user_address.create({
            data: addressData,
          });
        }
      }
    }
    if (payload.isProfileImageDeleting === 'true') {
      payload.profileImage = null;
    }
    delete payload.isProfileImageDeleting;
    return this.prismaService.users.update({
      where: {
        id: userId,
      },
      data: payload,
      include: {
        user_address: true,
      },
    });
  }

  async loginWithSocial(user: any) {
    // Lookup or create user in DB here
    const payload = {
      email: user.email,
      sub: user.email,
      userId: null,
      id: null,
    };
    if (!user.email) throw new Error('Email is required');

    let userByEmail = await this.prismaService.users.findFirst({
      where: {
        email: user.email,
      },
    });

    if (!userByEmail) {
      userByEmail = await this.prismaService.users.create({
        data: {
          email: user.email,
          firstName: user.firstName,
          isVerified: true,
          lastName: user.lastName,
          password: '',
          stripeId: '',
          verifyCode: null,
          profileImage: user.picture || '',
        },
      });
    }

    payload.userId = userByEmail.id;
    payload.id = userByEmail.id;
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }
}
