import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-facebook';
import { Injectable } from '@nestjs/common';
import { isAppEnvDev } from 'src/utils';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor() {
    const host = isAppEnvDev()
      ? 'http://localhost:3232'
      : 'https://nepaltracks.com';
    super({
      clientID: process.env['FACEBOOK_APP_ID'],
      clientSecret: process.env['FACEBOOK_APP_SECRET'],
      callbackURL: `${host}/auth/social/login/callback/facebook`,
      profileFields: ['emails', 'name', 'picture.type(large)'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: Function,
  ) {
    const { name, emails, photos } = profile;
    const user = {
      email: emails?.[0]?.value,
      firstName: name.givenName,
      lastName: name.familyName,
      picture: photos?.[0]?.value,
      provider: 'facebook',
    };
    done(null, user);
  }
}
