import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-twitter';
import { Injectable } from '@nestjs/common';
import { isAppEnvDev } from 'src/utils';

@Injectable()
export class TwitterStrategy extends PassportStrategy(Strategy, 'twitter') {
  constructor() {
    // const host = isAppEnvDev()
    //   ? 'http://localhost:3232'
    //   : 'https://nepaltracks.com';
    const host = isAppEnvDev()
      ? 'http://localhost:3434/api/v1' // backend URL
      : 'https://api.nepaltracks.com/api/v1';

    super({
      consumerKey: process.env.TWITTER_API_KEY,
      consumerSecret: process.env.TWITTER_API_SECRET,
      callbackURL: `${host}/auth/twitter/redirect`, // <== match this!
      profileFields: ['emails', 'name', 'picture.type(large)'],
      includeEmail: true, // ✅ This tells Twitter to include email
    });
  }

  async validate(
    token: string,
    tokenSecret: string,
    profile: any,
  ): Promise<any> {
    const { id, username, displayName, emails, photos } = profile;
    const name = displayName.split(' ');
    return {
      twitterId: id,
      username,
      displayName,
      firstName: name[0],
      lastName: name[1],
      email: emails?.[0]?.value,
      picture: photos?.[0]?.value,
      provider: 'twitter',
    };
  }
}
