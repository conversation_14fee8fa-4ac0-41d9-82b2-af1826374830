import { Injectable } from '@nestjs/common';
//@ts-ignore
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { camelCase, create, isObject } from 'lodash';
import { PrismaService } from 'nestjs-prisma';
import { deserializeFromDotNotation } from 'src/utils';

@Injectable()
export class AdminService {
  constructor(private prismaService: PrismaService) {}

  async *updateFromSheet(token: any, sheetId: string, task: string) {
    const intergers = ['count', 'id'];
    const booleanColumns = ['is'];
    const doc = await this.initGoogleSpreadSheet(token, sheetId);
    const originalSheet = doc.sheetsByTitle[task];
    const [model] = task.split(':');
    const rawRows = await originalSheet.getRows();

    for (const campaignRow of rawRows) {
      let lastInsertedData = null;
      try {
        const row = deserializeFromDotNotation(campaignRow) as any;
        const [_sheet, _rowNumber, _rawData, ID, ...header] = Object.keys(
          row,
        ) as any;
        const formattedRow = this.getFormattedRow(
          task,
          header.reduce((acc, key) => {
            const value = row[key];
            if (isObject(value)) return acc;

            const formattedKey =
              key === 'ecCandidateID' ? 'ecCandidateID' : camelCase(key);
            const lowerKey = key.toLowerCase();
            acc[formattedKey] = intergers.some((i) => lowerKey.includes(i))
              ? Number(value)
              : booleanColumns.some((i) => lowerKey.includes(i))
              ? value === '1'
              : value;

            return acc;
          }, {}),
        );
        // news
        const codeKey = model === 'leaders' ? 'ecCandidateID' : 'code';
        const code =
          model === 'leaders'
            ? formattedRow.ecCandidateID + ''
            : formattedRow[codeKey];
        lastInsertedData = formattedRow;
        const record = await this.prismaService[model].upsert({
          where: {
            [codeKey]: code,
            id: formattedRow.id,
          },
          create: {
            ...formattedRow,
            [codeKey]: code,
          },
          update: {
            ...formattedRow,
            [codeKey]: code,
          },
        });
        // testing
        yield record;
      } catch (err) {
        console.log(err);
        throw err;
      }
    }
  }

  private async initGoogleSpreadSheet(token: any, sheetId: string) {
    const doc = new GoogleSpreadsheet(sheetId);
    await doc.useServiceAccountAuth({
      client_email: token.client_email,
      private_key: token.private_key,
    });
    await doc.loadInfo();
    return doc;
  }

  getFormattedRow(task: string, row: any) {
    if (task === 'contents') {
      return {
        ...row,
        id: row.ID ? Number(row.ID) : undefined,
        resourceId: row.resourceId ? Number(row.resourceId) : undefined,
        code: `${row.resourceId}-${row.resourceType}-${row.contentType}`,
        eventDate: row.eventDate ? new Date(row.eventDate) : undefined,
        eventDueDate: row.eventDueDate ? new Date(row.eventDueDate) : undefined,
        eventEndDate: row.eventEndDate ? new Date(row.eventEndDate) : undefined,
      };
    }
    return {
      ...row,
      id: row.ID || row.id ? Number(row.ID || row.id) : undefined,
      startDate: row.startDate ? new Date(row.startDate) : undefined,
      endDate:
        row.endDate && row.endDate != 'NULL'
          ? new Date(row.endDate)
          : undefined,
    };
  }
}
