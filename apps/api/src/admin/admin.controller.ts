import {
  BadRequestException,
  Controller,
  Get,
  InternalServerErrorException,
  Param,
  ParseFilePipe,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('/update-from-sheet/:task')
  @UseInterceptors(FileInterceptor('file'))
  async updateFromSheet(
    @Param('task') task: string,
    @UploadedFile(new ParseFilePipe({ validators: [] }))
    file: Express.Multer.File,
    @Query('sheetId') sheetId: string,
    @Res() res: any,
  ) {
    try {
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      const token = JSON.parse(file.buffer.toString());

      const data = this.adminService.updateFromSheet(token, sheetId, task);
      let count = 0;

      for await (const item of data) {
        res.write(`${JSON.stringify({ added: item })}\n`);
        count++;
      }

      res.write(`${JSON.stringify({ totalCount: count })}\n`);
      res.end();
    } catch (err) {
      res.status(400).json({
        message: err.message,
        cause: err,
      });
    }
  }
}
