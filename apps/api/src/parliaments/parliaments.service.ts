import { Injectable } from '@nestjs/common';
import { CreateParliamentDto } from './dto/create-parliament.dto';
import { UpdateParliamentDto } from './dto/update-parliament.dto';
import { PrismaService } from 'src/prisma.service';
import { groupBy, sortBy, uniqBy } from 'lodash';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { ILeaderElectionFilter } from 'src/interfaces/ILeaderElectionFilter';
import {
  CommentableType,
  ParliamentMemberElectionType,
  ParliamentMemberType,
  Prisma,
} from '@prisma/client';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { DEFAULT_SUMMARY } from 'src/utils';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { EntityTypeEnum } from 'src/constants';

@Injectable()
export class ParliamentsService {
  constructor(
    private entityReview: EntityReview,
    private ratingService: RatingsService,
    private prismaService: PrismaService,
    private electionService: ElectionsService,
    private contentsService: ContentsService,
    private aiService: AiService,
    private governmentsService: GovernmentsService,
  ) {}

  async findAll() {
    const parliaments = await this.prismaService.parliaments.findMany({
      include: {
        states: true,
      },
      orderBy: {
        stateId: 'asc',
      },
    });
    // const parliaments = await this.prismaService.parliaments.findMany({
    //   select: {
    //     name: true,
    //     description: true,
    //     startDate: true,
    //     endDate: true,
    //   },
    //   orderBy: {
    //     startDate: 'desc',
    //   },
    // });
    const groupedItems = groupBy(parliaments, 'stateId');
    const results = Object.keys(groupedItems).map((item) => ({
      count: groupedItems[item].length,
      stateId: item === 'null' ? 'centre' : item,
      state: groupedItems[item]?.[0]?.states,
    }));
    return [results.pop(), ...results];
  }

  async findByLevel(id: string) {
    const parliaments = await this.prismaService.parliaments.findMany({
      where: {
        stateId: isNaN(+id) ? null : +id,
      },
      select: {
        name: true,
        description: true,
        startDate: true,
        endDate: true,
        id: true,
        houseType: true,
      },
      orderBy: {
        startDate: 'desc',
      },
    });

    return parliaments;
  }
  async getParliamentById(id: string) {
    const parliament = await this.prismaService.parliaments.findFirst({
      where: {
        id: +id,
      },
      include: {
        governments: {
          orderBy: {
            startedAt: 'desc',
          },
        },
        states: true,
        elections: true,
        parliament_members: {
          where: {
            memberType: { notIn: [ParliamentMemberType.MEMBER] },
          },
          include: {
            member: true,
          },
        },
      },
      orderBy: {
        startDate: 'desc',
      },
    });
    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: parliament.id + '',
      rateOnType: 'PARLIAMENT',
    });
    const { parliament_members, ...nextParliament } = parliament;
    const stats = await this.contentsService.getStats(
      parliament.id,
      'PARLIAMENT',
    );
    let summary = (
      await this.contentsService.getContents({
        resourceId: parliament.id,
        resourceType: 'PARLIAMENT',
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: parliament.id,
        resourceType: 'PARLIAMENT',
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    const response = {
      ...nextParliament,
      speakers: parliament_members,
      rating,
    };
    if (!summary || !summaryNP) {
      try {
        summary = DEFAULT_SUMMARY;
        await this.aiService.queueSummaryBuilder(
          {
            id: parliament.id,
            type: EntityTypeEnum.Parliament,
          },
          EntityTypeEnum.Parliament + parliament.id,
        );
      } catch (e) {
        console.log(e);
        summary = 'Not available.';
      }
    }

    return { ...response, summary, summaryNP, stats };
  }

  async getPariliamentParties(id: number) {
    const parliament = await this.prismaService.parliaments.findFirst({
      where: {
        id: +id,
      },
      include: { elections: true },
      orderBy: {
        startDate: 'desc',
      },
    });
    const parties = await this.electionService.getAllParties(
      parliament.electionId,
      {
        onlyWin: true,
      },
    );
    return parties;
  }

  async getPariliamentLeaders(
    id: number,
    {
      electedType,
      stateId,
      municipalId,
      districtId,
      partyId,
      wardId,
      ...query
    }: PaginationSortSearchDto & ILeaderElectionFilter,
  ) {
    const parliamentLeaders = await this.prismaService.index(
      'parliament_members',
      {
        // @ts-expect-error
        parliamentId: id,
      },
      {
        include: {
          member: {
            include: {
              leaders_images: true,
              party_leaders: {
                where: {
                  // endDate: null,
                },
                include: {
                  party: true,
                },
              },
            },
          },
        },
        limit: 300,
      },
    );
    const idsOfParliamentarian = parliamentLeaders.items.map(
      (item) => item.memberId,
    );
    const electionsResultOfLeaders = await this.prismaService.index(
      'election_results',
      {
        // @ts-expect-error
        leaderId: { in: idsOfParliamentarian },
        partyId: !!partyId ? +partyId : undefined,
        stateId: stateId && +stateId,
        municipalId: municipalId && +municipalId,
        districtId: districtId && +districtId,
        wardId: wardId && +wardId,
      },
      {
        include: {
          parties: true,
          states: true,
          districts: true,
          municipals: true,
          ward: true,
          leaders: {
            include: {
              leaders_images: true,
            },
          },
        },
        ...query,
        limit: 300,
      },
    );
    const isFilter = !!partyId || !!stateId || !!municipalId || !!districtId;
    const mapResultsById = new Map();
    electionsResultOfLeaders.items.forEach((item) => {
      mapResultsById.set(item.leaderId, item);
    });
    if (isFilter) {
      parliamentLeaders.items = parliamentLeaders.items.filter((item) => {
        if (partyId && !stateId && !municipalId && !districtId) {
          return mapResultsById.has(item.memberId) || item.partyId === +partyId;
        }
        return mapResultsById.has(item.memberId);
      });
      parliamentLeaders.totalItems = parliamentLeaders.items.length;
    }
    return {
      totalItems: parliamentLeaders.totalItems,
      items: (
        parliamentLeaders.items?.map((item) => {
          const {
            area,
            parties,
            states,
            districts,
            municipals,
            ward,
            voteCount,
            isElected,
            leaders,
          } = mapResultsById.get(item.memberId) || {};

          return {
            area,
            states,
            districts,
            municipals,
            ward,
            leaders: item.member,
            voteCount,
            isElected,
            electionType: item.electionType,
            memberType: item.memberType,
            parties: parties || item.member.party_leaders?.[0]?.party,
          };
        }) || []
      ).sort((a, b) => a.voteCount - b.voteCount),
    };
  }

  async index(query: PaginationSortSearchDto = {}) {
    const { entityId, ...nextQuery } = query;
    const additionalWhere = entityId
      ? {
          where: {},
        }
      : {};

    return this.entityReview.findAll(
      {
        model: CommentableType.PARLIAMENT,
        name: 'parliaments',
      },
      {
        searchFields: ['name', 'description'],
        ...nextQuery,
        //@ts-expect-error
        include: {},
        ...additionalWhere,
      },
    );
  }

  async analytics(query: PaginationSortSearchDto & { partyId?: number }) {
    let limit = 10;
    if (query.partyId) {
      limit = 1000;
    }
    let most_viewed = await this.index({
      top: 'views',
      limit: limit,
    });
    let mostLiked = await this.index({
      top: 'rates',
      limit: limit,
    });

    const popularParliamentarians = await this.getPopularParliamentarians(
      query,
    );

    return {
      most_viewed,
      mostLiked,
      hotParliamentLeaders: popularParliamentarians,
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async getParliamentOverview(id: number) {
    const government = await this.prismaService.governments.findFirst({
      where: {
        parliamentId: id,
        endAt: null,
      },
      include: {
        parliament: true,
      },
    });
    const governmentOverview =
      await this.governmentsService.getOverviewOfGovernment(
        government.id,
        government.government_type,
      );

    const parliament = government.parliament;
    const parties = await this.getPariliamentParties(id);
    const idsOfRulingParties = governmentOverview.coaltion.map(
      (item) => item.party.id,
    );

    //@ts-expect-error
    const members = await this.getPariliamentLeaders(id, {
      limit: 300,
      page: 1 + '',
    });
    const membersGroupedByParty = groupBy(members.items, 'parties.id');
    const processParties = (isRuling) =>
      parties
        .filter((item) =>
          isRuling
            ? idsOfRulingParties.includes(item.partyId)
            : !idsOfRulingParties.includes(item.partyId),
        )
        .map((item) => {
          const partyMembers = membersGroupedByParty[item.partyId] || [];
          return {
            ...item,
            directMembersCount: partyMembers.filter(
              (member) => member.electionType === 'DIRECT',
            ).length,
            proportionalMembersCount: partyMembers.filter(
              (member) => member.electionType === 'PROPORTIONAL',
            ).length,
            totalMembersCount: partyMembers.length,
            members: partyMembers,
          };
        });

    const rulingParties = sortBy(processParties(true), '-totalMembersCount');
    const oppositionParties = sortBy(
      processParties(false),
      '-totalMembersCount',
    );
    const allDirectMembersCount = members.items.filter(
      (member) => member.electionType === 'DIRECT',
    ).length;
    const allProportionalMembersCount = members.items.filter(
      (member) => member.electionType === 'PROPORTIONAL',
    ).length;

    return {
      allDirectMembersCount,
      allProportionalMembersCount,
      totalMembersCount: members.totalItems,
      rulingParties,
      oppositionParties,
      government,
      parliament,
    };
  }

  async syncParliamentMembers(parliamentId: number) {
    const parliaments = await this.prismaService.parliaments.findFirst({
      where: {
        id: parliamentId,
      },
      include: {
        elections: {
          include: { childElections: true },
        },
      },
    });
    const allElectionIds = parliaments.elections.childElections
      .map((item) => item.id)
      .concat([parliaments.electionId]);
    const allElectionResults =
      await this.prismaService.election_results.findMany({
        // distinct: ["leaderId"],
        where: {
          electionId: {
            in: allElectionIds,
          },
          isElected: true,
        },
        include: {
          leaders: {
            include: {
              leaders_images: true,
            },
          },
          parties: true,
        },
      });
    const allLeaderIds = allElectionResults.map((item) => item.leaderId);
    await this.prismaService.parliament_members.deleteMany({
      where: {
        parliamentId,
        memberId: {
          in: allLeaderIds,
        },
      },
    });
    const uniqLeaders = uniqBy(allElectionResults, 'leaderId');
    await this.prismaService.parliament_members.createMany({
      data: uniqLeaders.map((item) => ({
        electionType: ParliamentMemberElectionType.DIRECT,
        memberId: item.leaderId,
        parliamentId,
        memberType: ParliamentMemberType.MEMBER,
        partyId: item.parties.id,
      })),
    });
  }

  async getPopularParliamentarians(
    pagination: PaginationSortSearchDto,
    parliamentId?: number,
  ) {
    const limit = parseInt(pagination.limit as any) || 10;
    const page = parseInt(pagination.page as any) || 1;
    const offset = (page - 1) * limit;

    const parliamentFilter = parliamentId
      ? Prisma.sql`AND p.id = ${parliamentId}`
      : Prisma.empty;

    const resultRaw = await this.prismaService.$queryRaw<
      Array<{
        id: number;
        name: string;
        localName: string;
        img: string | null;
        address: string | null;
        gender: string | null;
        birthDate: Date | null;
        ecCandidateID: string | null;
        parliamentName: string;
        parliamentId: number;
        total_views: bigint;
        average_rating: number | null;
        total_ratings: bigint;
        total_content: bigint;
        leaders_images: string;
      }>
    >(Prisma.sql`
      SELECT
        l.id,
        l.name,
        l.localName,
        l.img,
        l.address,
        l.gender,
        l.birthDate,
        l.ecCandidateID,
        p.name AS parliamentName,
        p.id AS parliamentId,
  
        COALESCE(mv.total_views, 0) AS total_views,
        ROUND(r.avg_rating, 2) AS average_rating,
        COALESCE(r.total_ratings, 0) AS total_ratings,
        COALESCE(c.total_content, 0) AS total_content,
  
        COALESCE(
          JSON_ARRAYAGG(
            JSON_OBJECT('id', li.id, 'url', li.url, 'enabled', li.enabled, 'isDefault', li.isDefault)
          ),
          JSON_ARRAY()
        ) AS leaders_images
  
      FROM parliament_members pm
      JOIN leaders l ON l.id = pm.memberId
      JOIN election_results er ON er.leaderId = l.id
      JOIN parliaments p ON p.id = pm.parliamentId
      LEFT JOIN leaders_images li ON li.leadersId = l.id AND li.enabled = TRUE
  
      LEFT JOIN (
        SELECT resourceId, SUM(views) AS total_views
        FROM most_viewed
        WHERE resourceType = 'leaders'
        GROUP BY resourceId
      ) mv ON mv.resourceId = l.id
  
      LEFT JOIN (
        SELECT rateOnId, AVG(value) AS avg_rating, COUNT(id) AS total_ratings
        FROM ratings
        WHERE rateOnType = 'LEADER'
        GROUP BY rateOnId
      ) r ON r.rateOnId = l.id
  
      LEFT JOIN (
        SELECT resourceId, COUNT(id) AS total_content
        FROM contents
        WHERE resourceType = 'LEADER'
          AND contentType NOT IN ('SUMMARY', 'SUMMARY_NP', 'NEWS')
        GROUP BY resourceId
      ) c ON c.resourceId = l.id
  
      WHERE 1=1
      ${parliamentFilter}
  
      GROUP BY
        l.id,
        l.name,
        l.localName,
        l.img,
        l.address,
        l.gender,
        l.birthDate,
        l.ecCandidateID,
        p.name,
        p.id,
        mv.total_views,
        r.avg_rating,
        r.total_ratings,
        c.total_content
  
      ORDER BY
        total_views DESC,
        average_rating DESC,
        total_content DESC
      LIMIT ${limit} OFFSET ${offset};
    `);

    const data = resultRaw.map((row) => ({
      ...row,
      total_views: Number(row.total_views),
      total_ratings: Number(row.total_ratings),
      total_content: Number(row.total_content),
      leaders_images: row.leaders_images,
    }));

    return {
      items: data.filter(
        (item) =>
          item.total_views + item.total_ratings + item.total_content > 0,
      ),
      page,
      limit,
    };
  }
}
