import {
  <PERSON>,
  Get,
  Param,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ContentsService } from './contents.service';
import { ContentQuery } from './dto/ContentQuery';

@Controller('contents')
export class ContentsController {
  constructor(private readonly contentsService: ContentsService) {}

  @Get('/')
  @UsePipes(
    new ValidationPipe({
      forbidUnknownValues: true,
    }),
  )
  index(@Query() query: ContentQuery) {
    return this.contentsService.getContents(query);
  }

  @Get('/link-preview')
  linkPreview(@Query('url') url: string) {
    return this.contentsService.linkPreview(url);
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number }) {
    return this.contentsService.analytics(query);
  }

  @Get('/:id')
  @UsePipes(
    new ValidationPipe({
      forbidUnknownValues: true,
    }),
  )
  async getOne(@Param('id') contentID: string) {
    try {
      const data = await this.contentsService.getContent(+contentID);
      return data;
    } catch (err) {
      throw err;
    }
  }
}
