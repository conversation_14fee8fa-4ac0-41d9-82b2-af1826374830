import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { ContentQuery } from './dto/ContentQuery';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import {
  CommentableType,
  CONTENT_STATUS,
  CONTENT_TYPE,
  contents,
  RESOURCE_TYPE,
} from '@prisma/client';
import { APIOutput } from 'src/interfaces/ILinkPreviewAPI';
import { getMetadata } from 'src/utils';
import { Readability } from '@mozilla/readability';
import { JSDOM } from 'jsdom';
import axios from 'axios';
import { ISocialMediaContentResponseOptions } from 'src/socialcontentmanager/contents/ISocialMediaContent';
import { capitalize, toUpper, truncate, upperCase } from 'lodash';
import { AiService } from 'src/ai/ai.service';
import { EntityTypeEnum } from 'src/constants';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';

@Injectable()
export class ContentsService {
  constructor(
    private prismaService: PrismaService,
    private entityReview: EntityReview,
    private aiService: AiService,
  ) {}

  async saveContent(content: Partial<contents>) {
    const data = await this.prismaService.contents.upsert({
      where: {
        code: content.code,
      },
      //@ts-expect-error
      create: content,
      update: content,
    });
    return data;
  }

  async getContent(id: number) {
    const contents = await this.entityReview.findAll(
      {
        //@ts-ignore
        model: CommentableType.CONTENT,
        name: 'contents',
      },
      {
        where: {
          id,
          isDeleted: false,
        },
        //@ts-expect-error
        include: {
          childContents: {
            select: {
              id: true,
              resourceType: true,
              resourceId: true,
              contentType: true,
              contentStatus: true,
              title: true,
              code: true,
              content: true,
              eventDate: true,
              childContents: true,
            },
          },
        },
      },
    );
    contents.items = await this.getEnrichedContents(contents.items);
    let item = contents.items?.[0];
    if (!item) return null;
    if (item.cmsLink && !item.content) {
      try {
        const content = await this.saveContentFromCMSLink(item);
        item = {
          ...item,
          ...content,
        };
      } catch (e) {
        console.log(e);
      }
    } else if (!item.summary) {
      try {
        await this.aiService.queueSummaryBuilder(
          {
            id,
            type: EntityTypeEnum.Content,
            content: truncate(item.content, { length: 2000 }),
            code: item.code,
            resourceType: item.resourceType,
          },
          EntityTypeEnum.Content,
        );
      } catch (e) {
        console.log(e);
      }
    }

    if (!item.content) {
      item.content = '';
    }
    item.metadata =
      typeof item.metadata === 'string'
        ? JSON.parse(item.metadata || '{}')
        : item.metadata;

    if (contents.items.length === 0) return null;
    const stats = await this.getStats(1429, contents.items[0].resourceType);
    return { item, stats };
  }
  async getRecentlyAddedContents(options?: ISocialMediaContentResponseOptions) {
    const contents = await this.prismaService.contents.findMany({
      take: options?.limit || 10,
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        isDeleted: false,
        id: { notIn: options?.notIn, in: options?.in },
        contentType: {
          notIn: options?.contentTypeNotIn || [
            CONTENT_TYPE.SUMMARY,
            CONTENT_TYPE.SUMMARY_NP,
          ],
          in: options?.contentTypeIn,
        },
      },
    });
    return this.getEnrichedContents(contents);
  }

  async getContents(filter: ContentQuery) {
    let {
      resourceType,
      resourceId,
      contentType,
      contentStatus,
      ...restFilter
    } = filter;
    const contentTypes = contentType?.split(',') || [];

    if (resourceType === 'ALL' || !resourceType?.length) {
      resourceType = undefined;
    }

    if (contentType === 'ALL') {
      contentType = undefined;
    }

    const contents = await this.entityReview.findAll(
      {
        //@ts-ignore
        model: CommentableType.CONTENT,
        name: 'contents',
      },
      {
        ...restFilter,
        searchFields: ['title', 'content'],
        where: {
          isDeleted: false,
          code: filter.code,
          resourceType: resourceType && toUpper(resourceType),
          contentStatus,
          contentType: contentType
            ? {
                in: contentTypes
                  .map((item) => (item ? item.toUpperCase() : undefined))
                  .filter(Boolean),
              }
            : {
                notIn: ['SUMMARY', 'SUMMARY_NP'],
              },
          resourceId: resourceId && +resourceId,
          content: {
            // not: null
          },
        },
      },
    );
    contents.items = await this.getEnrichedContents(contents.items);
    return contents;
  }

  async getStats(resourceId: number, resourceType: string) {
    const contents = await this.getContents({
      resourceId: resourceId,
      resourceType: resourceType,
      limit: 10000,
    });
    const stats = {};

    // Loop through contents and group them by contentType and contentStatus
    contents.items.forEach(({ contentType, contentStatus }) => {
      if (!stats[contentType]) {
        stats[contentType] = { total: 0, statuses: {} };
      }

      stats[contentType].total += 1;
      stats[contentType].statuses[contentStatus] =
        (stats[contentType].statuses[contentStatus] || 0) + 1;
    });

    // Add percentage breakdown for each contentType
    for (const type in stats) {
      const total = stats[type].total;
      const statuses = stats[type].statuses;

      stats[type].percentages = {};
      for (const status in statuses) {
        const count = statuses[status];
        stats[type].percentages[status] =
          ((count / total) * 100).toFixed(2) + '%';
      }
    }
    delete stats['SUMMARY'];
    delete stats['SUMMARY_NP'];
    return stats;
  }

  async getEnrichedContents(contents: contents[]) {
    // Group contents by resourceType
    const grouped = contents.reduce((acc, content) => {
      if (!acc[content.resourceType]) acc[content.resourceType] = [];
      acc[content.resourceType].push(content.resourceId);
      return acc;
    }, {} as Record<string, number[]>);

    // Load related resources by type
    const [governments, leaders, parties, parliaments, departments, elections] =
      await Promise.all([
        this.prismaService.governments.findMany({
          where: { id: { in: grouped['GOVERNMENT'] || [] } },
        }),
        this.prismaService.leaders.findMany({
          where: { id: { in: grouped['LEADER'] || [] } },
        }),
        this.prismaService.parties.findMany({
          where: { id: { in: grouped['PARTY'] || [] } },
        }),
        this.prismaService.parliaments.findMany({
          where: { id: { in: grouped['PARLIAMENT'] || [] } },
        }),
        this.prismaService.departments.findMany({
          where: { id: { in: grouped['DEPARTMENT'] || [] } },
        }),
        this.prismaService.elections.findMany({
          where: { id: { in: grouped['ELECTION'] || [] } },
        }),
      ]);

    // Maps for fast access
    const resourceMaps = {
      GOVERNMENT: new Map(governments.map((g) => [g.id, g])),
      LEADER: new Map(leaders.map((l) => [l.id, l])),
      PARTY: new Map(parties.map((p) => [p.id, p])),
      PARLIAMENT: new Map(parliaments.map((p) => [p.id, p])),
      DEPARTMENT: new Map(departments.map((d) => [d.id, d])),
      ELECTION: new Map(elections.map((e) => [e.id, e])),
    };

    // Merge related resource into content
    const enriched = contents.map((content) => {
      const resourceMap =
        resourceMaps[content.resourceType as keyof typeof resourceMaps];
      const resource = resourceMap?.get(content.resourceId) ?? null;

      return { ...content, resource };
    });

    return enriched;
  }

  async analytics(
    query: {
      partyId?: number;
      contentTypeIn?: CONTENT_TYPE[];
      onlyCategories?: string[];
    } & PaginationSortSearchDto,
  ) {
    let limit = +query.limit || 10;
    let page = query.page || '1';
    const onlyCategories = query?.onlyCategories || [];

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let news =
      onlyCategories.includes('news') || !onlyCategories.length
        ? await this.prismaService.contents.findMany({
            take: limit,
            orderBy: {
              createdAt: 'desc',
            },
            where: {
              isDeleted: false,
              createdAt: { gt: oneWeekAgo },
              contentType: 'NEWS',
            },
            distinct: ['resourceId'],
          })
        : [];

    news = await this.getEnrichedContents(news);

    let recentlyAdded =
      onlyCategories.includes('recentlyAdded') || !onlyCategories.length
        ? await this.prismaService.contents.findMany({
            take: limit,
            orderBy: {
              createdAt: 'desc',
            },
            where: {
              isDeleted: false,
              createdAt: { gt: oneWeekAgo },
              contentType: query.contentTypeIn
                ? {
                    in: query.contentTypeIn,
                  }
                : undefined,
              NOT: !query.contentTypeIn
                ? {
                    contentType: {
                      in: ['SUMMARY', 'SUMMARY_NP', 'NEWS'],
                    },
                  }
                : undefined,
            },
          })
        : [];

    let most_viewed =
      onlyCategories.includes('most_viewed') || !onlyCategories.length
        ? await this.getContents({
            top: 'views',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };
    let mostLiked =
      onlyCategories.includes('mostLiked') || !onlyCategories.length
        ? await this.getContents({
            top: 'rates',
            limit: limit,
            page,
          })
        : { items: [], totalItems: 0 };

    most_viewed.items = await this.getEnrichedContents(most_viewed.items);
    mostLiked.items = await this.getEnrichedContents(mostLiked.items);
    recentlyAdded = await this.getEnrichedContents(recentlyAdded);

    return {
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
      news: { items: news },
      // most Viewed
      // Most liked
      // Hot items
    };
  }

  async linkPreview(url: string) {
    try {
      url = url.toLowerCase();
      url = url.indexOf('://') === -1 ? 'http://' + url : url;

      const isUrlValid =
        /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi.test(
          url,
        );

      if (!url || !isUrlValid) {
        throw new Error('Invalid URL');
      }

      if (url && isUrlValid) {
        const { hostname } = new URL(url);

        let output: APIOutput;

        const metadata = await getMetadata(url);

        if (!metadata) {
          throw new Error('Failed to fetch metadata');
        }
        const { images, og, meta } = metadata!;

        let image = og.image
          ? og.image
          : images.length > 0
          ? images[0].url
          : `${process.env.NEXT_PUBLIC_SITE_URL}/img-placeholder.jpg`;
        const description = og.description
          ? og.description
          : meta.description
          ? meta.description
          : null;
        const title = (og.title ? og.title : meta.title) || '';
        const siteName = og.site_name || '';

        output = {
          title,
          description,
          image,
          siteName,
          hostname,
        };

        return output;
      }
    } catch (error) {
      console.log(error);
      throw new Error(
        'Internal server error. Please open a Github issue or contact me on Twitter @dhaiwat10 if the issue persists.',
      );
    }
  }

  async getContentFromCMSLink(content: contents) {
    const reponse = await axios.get(content.cmsLink);
    const html = reponse.data;
    const doc = new JSDOM(html);
    let reader = new Readability(doc.window.document);
    let article = reader.parse();
    return article;
  }

  async saveContentFromCMSLink(content: contents) {
    const article = await this.getContentFromCMSLink(content);
    if (!article) throw new Error('Failed to fetch content');

    const linkPreview = await this.linkPreview(content.cmsLink);
    //@ts-expect-error
    const { id, parentContentId, createdAt, rating, resource, ...nextContent } =
      content;
    let aiSummary = null;
    try {
      await this.aiService.queueSummaryBuilder(
        {
          id,
          type: EntityTypeEnum.Content,
          content: truncate(article.textContent, { length: 2000 }),
          code: content.code,
          resourceType: content.resourceType,
        },
        EntityTypeEnum.Content,
      );
    } catch (e) {
      console.log(e);
    }

    const newContent = await this.saveContent({
      code: content.code,
      summary: aiSummary,
      cmsLink: content.cmsLink,
      content: truncate(article.textContent, { length: 2000 }),
      eventDate: article.publishedTime
        ? new Date(article.publishedTime)
        : new Date(),
      title: article.title,
      resourceType: toUpper(content.resourceType) as RESOURCE_TYPE,
      resourceId: content.resourceId,
      contentType: content.contentType,
      contentStatus: content.contentStatus,
      metadata: JSON.stringify({
        [content.cmsLink]: linkPreview,
      }),
    });
    return newContent;
  }

  async getContentsWithCMSLinkAndNoContent(
    options?: ISocialMediaContentResponseOptions,
  ) {
    const contents = await this.prismaService.contents.findMany({
      take: options?.limit || 10,
      orderBy: {
        createdAt: 'desc',
      },
      where: {
        AND: [
          {
            OR: [{ content: null }, { content: '' }],
          },
          {
            cmsLink: { not: null },
          },
        ],
      },
    });

    return contents;
  }
}
