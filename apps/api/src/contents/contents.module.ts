import { Modu<PERSON> } from '@nestjs/common';
import { ContentsService } from './contents.service';
import { ContentsController } from './contents.controller';
import { PrismaService } from 'src/prisma.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { AiService } from 'src/ai/ai.service';

@Module({
  controllers: [ContentsController],
  providers: [ContentsService, PrismaService,
    most_viewedService,
    RatingsService,
    EntityReview, AiService]
})
export class ContentsModule { }
