import { IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { PaginationSortSearchDto } from "src/modules/leaders/dto/index.dto";

export class ContentQuery extends PaginationSortSearchDto {
    @IsString()
    @IsOptional()
    resourceType?: string;

    @IsString()
    @IsOptional()
    resourceId?: number;

    @IsString()
    @IsOptional()
    contentType?: string;

    @IsString()
    @IsOptional()
    contentStatus?: string;

    code?: string
}