import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseWorker } from './basic.worker';
import { QUEUE_SEARCH_RESULT } from 'src/constants';
import { Job } from 'bullmq';
import { AiService } from 'src/ai/ai.service';
import { CONTENT_TYPE, RESOURCE_TYPE } from '@prisma/client';
import { ContentsService } from 'src/contents/contents.service';
import { randomUUID } from 'crypto';

@Injectable()
export class SearchResultWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    private aiService: AiService,
    private contentsService: ContentsService,
  ) {
    super(QUEUE_SEARCH_RESULT, eventEmitter);
    this.workerConfig = {
      concurrency: 1,
      limiter: {
        duration: 60000,
        max: 1,
      },
    };
  }

  async process(
    job: Job<
      {
        payload: {
          resourceType: RESOURCE_TYPE;
          contentType?: CONTENT_TYPE;
          resourceId: number;
          query: string;
          [key: string]: any;
        };
      },
      any,
      string
    >,
  ) {
    const { query, ...options } = job.data.payload;
    const { summary, webSearch } = this.aiService.getNewsFromBrave(query, {
      ...options,
    });
    const webSearchResponse = await webSearch;
    const summarizedAnswer = await summary;

    const formattedResults = webSearchResponse.web.results.map(
      (result: any) => {
        return {
          ...result,
          title: result.title.replace(/<b>|<\/b>/g, ''),
          description: result.description.replace(/<b>|<\/b>/g, ''),
          cmsLink: result.url,
          createdAt: result.page_age,
          source: result.source,
          link: result.url,
        };
      },
    );
    const contents = [];
    for (let i = 0; i < formattedResults.length; i++) {
      const content = await this.contentsService.saveContent({
        resourceId: job.data.payload.resourceId,
        resourceType: job.data.payload.resourceType,
        contentType: job.data.payload.contentType || CONTENT_TYPE.NEWS,
        contentStatus: 'PUBLISHED',
        content: formattedResults[i].description,
        title: formattedResults[i].title,
        code: randomUUID(),
        cmsLink: formattedResults[i].url,
      });
      contents.push(content);
    }

    return { contents: contents.map((item) => item.id) };
  }
}
