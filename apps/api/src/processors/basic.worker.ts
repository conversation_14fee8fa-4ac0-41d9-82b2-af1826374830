import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Job, Worker, WorkerOptions } from 'bullmq';
import redisConfiguration, {
  unregisteredRedisConfig,
} from 'config/redis.configuration';
import * as os from 'os';

export abstract class BaseWorker {
  protected worker: Worker;
  protected logger: Logger;
  protected workerConfig: Omit<WorkerOptions, 'connection'> = {};
  constructor(
    protected queueName: string,
    protected eventEmitter: EventEmitter2,
  ) {
    this.logger = new Logger(queueName);
  }

  startWorker() {
    try {
      this.worker = new Worker(
        this.queueName,
        async (job) => {
          try {
            const response = await this.process(job);
            return response;
          } catch (err) {
            this.logger.error(err);
            throw err;
          }
        },
        {
          name: `${this.queueName}:${os.hostname()}`,
          connection: {
            ...unregisteredRedisConfig.connection,
            retryStrategy: function (times) {
              const retryDelays = [180000, 540000, 3600000]; // 3 min, 9 min, 1 hr

              if (times <= retryDelays.length) {
                return retryDelays[times - 1]; // Return the delay based on retry count
              }

              return null; // Stop retrying after the third attempt
            },
            maxRetriesPerRequest: null, // Fail after 5 retries
            enableOfflineQueue: true, // Fail fast if Redis is offline
          },
          concurrency: 2,
          ...(this.workerConfig || {}),
        },
      );
      this.logger.log(`Started worker for queue ${this.queueName}`);
      this.worker.on('error', this.onError.bind(this));
      this.worker.on('completed', this.onCompleted.bind(this));
      this.worker.on('failed', this.onFailed.bind(this));
      this.worker.on('paused', this.onPaused.bind(this));
      return this.worker;
    } catch (err) {
      this.logger.error(err);
    }
  }

  abstract process(job: Job<unknown>);

  onError(job) {
    this.logger.error('error', job.stack);
  }

  onCompleted(job) {
    this.logger.log(`${this.queueName} Job processed ${job.id} `);
  }

  onPaused() {
    this.logger.log(`${this.queueName} Job paused   `);
  }

  async onFailed(job, error) {
    this.logger.log(job);
    this.logger.error(error);
  }
}
