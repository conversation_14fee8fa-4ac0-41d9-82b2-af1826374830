import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseWorker } from './basic.worker';
import { EntityTypeEnum, QUEUE_AI_IMAGE_FETCHER } from 'src/constants';
import { Job } from 'bullmq';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PrismaService } from 'src/prisma.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { ImageScraper as GoogleImageScraper } from 'src/core/scrapper/GoogleImageScrapper';

export type ImageFetcherPayload = {
  entityId: number;
  entityType: EntityTypeEnum;
  entityName: string;
  keyword: string;
  crawler: string;
};

@Injectable()
export class ImageFetcherWorker extends BaseWorker {
  googleImageScraper: GoogleImageScraper;

  constructor(
    protected eventEmitter: EventEmitter2,
    private prismaService: PrismaService,
    private leaderService: LeadersService,
    private partiesService: PartiesService,
    private governmentsService: GovernmentsService,
    private parliamentsService: ParliamentsService,
    private electionsService: ElectionsService,
    private contentsService: ContentsService,
    private entityReview: EntityReview,
    private aiService: AiService,
    private mostViewedService: most_viewedService,
    private configService: ConfigService,
  ) {
    super(QUEUE_AI_IMAGE_FETCHER, eventEmitter);
    this.workerConfig = {
      removeOnComplete: {
        age: 3600 * 12, // keep up to 1 hour
        count: 1000, // keep up to 1000 jobs
      },
      removeOnFail: {
        age: 24 * 7 * 3600, // keep up to 48 hours
      },

      concurrency: 2,
      limiter: {
        duration: 30000,
        max: 2,
      },
    };
    this.googleImageScraper = new GoogleImageScraper({
      puppeteer: {
        headless: true,
      },
    });
  }

  async process(
    job: Job<
      {
        payload: ImageFetcherPayload;
      },
      any,
      string
    >,
  ): Promise<any> {
    const { entityId, entityType, entityName, keyword, crawler } =
      job.data.payload;
    this.logger.log(
      `Processing image fetch for ${entityType} [ID: ${entityId}, Name: ${entityName}, Keyword: ${keyword}]`,
    );

    try {
      const results = Object.values(
        (await this.googleImageScraper.getImageUrl(`${keyword}`, 10, {
          crawler,
        })) || {},
      ).flat();

      if (!results?.length) {
        this.logger.warn(
          `No images found for ${entityType} ${entityId} (${entityName}) with keyword: ${keyword}`,
        );
        return;
      }

      this.logger.log(
        `Found ${results.length} images for ${entityType} ${entityId} (${entityName})`,
      );

      const randomIndex = Math.floor(Math.random() * results.length);

      // const imageUrl = results[randomIndex].url;
      console.log(results);
      await this.prismaService.$transaction([
        this.prismaService.leaders_images.deleteMany({
          where: {
            id: entityId,
          },
        }),
        this.prismaService.leaders_images.createMany({
          data: results.map((item) => {
            return {
              enabled: false,
              leadersId: entityId,
              url: item.url,
            };
          }),
        }),
      ]);

      this.logger.log(
        `Successfully updated ${entityType} ${entityId} with image:  `,
      );
      return results;
    } catch (error) {
      this.logger.error(
        `Failed to process image fetch for ${entityType} ${entityId}: ${error.message}`,
        error.stack,
      );
    }
  }
}
