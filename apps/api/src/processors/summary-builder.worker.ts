import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseWorker } from './basic.worker';
import { EntityTypeEnum, QUEUE_AI_SUMMARY } from 'src/constants';
import { Job } from 'bullmq';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PrismaService } from 'src/prisma.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { RESOURCE_TYPE } from '@prisma/client';
import { upperCase } from 'lodash';

@Injectable()
export class SummaryBuilderWorker extends BaseWorker {
  constructor(
    protected eventEmitter: EventEmitter2,
    private prismaService: PrismaService,
    private leaderService: LeadersService,
    private partiesService: PartiesService,
    private governmentsService: GovernmentsService,
    private parliamentsService: ParliamentsService,
    private electionsService: ElectionsService,
    private contentsService: ContentsService,
    private entityReview: EntityReview,
    private aiService: AiService,
    private mostViewedService: most_viewedService,
    private configService: ConfigService,
  ) {
    super(QUEUE_AI_SUMMARY, eventEmitter);
    this.workerConfig = {
      concurrency: 1,
      limiter: {
        duration: 60000,
        max: 1,
      },
    };
  }

  async process(
    job: Job<
      {
        payload: {
          id: number;
          type: EntityTypeEnum;
          [key: string]: any;
        };
      },
      any,
      string
    >,
  ): Promise<any> {
    const entityId = job.data.payload.id;
    const entity = job.data.payload.type;
    let result, summary;
    return;
    switch (entity) {
      case EntityTypeEnum.Leader:
        result = await this.leaderService.findOne(job.data.payload.id);
        summary = await this.aiService.generateOverviewSummaryOfLeader(result);
        break;
      case EntityTypeEnum.Party:
        result = await this.partiesService.findOne(job.data.payload.id);
        summary = await this.aiService.generateOverviewSummaryOfParty(result);
        break;
      case EntityTypeEnum.Government:
        result = await this.governmentsService.getOverviewOfGovernment(
          job.data.payload.id,
          null,
        );
        summary = await this.aiService.generateOverviewSummaryOfGovernment(
          result,
        );
        break;
      case EntityTypeEnum.Parliament:
        result = await this.parliamentsService.getParliamentById(
          job.data.payload.id + '',
        );
        summary = await this.aiService.generateOverviewSummaryOfParliament(
          result,
        );
        break;

      case EntityTypeEnum.Content:
        result = job.data.payload.content;
        const content = await this.contentsService.getContent(
          job.data.payload.id,
        );
        if (content.item.cmsLink) {
          console.log(content.item.cmsLink);
          const contentFromCMS =
            await this.contentsService.getContentFromCMSLink(content.item);
          result = contentFromCMS.textContent;
          console.log('Generating summary', contentFromCMS.textContent);
        }
        console.log(result);

        summary = await this.aiService.generateOverviewSummaryOfContent(result);

        await this.contentsService.saveContent({
          resourceId: content.item.resourceId,
          resourceType: content.item.resourceType,
          contentType: content.item.contentType,
          contentStatus: content.item.contentStatus,
          code: content.item.code,
          summary: summary,
        });
        return;
        break;
    }

    const fullSummary = summary;
    const code = `${entityId}-${entity}-SUMMARY`;

    const generatedSummary = await this.aiService.transliterate(fullSummary);
    await this.contentsService.saveContent({
      resourceId: entityId,
      resourceType: upperCase(entity) as RESOURCE_TYPE,
      contentType: 'SUMMARY_NP',
      contentStatus: 'PUBLISHED',
      content: generatedSummary,
      title: 'Summary of ' + result.name,
      code: code + 'np',
    });

    await this.contentsService.saveContent({
      resourceId: entityId,
      resourceType: upperCase(entity) as RESOURCE_TYPE,
      contentType: 'SUMMARY',
      contentStatus: 'PUBLISHED',
      content: fullSummary,
      title: 'Summary of ' + result.name,
      code,
    });
    return summary;
  }
}
