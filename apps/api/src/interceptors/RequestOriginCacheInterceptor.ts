import { CacheInterceptor } from '@nestjs/cache-manager';
import { ExecutionContext, Injectable } from '@nestjs/common';
import { isAppEnvDev } from 'src/utils';

@Injectable()
export class RequestOriginCacheInterceptor extends CacheInterceptor {
  trackBy(context: ExecutionContext): string | undefined {
    const request = context.switchToHttp().getRequest();

    // Check if the environment is development
    if (isAppEnvDev()) {
      return undefined;
    }

    // Read the header value (e.g., 'x-custom-header')

    // Optionally, include request URL or other unique identifiers
    const url = request.url;
    if (request.method !== 'GET') return super.trackBy(context);
    if (url.includes('auth')) return undefined;
    if (url.includes('v1/users')) return undefined;
    // Construct a custom cache key including the header value
    return `cache-key:${url}`;

    // Fallback to the default behavior if the header is not present
    return super.trackBy(context);
  }
}
