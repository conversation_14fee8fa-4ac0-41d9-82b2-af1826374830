import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { RESOURCE_TYPE, POLL_CREATED_BY, Prisma } from '@prisma/client';
import { randomBytes } from 'crypto';
import { PrismaService } from 'src/prisma.service';
import { POLL_TEMPLATES } from './poll-templates/poll-templates.constant';

@Injectable()
export class SystemPollsService {
  private readonly logger = new Logger(SystemPollsService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Get or create system polls for a specific resource
   */
  async getOrCreateSystemPolls(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
    validateResource: boolean = true,
  ) {
    try {
      // Validate resource exists if requested
      if (validateResource) {
        await this.validateResource(resourceId, resourceType);
      }

      // First, check if polls already exist for this resource
      const existingPolls = await this.prisma.polls.findMany({
        where: {
          resourceId,
          resourceType,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
        include: {
          options: {
            include: {
              _count: {
                select: { responses: true },
              },
            },
          },
          _count: {
            select: { responses: true },
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      // If polls exist, return them
      if (existingPolls.length > 0) {
        this.logger.debug(
          `Found ${existingPolls.length} existing polls for ${resourceType}:${resourceId}`,
        );
        return this.formatPollsResponse(existingPolls);
      }

      // If no polls exist, create them
      this.logger.log(
        `Creating system polls for ${resourceType}:${resourceId}`,
      );
      const createdPolls = await this.createSystemPollsForResource(
        resourceId,
        resourceType,
      );

      return this.formatPollsResponse(createdPolls);
    } catch (error) {
      this.logger.error(
        `Error getting/creating system polls for ${resourceType}:${resourceId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create system polls for a specific resource
   */
  private async createSystemPollsForResource(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ) {
    const templates = POLL_TEMPLATES.find(
      (template) => template.resourceType === resourceType,
    );

    if (!templates) {
      this.logger.warn(
        `No poll templates found for resource type: ${resourceType}`,
      );
      return [];
    }

    const createdPolls = [];

    // Use transaction to ensure all polls are created or none
    await this.prisma.$transaction(async (tx) => {
      // Create standard template polls
      for (const template of templates.templates) {
        try {
          const poll = await tx.polls.create({
            data: {
              resourceId,
              resourceType,
              question: template.question,
              questionLocal: template.questionLocal,
              title: template.title,
              titleLocal: template.titleLocal,
              description: template.description,
              descriptionLocal: template.descriptionLocal,
              type: template.type,
              createdBy: POLL_CREATED_BY.SYSTEM,
              hash: this.generateUniqueHash(),
              code: this.generatePollCode(
                resourceType,
                template.code,
                resourceId,
              ),
              options: {
                create: template.options.map((option) => ({
                  text: option.text,
                  textLocal: option.textLocal,
                  value: option.value,
                })),
              },
            },
            include: {
              options: {
                include: {
                  _count: {
                    select: { responses: true },
                  },
                },
              },
              _count: {
                select: { responses: true },
              },
            },
          });

          // Initialize vote counts for each option
          await this.initializePollVoteCounts(tx, poll.id, poll.options);

          createdPolls.push(poll);
          this.logger.debug(
            `Created poll: ${poll.title} for ${resourceType}:${resourceId}`,
          );
        } catch (error) {
          this.logger.error(`Error creating poll: ${template.title}`, error);
          throw error; // Fail the entire transaction if one poll fails
        }
      }

      // Create dynamic polls based on resource relationships
      const dynamicPolls = await this.createDynamicPolls(
        tx,
        resourceId,
        resourceType,
      );
      createdPolls.push(...dynamicPolls);
    });

    this.logger.log(
      `Created ${createdPolls.length} system polls (including dynamic) for ${resourceType}:${resourceId}`,
    );
    return createdPolls;
  }

  /**
   * Get system polls with user response status
   */
  async getSystemPollsWithUserStatus(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
    userId?: number,
  ) {
    const polls = await this.getOrCreateSystemPolls(resourceId, resourceType);

    if (!userId) {
      return polls;
    }

    // Get user responses for all polls
    const userResponses = await this.prisma.poll_responses.findMany({
      where: {
        userId,
        poll: {
          resourceId,
          resourceType,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
      },
      include: {
        option: true,
        poll: true,
      },
    });

    // Map user responses by poll ID
    const responsesByPoll = userResponses.reduce((acc, response) => {
      if (!acc[response.pollId]) {
        acc[response.pollId] = [];
      }
      acc[response.pollId].push(response.option);
      return acc;
    }, {} as Record<number, any[]>);

    // Add user response status to polls
    return polls.map((poll) => ({
      ...poll,
      userResponse: responsesByPoll[poll.id] || null,
      hasUserResponded: !!responsesByPoll[poll.id],
    }));
  }

  /**
   * Get aggregated results for all system polls of a resource
   */
  async getSystemPollsResults(resourceId: number, resourceType: RESOURCE_TYPE) {
    const polls = await this.prisma.polls.findMany({
      where: {
        resourceId,
        resourceType,
        createdBy: POLL_CREATED_BY.SYSTEM,
        isDeleted: false,
      },
      include: {
        options: {
          include: {
            _count: {
              select: { responses: true },
            },
          },
        },
        _count: {
          select: { responses: true },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    return polls.map((poll) => {
      const totalResponses = poll._count.responses;
      const options = poll.options.map((option) => ({
        ...option,
        votes: option._count.responses,
        percentage:
          totalResponses > 0
            ? Math.round((option._count.responses / totalResponses) * 100)
            : 0,
      }));

      return {
        id: poll.id,
        question: poll.question,
        questionLocal: poll.questionLocal,
        title: poll.title,
        titleLocal: poll.titleLocal,
        type: poll.type,
        totalResponses,
        options,
      };
    });
  }

  /**
   * Regenerate system polls for a resource (useful for updates)
   */
  async regenerateSystemPolls(resourceId: number, resourceType: RESOURCE_TYPE) {
    this.logger.log(
      `Regenerating system polls for ${resourceType}:${resourceId}`,
    );

    // Soft delete existing polls
    await this.prisma.polls.updateMany({
      where: {
        resourceId,
        resourceType,
        createdBy: POLL_CREATED_BY.SYSTEM,
      },
      data: {
        isDeleted: true,
      },
    });

    // Create new polls
    return this.createSystemPollsForResource(resourceId, resourceType);
  }

  /**
   * Format polls response with calculated statistics
   */
  private formatPollsResponse(polls: any[]) {
    return polls.map((poll) => {
      const totalResponses = poll._count.responses;
      const options = poll.options.map((option: any) => ({
        ...option,
        votes: option._count.responses,
        percentage:
          totalResponses > 0
            ? Math.round((option._count.responses / totalResponses) * 100)
            : 0,
      }));

      return {
        ...poll,
        options,
        totalResponses,
      };
    });
  }

  /**
   * Get available poll templates for a resource type
   */
  getAvailableTemplates(resourceType: RESOURCE_TYPE) {
    const templates = POLL_TEMPLATES.find(
      (template) => template.resourceType === resourceType,
    );
    return templates ? templates.templates : [];
  }

  /**
   * Check if system polls exist for a resource
   */
  async hasSystemPolls(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ): Promise<boolean> {
    const count = await this.prisma.polls.count({
      where: {
        resourceId,
        resourceType,
        createdBy: POLL_CREATED_BY.SYSTEM,
        isDeleted: false,
      },
    });

    return count > 0;
  }

  /**
   * Create dynamic polls based on resource relationships and data
   */
  private async createDynamicPolls(
    tx: Prisma.TransactionClient,
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ) {
    const dynamicPolls = [];

    try {
      switch (resourceType) {
        case RESOURCE_TYPE.LEADER:
          const leaderPolls = await this.createLeaderDynamicPolls(
            tx,
            resourceId,
          );
          dynamicPolls.push(...leaderPolls);
          break;

        case RESOURCE_TYPE.DEPARTMENT:
          const departmentPolls = await this.createDepartmentDynamicPolls(
            tx,
            resourceId,
          );
          dynamicPolls.push(...departmentPolls);
          break;

        case RESOURCE_TYPE.PARLIAMENT:
          const parliamentPolls = await this.createParliamentDynamicPolls(
            tx,
            resourceId,
          );
          dynamicPolls.push(...parliamentPolls);
          break;

        case RESOURCE_TYPE.GOVERNMENT:
          const governmentPolls = await this.createGovernmentDynamicPolls(
            tx,
            resourceId,
          );
          dynamicPolls.push(...governmentPolls);
          break;

        case RESOURCE_TYPE.PARTY:
          const partyPolls = await this.createPartyDynamicPolls(tx, resourceId);
          dynamicPolls.push(...partyPolls);
          break;

        default:
          this.logger.debug(`No dynamic polls defined for ${resourceType}`);
      }

      this.logger.debug(
        `Created ${dynamicPolls.length} dynamic polls for ${resourceType}:${resourceId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error creating dynamic polls for ${resourceType}:${resourceId}`,
        error,
      );
      // Don't throw error, just log it and continue
    }

    return dynamicPolls;
  }

  /**
   * Create dynamic polls for leaders based on their roles and tenures
   */
  private async createLeaderDynamicPolls(
    tx: Prisma.TransactionClient,
    leaderId: number,
  ) {
    const dynamicPolls = [];

    // Get leader's cabinet member history
    const cabinetTenures = await this.prisma.cabinet_members.findMany({
      where: { leaderId },
      include: {
        department: true,
        governments: true,
      },
    });

    // Create polls for each cabinet tenure
    for (const tenure of cabinetTenures) {
      const tenurePolls = await this.createCabinetTenurePolls(
        tx,
        leaderId,
        tenure,
      );
      dynamicPolls.push(...tenurePolls);
    }

    // Get leader's parliament member history
    const parliamentTenures = await this.prisma.parliament_members.findMany({
      where: { memberId: leaderId },
      include: {
        parliament: true,
      },
    });

    // Create polls for each parliament tenure
    for (const tenure of parliamentTenures) {
      const tenurePolls = await this.createParliamentTenurePolls(
        tx,
        leaderId,
        tenure,
      );
      dynamicPolls.push(...tenurePolls);
    }

    // Get leader's party leadership history
    const partyLeadershipTenures = await this.prisma.party_leaders.findMany({
      where: { leaderId },
      include: {
        party: true,
      },
    });

    // Create polls for party leadership
    for (const tenure of partyLeadershipTenures) {
      const leadershipPolls = await this.createPartyLeadershipPolls(
        tx,
        leaderId,
        tenure,
      );
      dynamicPolls.push(...leadershipPolls);
    }

    return dynamicPolls;
  }

  /**
   * Generate unique hash for poll
   */
  private generateUniqueHash(): string {
    return randomBytes(16).toString('hex');
  }

  /**
   * Create cabinet tenure polls for a leader
   */
  private async createCabinetTenurePolls(
    tx: Prisma.TransactionClient,
    leaderId: number,
    tenure: any,
  ) {
    const polls = [];
    const departmentName =
      tenure.department?.name || tenure.portfolioTitle || 'Cabinet';
    const governmentName = tenure.governments?.name || 'Government';

    const tenurePolls = [
      {
        code: `CABINET_PERFORMANCE_${tenure.id}`,
        question: `How would you rate ${departmentName} performance under this leader?`,
        questionLocal: `यस नेताको नेतृत्वमा ${departmentName} को प्रदर्शनलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?`,
        title: `${departmentName} Performance`,
        titleLocal: `${departmentName} प्रदर्शन`,
        description: `Rate the performance during tenure in ${departmentName}`,
        descriptionLocal: `${departmentName} मा कार्यकालको दौरानको प्रदर्शनको मूल्याङ्कन गर्नुहोस्`,
        type: 'RADIO' as any,
        options: this.getRatingOptions(),
      },
      {
        code: `CABINET_POLICY_${tenure.id}`,
        question: `Were the policies implemented during this tenure effective?`,
        questionLocal: `यस कार्यकालमा लागू गरिएका नीतिहरू प्रभावकारी थिए?`,
        title: `Policy Effectiveness`,
        titleLocal: `नीति प्रभावकारिता`,
        description: `Evaluate policy effectiveness during cabinet tenure`,
        descriptionLocal: `मन्त्रिपरिषद्को कार्यकालमा नीति प्रभावकारिताको मूल्याङ्कन गर्नुहोस्`,
        type: 'RADIO' as any,
        options: this.getRatingOptions(),
      },
    ];

    for (const pollTemplate of tenurePolls) {
      const poll = await tx.polls.create({
        data: {
          resourceId: leaderId,
          resourceType: RESOURCE_TYPE.LEADER,
          question: pollTemplate.question,
          questionLocal: pollTemplate.questionLocal,
          title: pollTemplate.title,
          titleLocal: pollTemplate.titleLocal,
          description: pollTemplate.description,
          descriptionLocal: pollTemplate.descriptionLocal,
          type: pollTemplate.type,
          createdBy: POLL_CREATED_BY.SYSTEM,
          hash: this.generateUniqueHash(),
          code: this.generatePollCode(
            RESOURCE_TYPE.LEADER,
            pollTemplate.code,
            leaderId,
          ),
          options: {
            create: pollTemplate.options,
          },
        },
        include: {
          options: {
            include: {
              _count: {
                select: { responses: true },
              },
            },
          },
          _count: {
            select: { responses: true },
          },
        },
      });

      await this.initializePollVoteCounts(tx, poll.id, poll.options);
      polls.push(poll);
    }

    return polls;
  }

  /**
   * Create parliament tenure polls for a leader
   */
  private async createParliamentTenurePolls(
    tx: Prisma.TransactionClient,
    leaderId: number,
    tenure: any,
  ) {
    const polls = [];
    const parliamentName = tenure.parliament?.name || 'Parliament';

    const tenurePolls = [
      {
        code: `PARLIAMENT_ATTENDANCE_${tenure.id}`,
        question: `How would you rate this leader's parliament attendance?`,
        questionLocal: `यस नेताको संसदमा उपस्थितिलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?`,
        title: `Parliament Attendance`,
        titleLocal: `संसदीय उपस्थिति`,
        description: `Rate attendance during parliament tenure`,
        descriptionLocal: `संसदीय कार्यकालमा उपस्थितिको मूल्याङ्कन गर्नुहोस्`,
        type: 'RADIO' as any,
        options: this.getRatingOptions(),
      },
      {
        code: `PARLIAMENT_CONTRIBUTION_${tenure.id}`,
        question: `How effective was this leader's contribution in parliament?`,
        questionLocal: `संसदमा यस नेताको योगदान कत्तिको प्रभावकारी थियो?`,
        title: `Parliamentary Contribution`,
        titleLocal: `संसदीय योगदान`,
        description: `Evaluate contribution during parliament tenure`,
        descriptionLocal: `संसदीय कार्यकालमा योगदानको मूल्याङ्कन गर्नुहोस्`,
        type: 'RADIO' as any,
        options: this.getRatingOptions(),
      },
    ];

    for (const pollTemplate of tenurePolls) {
      const poll = await tx.polls.create({
        data: {
          resourceId: leaderId,
          resourceType: RESOURCE_TYPE.LEADER,
          question: pollTemplate.question,
          questionLocal: pollTemplate.questionLocal,
          title: pollTemplate.title,
          titleLocal: pollTemplate.titleLocal,
          description: pollTemplate.description,
          descriptionLocal: pollTemplate.descriptionLocal,
          type: pollTemplate.type,
          createdBy: POLL_CREATED_BY.SYSTEM,
          hash: this.generateUniqueHash(),
          code: this.generatePollCode(
            RESOURCE_TYPE.LEADER,
            pollTemplate.code,
            leaderId,
          ),
          options: {
            create: pollTemplate.options,
          },
        },
        include: {
          options: {
            include: {
              _count: {
                select: { responses: true },
              },
            },
          },
          _count: {
            select: { responses: true },
          },
        },
      });

      await this.initializePollVoteCounts(tx, poll.id, poll.options);
      polls.push(poll);
    }

    return polls;
  }

  /**
   * Create party leadership polls for a leader
   */
  private async createPartyLeadershipPolls(
    tx: Prisma.TransactionClient,
    leaderId: number,
    tenure: any,
  ) {
    const polls = [];
    const partyName = tenure.party?.name || 'Party';

    const leadershipPolls = [
      {
        code: `PARTY_LEADERSHIP_${tenure.id}`,
        question: `How would you rate this leader's performance as ${partyName} leader?`,
        questionLocal: `${partyName} नेताको रूपमा यस नेताको प्रदर्शनलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?`,
        title: `${partyName} Leadership`,
        titleLocal: `${partyName} नेतृत्व`,
        description: `Rate leadership performance in ${partyName}`,
        descriptionLocal: `${partyName} मा नेतृत्व प्रदर्शनको मूल्याङ्कन गर्नुहोस्`,
        type: 'RADIO' as any,
        options: this.getRatingOptions(),
      },
    ];

    for (const pollTemplate of leadershipPolls) {
      const poll = await tx.polls.create({
        data: {
          resourceId: leaderId,
          resourceType: RESOURCE_TYPE.LEADER,
          question: pollTemplate.question,
          questionLocal: pollTemplate.questionLocal,
          title: pollTemplate.title,
          titleLocal: pollTemplate.titleLocal,
          description: pollTemplate.description,
          descriptionLocal: pollTemplate.descriptionLocal,
          type: pollTemplate.type,
          createdBy: POLL_CREATED_BY.SYSTEM,
          hash: this.generateUniqueHash(),
          code: this.generatePollCode(
            RESOURCE_TYPE.LEADER,
            pollTemplate.code,
            leaderId,
          ),
          options: {
            create: pollTemplate.options,
          },
        },
        include: {
          options: {
            include: {
              _count: {
                select: { responses: true },
              },
            },
          },
          _count: {
            select: { responses: true },
          },
        },
      });

      await this.initializePollVoteCounts(tx, poll.id, poll.options);
      polls.push(poll);
    }

    return polls;
  }

  /**
   * Create dynamic polls for departments
   */
  private async createDepartmentDynamicPolls(
    tx: Prisma.TransactionClient,
    departmentId: number,
  ) {
    const polls = [];

    // Get all ministers who served in this department
    const ministers = await this.prisma.cabinet_members.findMany({
      where: { departmentId },
      include: {
        leaders: true,
        governments: true,
      },
      distinct: ['leaderId'],
    });

    if (ministers.length > 1) {
      // Create comparison polls only if there are multiple ministers
      const ministerNames = ministers
        .filter((m) => m.leaders)
        .map((m) => m.leaders!.localName || m.leaders!.name)
        .slice(0, 4); // Limit to 4 options

      if (ministerNames.length > 1) {
        const comparisonPoll = {
          code: `DEPT_MINISTER_COMPARISON_${departmentId}`,
          question: `Which minister performed best in this department?`,
          questionLocal: `यस विभागमा कुन मन्त्रीले सबैभन्दा राम्रो प्रदर्शन गरे?`,
          title: `Best Minister Comparison`,
          titleLocal: `उत्कृष्ट मन्त्री तुलना`,
          description: `Compare ministers who served in this department`,
          descriptionLocal: `यस विभागमा सेवा गरेका मन्त्रीहरूको तुलना गर्नुहोस्`,
          type: 'RADIO' as any,
          options: ministerNames.map((name) => ({
            text: name,
            textLocal: name,
            value: name.toLowerCase().replace(/\s+/g, '_'),
          })),
        };

        const poll = await tx.polls.create({
          data: {
            resourceId: departmentId,
            resourceType: RESOURCE_TYPE.DEPARTMENT,
            question: comparisonPoll.question,
            questionLocal: comparisonPoll.questionLocal,
            title: comparisonPoll.title,
            titleLocal: comparisonPoll.titleLocal,
            description: comparisonPoll.description,
            descriptionLocal: comparisonPoll.descriptionLocal,
            type: comparisonPoll.type,
            createdBy: POLL_CREATED_BY.SYSTEM,
            hash: this.generateUniqueHash(),
            code: this.generatePollCode(
              RESOURCE_TYPE.DEPARTMENT,
              comparisonPoll.code,
              departmentId,
            ),
            options: {
              create: comparisonPoll.options,
            },
          },
          include: {
            options: {
              include: {
                _count: {
                  select: { responses: true },
                },
              },
            },
            _count: {
              select: { responses: true },
            },
          },
        });

        await this.initializePollVoteCounts(tx, poll.id, poll.options);
        polls.push(poll);
      }
    }

    return polls;
  }

  /**
   * Create dynamic polls for parliaments
   */
  private async createParliamentDynamicPolls(
    tx: Prisma.TransactionClient,
    parliamentId: number,
  ) {
    const polls = [];

    // Get all members of this parliament
    const members = await this.prisma.parliament_members.findMany({
      where: { parliamentId },
      include: {
        member: true,
        party: true,
      },
      distinct: ['memberId'],
    });

    if (members.length > 1) {
      // Create polls about most effective member
      const memberNames = members
        .filter((m) => m.member)
        .map((m) => m.member!.localName || m.member!.name)
        .slice(0, 5); // Limit to 5 options

      if (memberNames.length > 1) {
        const effectiveMemberPoll = {
          code: `PARLIAMENT_EFFECTIVE_MEMBER_${parliamentId}`,
          question: `Who was the most effective member in this parliament?`,
          questionLocal: `यस संसदमा सबैभन्दा प्रभावकारी सदस्य को थिए?`,
          title: `Most Effective Member`,
          titleLocal: `सबैभन्दा प्रभावकारी सदस्य`,
          description: `Vote for the most effective parliament member`,
          descriptionLocal: `सबैभन्दा प्रभावकारी संसद सदस्यको लागि मतदान गर्नुहोस्`,
          type: 'RADIO' as any,
          options: memberNames.map((name) => ({
            text: name,
            textLocal: name,
            value: name.toLowerCase().replace(/\s+/g, '_'),
          })),
        };

        const poll = await tx.polls.create({
          data: {
            resourceId: parliamentId,
            resourceType: RESOURCE_TYPE.PARLIAMENT,
            question: effectiveMemberPoll.question,
            questionLocal: effectiveMemberPoll.questionLocal,
            title: effectiveMemberPoll.title,
            titleLocal: effectiveMemberPoll.titleLocal,
            description: effectiveMemberPoll.description,
            descriptionLocal: effectiveMemberPoll.descriptionLocal,
            type: effectiveMemberPoll.type,
            createdBy: POLL_CREATED_BY.SYSTEM,
            hash: this.generateUniqueHash(),
            code: this.generatePollCode(
              RESOURCE_TYPE.PARLIAMENT,
              effectiveMemberPoll.code,
              parliamentId,
            ),
            options: {
              create: effectiveMemberPoll.options,
            },
          },
          include: {
            options: {
              include: {
                _count: {
                  select: { responses: true },
                },
              },
            },
            _count: {
              select: { responses: true },
            },
          },
        });

        await this.initializePollVoteCounts(tx, poll.id, poll.options);
        polls.push(poll);
      }
    }

    return polls;
  }

  /**
   * Create dynamic polls for governments
   */
  private async createGovernmentDynamicPolls(
    tx: Prisma.TransactionClient,
    governmentId: number,
  ) {
    const polls = [];

    // Get cabinet members of this government
    const cabinetMembers = await this.prisma.cabinet_members.findMany({
      where: { governmentId },
      include: {
        leaders: true,
        department: true,
      },
    });

    if (cabinetMembers.length > 1) {
      // Create poll for best performing minister
      const ministerNames = cabinetMembers
        .filter((m) => m.leaders)
        .map((m) => m.leaders!.localName || m.leaders!.name)
        .slice(0, 6); // Limit to 6 options

      if (ministerNames.length > 1) {
        const bestMinisterPoll = {
          code: `GOVT_BEST_MINISTER_${governmentId}`,
          question: `Who was the best performing minister in this government?`,
          questionLocal: `यस सरकारमा सबैभन्दा राम्रो प्रदर्शन गर्ने मन्त्री को थिए?`,
          title: `Best Performing Minister`,
          titleLocal: `उत्कृष्ट प्रदर्शन गर्ने मन्त्री`,
          description: `Vote for the best performing minister`,
          descriptionLocal: `उत्कृष्ट प्रदर्शन गर्ने मन्त्रीको लागि मतदान गर्नुहोस्`,
          type: 'RADIO' as any,
          options: ministerNames.map((name) => ({
            text: name,
            textLocal: name,
            value: name.toLowerCase().replace(/\s+/g, '_'),
          })),
        };

        const poll = await tx.polls.create({
          data: {
            resourceId: governmentId,
            resourceType: RESOURCE_TYPE.GOVERNMENT,
            question: bestMinisterPoll.question,
            questionLocal: bestMinisterPoll.questionLocal,
            title: bestMinisterPoll.title,
            titleLocal: bestMinisterPoll.titleLocal,
            description: bestMinisterPoll.description,
            descriptionLocal: bestMinisterPoll.descriptionLocal,
            type: bestMinisterPoll.type,
            createdBy: POLL_CREATED_BY.SYSTEM,
            hash: this.generateUniqueHash(),
            code: this.generatePollCode(
              RESOURCE_TYPE.GOVERNMENT,
              bestMinisterPoll.code,
              governmentId,
            ),
            options: {
              create: bestMinisterPoll.options,
            },
          },
          include: {
            options: {
              include: {
                _count: {
                  select: { responses: true },
                },
              },
            },
            _count: {
              select: { responses: true },
            },
          },
        });

        await this.initializePollVoteCounts(tx, poll.id, poll.options);
        polls.push(poll);
      }
    }

    return polls;
  }

  /**
   * Create dynamic polls for parties
   */
  private async createPartyDynamicPolls(
    tx: Prisma.TransactionClient,
    partyId: number,
  ) {
    const polls = [];

    // Get party leaders history
    const partyLeaders = await this.prisma.party_leaders.findMany({
      where: { partyId },
      include: {
        leader: true,
      },
      distinct: ['leaderId'],
    });

    if (partyLeaders.length > 1) {
      // Create poll for best party leader
      const leaderNames = partyLeaders
        .filter((pl) => pl.leader)
        .map((pl) => pl.leader!.localName || pl.leader!.name)
        .slice(0, 5); // Limit to 5 options

      if (leaderNames.length > 1) {
        const bestLeaderPoll = {
          code: `PARTY_BEST_LEADER_${partyId}`,
          question: `Who was the best leader of this party?`,
          questionLocal: `यस पार्टीको सबैभन्दा राम्रो नेता को थिए?`,
          title: `Best Party Leader`,
          titleLocal: `उत्कृष्ट पार्टी नेता`,
          description: `Vote for the best leader of this party`,
          descriptionLocal: `यस पार्टीको उत्कृष्ट नेताको लागि मतदान गर्नुहोस्`,
          type: 'RADIO' as any,
          options: leaderNames.map((name) => ({
            text: name,
            textLocal: name,
            value: name.toLowerCase().replace(/\s+/g, '_'),
          })),
        };

        const poll = await tx.polls.create({
          data: {
            resourceId: partyId,
            resourceType: RESOURCE_TYPE.PARTY,
            question: bestLeaderPoll.question,
            questionLocal: bestLeaderPoll.questionLocal,
            title: bestLeaderPoll.title,
            titleLocal: bestLeaderPoll.titleLocal,
            description: bestLeaderPoll.description,
            descriptionLocal: bestLeaderPoll.descriptionLocal,
            type: bestLeaderPoll.type,
            createdBy: POLL_CREATED_BY.SYSTEM,
            hash: this.generateUniqueHash(),
            code: this.generatePollCode(
              RESOURCE_TYPE.PARTY,
              bestLeaderPoll.code,
              partyId,
            ),
            options: {
              create: bestLeaderPoll.options,
            },
          },
          include: {
            options: {
              include: {
                _count: {
                  select: { responses: true },
                },
              },
            },
            _count: {
              select: { responses: true },
            },
          },
        });

        await this.initializePollVoteCounts(tx, poll.id, poll.options);
        polls.push(poll);
      }
    }

    return polls;
  }

  /**
   * Get rating options for polls
   */
  private getRatingOptions() {
    return [
      { text: 'Excellent', textLocal: 'उत्कृष्ट', value: 'excellent' },
      { text: 'Good', textLocal: 'राम्रो', value: 'good' },
      { text: 'Fair', textLocal: 'ठीक', value: 'fair' },
      { text: 'Poor', textLocal: 'खराब', value: 'poor' },
      { text: 'Very Poor', textLocal: 'धेरै खराब', value: 'very_poor' },
    ];
  }

  /**
   * Get satisfaction options for polls
   */
  private getSatisfactionOptions() {
    return [
      {
        text: 'Very Satisfied',
        textLocal: 'धेरै सन्तुष्ट',
        value: 'very_satisfied',
      },
      { text: 'Satisfied', textLocal: 'सन्तुष्ट', value: 'satisfied' },
      { text: 'Neutral', textLocal: 'तटस्थ', value: 'neutral' },
      { text: 'Dissatisfied', textLocal: 'असन्तुष्ट', value: 'dissatisfied' },
      {
        text: 'Very Dissatisfied',
        textLocal: 'धेरै असन्तुष्ट',
        value: 'very_dissatisfied',
      },
    ];
  }

  /**
   * Get standard options for polls
   */
  private getStandardOptions() {
    return [
      { text: 'Yes', textLocal: 'हो', value: 'yes' },
      { text: 'No', textLocal: 'होइन', value: 'no' },
      { text: 'Neutral', textLocal: 'तटस्थ', value: 'neutral' },
    ];
  }

  /**
   * Generate poll code based on resource type and template code
   */
  private generatePollCode(
    resourceType: RESOURCE_TYPE,
    templateCode: string,
    resourceId: number,
  ): string {
    return `${resourceType}_${templateCode}_${resourceId}_${Date.now()}`;
  }

  /**
   * Initialize poll vote counts for all options
   */
  private async initializePollVoteCounts(
    tx: Prisma.TransactionClient,
    pollId: number,
    options: any[],
  ) {
    const voteCounts = options.map((option) => ({
      pollId,
      optionId: option.id,
      count: 0,
      percentage: 0,
    }));

    await tx.poll_vote_counts.createMany({
      data: voteCounts,
    });
  }

  /**
   * Update poll vote counts after a response is added/removed
   */
  async updatePollVoteCounts(pollId: number) {
    try {
      // Get all responses for this poll
      const responses = await this.prisma.poll_responses.groupBy({
        by: ['optionId'],
        where: { pollId },
        _count: { optionId: true },
      });

      // Get total responses for percentage calculation
      const totalResponses = await this.prisma.poll_responses.count({
        where: { pollId },
      });

      // Update vote counts for each option
      for (const response of responses) {
        const percentage =
          totalResponses > 0
            ? Math.round((response._count.optionId / totalResponses) * 100)
            : 0;

        await this.prisma.poll_vote_counts.upsert({
          where: {
            pollId_optionId: {
              pollId,
              optionId: response.optionId,
            },
          },
          update: {
            count: response._count.optionId,
            percentage,
          },
          create: {
            pollId,
            optionId: response.optionId,
            count: response._count.optionId,
            percentage,
          },
        });
      }

      // Reset counts for options with no responses
      const optionsWithResponses = responses.map((r) => r.optionId);
      const allOptions = await this.prisma.poll_options.findMany({
        where: { pollId },
        select: { id: true },
      });

      const optionsWithoutResponses = allOptions
        .filter((option) => !optionsWithResponses.includes(option.id))
        .map((option) => option.id);

      if (optionsWithoutResponses.length > 0) {
        await this.prisma.poll_vote_counts.updateMany({
          where: {
            pollId,
            optionId: { in: optionsWithoutResponses },
          },
          data: {
            count: 0,
            percentage: 0,
          },
        });
      }

      this.logger.debug(`Updated vote counts for poll ${pollId}`);
    } catch (error) {
      this.logger.error(`Error updating vote counts for poll ${pollId}`, error);
      throw error;
    }
  }

  /**
   * Get poll statistics for analytics
   */
  async getPollStatistics(resourceId: number, resourceType: RESOURCE_TYPE) {
    const polls = await this.prisma.polls.findMany({
      where: {
        resourceId,
        resourceType,
        createdBy: POLL_CREATED_BY.SYSTEM,
        isDeleted: false,
      },
      include: {
        _count: {
          select: { responses: true },
        },
      },
    });

    const totalPolls = polls.length;
    const totalResponses = polls.reduce(
      (sum, poll) => sum + poll._count.responses,
      0,
    );
    const averageResponsesPerPoll =
      totalPolls > 0 ? Number((totalResponses / totalPolls).toFixed(2)) : 0;
    console.log(totalPolls, totalResponses, totalPolls);
    return {
      totalPolls,
      totalResponses,
      averageResponsesPerPoll,
      pollsWithResponses: polls.filter((poll) => poll._count.responses > 0)
        .length,
      pollsWithoutResponses: polls.filter((poll) => poll._count.responses === 0)
        .length,
    };
  }

  /**
   * Get trending polls (most responded to)
   */
  async getTrendingPolls(resourceType?: RESOURCE_TYPE, limit: number = 10) {
    const whereClause: any = {
      createdBy: POLL_CREATED_BY.SYSTEM,
      isDeleted: false,
    };

    if (resourceType) {
      whereClause.resourceType = resourceType;
    }

    return this.prisma.polls.findMany({
      where: whereClause,
      include: {
        _count: {
          select: { responses: true },
        },
        options: {
          include: {
            _count: {
              select: { responses: true },
            },
          },
        },
      },
      orderBy: {
        responses: {
          _count: 'desc',
        },
      },
      take: limit,
    });
  }

  /**
   * Force create system polls for a resource (even if they already exist)
   */
  async forceCreateSystemPolls(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ) {
    this.logger.log(
      `Force creating system polls for ${resourceType}:${resourceId}`,
    );

    try {
      // Validate resource exists
      await this.validateResource(resourceId, resourceType);

      // Check if polls already exist
      const existingPolls = await this.prisma.polls.findMany({
        where: {
          resourceId,
          resourceType,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
      });

      if (existingPolls.length > 0) {
        throw new BadRequestException(
          `System polls already exist for ${resourceType}:${resourceId}. Use regenerateSystemPolls to recreate them.`,
        );
      }

      // Create new polls
      const createdPolls = await this.createSystemPollsForResource(
        resourceId,
        resourceType,
      );

      return this.formatPollsResponse(createdPolls);
    } catch (error) {
      this.logger.error(
        `Error force creating system polls for ${resourceType}:${resourceId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check if system polls exist for a resource
   */
  async systemPollsExist(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ): Promise<boolean> {
    try {
      const count = await this.prisma.polls.count({
        where: {
          resourceId,
          resourceType,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
      });

      return count > 0;
    } catch (error) {
      this.logger.error(
        `Error checking if system polls exist for ${resourceType}:${resourceId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Validate if a resource exists before creating polls
   */
  async validateResource(
    resourceId: number,
    resourceType: RESOURCE_TYPE,
  ): Promise<boolean> {
    try {
      let exists = false;

      switch (resourceType) {
        case RESOURCE_TYPE.LEADER:
          exists = !!(await this.prisma.leaders.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.PARTY:
          exists = !!(await this.prisma.parties.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.GOVERNMENT:
          exists = !!(await this.prisma.governments.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.PARLIAMENT:
          exists = !!(await this.prisma.parliaments.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.DEPARTMENT:
          exists = !!(await this.prisma.departments.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.PROJECT:
          exists = !!(await this.prisma.projects.findUnique({
            where: { id: resourceId },
          }));
          break;
        case RESOURCE_TYPE.MEDIA:
          exists = !!(await this.prisma.medias.findUnique({
            where: { id: resourceId },
          }));
          break;
        default:
          this.logger.warn(`Unknown resource type: ${resourceType}`);
          return false;
      }

      if (!exists) {
        throw new BadRequestException(
          `Resource ${resourceType}:${resourceId} does not exist`,
        );
      }

      return exists;
    } catch (error) {
      this.logger.error(
        `Error validating resource ${resourceType}:${resourceId}`,
        error,
      );
      throw error;
    }
  }
}
