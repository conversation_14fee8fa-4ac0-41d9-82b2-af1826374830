import { Test, TestingModule } from '@nestjs/testing';
import { RESOURCE_TYPE, POLL_CREATED_BY } from '@prisma/client';
import { SystemPollsService } from './system-polls.service';
import { PrismaService } from '../prisma.service';

describe('SystemPollsService', () => {
  let service: SystemPollsService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    polls: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    poll_options: {
      findMany: jest.fn(),
    },
    poll_responses: {
      groupBy: jest.fn(),
      count: jest.fn(),
    },
    poll_vote_counts: {
      createMany: jest.fn(),
      upsert: jest.fn(),
      updateMany: jest.fn(),
    },
    leaders: {
      findUnique: jest.fn(),
    },
    parties: {
      findUnique: jest.fn(),
    },
    governments: {
      findUnique: jest.fn(),
    },
    parliaments: {
      findUnique: jest.fn(),
    },
    departments: {
      findUnique: jest.fn(),
    },
    projects: {
      findUnique: jest.fn(),
    },
    medias: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SystemPollsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<SystemPollsService>(SystemPollsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getOrCreateSystemPolls', () => {
    it('should return existing polls if they exist', async () => {
      const mockPolls = [
        {
          id: 1,
          title: 'Job Performance',
          question: 'Do you think this leader is doing a good job?',
          resourceId: 123,
          resourceType: RESOURCE_TYPE.LEADER,
          createdBy: POLL_CREATED_BY.SYSTEM,
          options: [
            {
              id: 1,
              text: 'Excellent',
              _count: { responses: 10 },
            },
            {
              id: 2,
              text: 'Good',
              _count: { responses: 5 },
            },
          ],
          _count: { responses: 15 },
        },
      ];

      mockPrismaService.leaders.findUnique.mockResolvedValue({ id: 123 });
      mockPrismaService.polls.findMany.mockResolvedValue(mockPolls);

      const result = await service.getOrCreateSystemPolls(123, RESOURCE_TYPE.LEADER);

      expect(mockPrismaService.polls.findMany).toHaveBeenCalledWith({
        where: {
          resourceId: 123,
          resourceType: RESOURCE_TYPE.LEADER,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
        include: {
          options: {
            include: {
              _count: {
                select: { responses: true },
              },
            },
          },
          _count: {
            select: { responses: true },
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Job Performance');
      expect(result[0].totalResponses).toBe(15);
      expect(result[0].options[0].votes).toBe(10);
      expect(result[0].options[0].percentage).toBe(67); // 10/15 * 100 rounded
    });

    it('should create new polls if none exist', async () => {
      const mockCreatedPoll = {
        id: 1,
        title: 'Job Performance',
        question: 'Do you think this leader is doing a good job?',
        resourceId: 123,
        resourceType: RESOURCE_TYPE.LEADER,
        createdBy: POLL_CREATED_BY.SYSTEM,
        options: [
          {
            id: 1,
            text: 'Excellent',
            _count: { responses: 0 },
          },
        ],
        _count: { responses: 0 },
      };

      mockPrismaService.leaders.findUnique.mockResolvedValue({ id: 123 });
      mockPrismaService.polls.findMany.mockResolvedValue([]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          polls: {
            create: jest.fn().mockResolvedValue(mockCreatedPoll),
          },
          poll_vote_counts: {
            createMany: jest.fn(),
          },
        });
      });

      const result = await service.getOrCreateSystemPolls(123, RESOURCE_TYPE.LEADER);

      expect(mockPrismaService.$transaction).toHaveBeenCalled();
      expect(result).toHaveLength(1);
    });

    it('should validate resource exists when validation is enabled', async () => {
      mockPrismaService.leaders.findUnique.mockResolvedValue(null);

      await expect(
        service.getOrCreateSystemPolls(123, RESOURCE_TYPE.LEADER, true)
      ).rejects.toThrow('Resource LEADER:123 does not exist');
    });

    it('should skip validation when validation is disabled', async () => {
      mockPrismaService.polls.findMany.mockResolvedValue([]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          polls: {
            create: jest.fn().mockResolvedValue({
              id: 1,
              options: [],
              _count: { responses: 0 },
            }),
          },
          poll_vote_counts: {
            createMany: jest.fn(),
          },
        });
      });

      await service.getOrCreateSystemPolls(123, RESOURCE_TYPE.LEADER, false);

      expect(mockPrismaService.leaders.findUnique).not.toHaveBeenCalled();
    });
  });

  describe('systemPollsExist', () => {
    it('should return true if polls exist', async () => {
      mockPrismaService.polls.count.mockResolvedValue(5);

      const result = await service.systemPollsExist(123, RESOURCE_TYPE.LEADER);

      expect(result).toBe(true);
      expect(mockPrismaService.polls.count).toHaveBeenCalledWith({
        where: {
          resourceId: 123,
          resourceType: RESOURCE_TYPE.LEADER,
          createdBy: POLL_CREATED_BY.SYSTEM,
          isDeleted: false,
        },
      });
    });

    it('should return false if no polls exist', async () => {
      mockPrismaService.polls.count.mockResolvedValue(0);

      const result = await service.systemPollsExist(123, RESOURCE_TYPE.LEADER);

      expect(result).toBe(false);
    });
  });

  describe('validateResource', () => {
    it('should validate leader exists', async () => {
      mockPrismaService.leaders.findUnique.mockResolvedValue({ id: 123 });

      const result = await service.validateResource(123, RESOURCE_TYPE.LEADER);

      expect(result).toBe(true);
      expect(mockPrismaService.leaders.findUnique).toHaveBeenCalledWith({
        where: { id: 123 },
      });
    });

    it('should validate party exists', async () => {
      mockPrismaService.parties.findUnique.mockResolvedValue({ id: 456 });

      const result = await service.validateResource(456, RESOURCE_TYPE.PARTY);

      expect(result).toBe(true);
      expect(mockPrismaService.parties.findUnique).toHaveBeenCalledWith({
        where: { id: 456 },
      });
    });

    it('should throw error if resource does not exist', async () => {
      mockPrismaService.leaders.findUnique.mockResolvedValue(null);

      await expect(
        service.validateResource(999, RESOURCE_TYPE.LEADER)
      ).rejects.toThrow('Resource LEADER:999 does not exist');
    });
  });

  describe('updatePollVoteCounts', () => {
    it('should update vote counts and percentages', async () => {
      const mockResponses = [
        { optionId: 1, _count: { optionId: 10 } },
        { optionId: 2, _count: { optionId: 5 } },
      ];

      const mockOptions = [
        { id: 1 },
        { id: 2 },
        { id: 3 },
      ];

      mockPrismaService.poll_responses.groupBy.mockResolvedValue(mockResponses);
      mockPrismaService.poll_responses.count.mockResolvedValue(15);
      mockPrismaService.poll_options.findMany.mockResolvedValue(mockOptions);
      mockPrismaService.poll_vote_counts.upsert.mockResolvedValue({});
      mockPrismaService.poll_vote_counts.updateMany.mockResolvedValue({});

      await service.updatePollVoteCounts(1);

      expect(mockPrismaService.poll_vote_counts.upsert).toHaveBeenCalledTimes(2);
      expect(mockPrismaService.poll_vote_counts.updateMany).toHaveBeenCalledWith({
        where: {
          pollId: 1,
          optionId: { in: [3] }, // Option 3 has no responses
        },
        data: {
          count: 0,
          percentage: 0,
        },
      });
    });
  });
});
