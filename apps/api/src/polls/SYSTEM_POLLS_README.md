# SystemPollsService Documentation

## Overview

The `SystemPollsService` is a comprehensive service that automatically manages system-defined polls for different resources in the application. It ensures that every resource (leaders, parties, governments, etc.) has standardized polls available for user engagement.

## Key Features

- **Automatic Poll Creation**: Creates polls based on predefined templates when they don't exist
- **Resource Validation**: Validates that resources exist before creating polls
- **Vote Count Management**: Automatically updates and maintains vote counts and percentages
- **Transaction Safety**: Uses database transactions to ensure data consistency
- **Analytics Support**: Provides statistics and trending poll functionality
- **Template-Based**: Uses configurable poll templates for different resource types

## Core Methods

### `getOrCreateSystemPolls(resourceId, resourceType, validateResource?)`

The main method that either returns existing polls or creates new ones.

```typescript
// Get polls for a leader (will create if they don't exist)
const leaderPolls = await systemPollsService.getOrCreateSystemPolls(
  123, 
  RESOURCE_TYPE.LEADER
);

// Get polls without resource validation (faster)
const partyPolls = await systemPollsService.getOrCreateSystemPolls(
  456, 
  RESOURCE_TYPE.PARTY, 
  false
);
```

### `forceCreateSystemPolls(resourceId, resourceType)`

Forces creation of new polls even if they already exist.

```typescript
// Force create new polls (useful for testing or reset scenarios)
const newPolls = await systemPollsService.forceCreateSystemPolls(
  789, 
  RESOURCE_TYPE.GOVERNMENT
);
```

### `systemPollsExist(resourceId, resourceType)`

Checks if system polls already exist for a resource.

```typescript
const exists = await systemPollsService.systemPollsExist(
  123, 
  RESOURCE_TYPE.LEADER
);
```

## Poll Templates

Polls are created based on templates defined in `poll-templates.constant.ts`. Each resource type has specific poll templates:

### Leader Polls
- Job Performance (ratingOptions)
- Honesty (ratingOptions)
- Competency (ratingOptions)
- Charisma (ratingOptions)
- Trustworthiness (ratingOptions)
- Communication Skills (ratingOptions)
- Listening Skills (ratingOptions)
- Decision Making (ratingOptions)

### Party Polls
- Party Performance (ratingOptions)
- Party Honesty (ratingOptions)
- Party Competency (ratingOptions)
- Party Trustworthiness (ratingOptions)
- Party Communication (ratingOptions)
- Decision Making (ratingOptions)

### Government Polls
- Economic Management (ratingOptions)
- Public Services (satisfactionOptions)
- Government Transparency (ratingOptions)

### Parliament Polls
- Legislative Effectiveness (ratingOptions)
- Parliamentary Oversight (ratingOptions)
- Parliamentary Transparency (satisfactionOptions)

### Department Polls
- Service Quality (satisfactionOptions)
- Efficiency (ratingOptions)
- Staff Quality (ratingOptions)
- Accessibility (ratingOptions)
- Response Time (ratingOptions)

## Option Types

The service uses three types of poll options:

1. **standardOptions**: Yes/No/Neutral (for simple approval questions)
2. **ratingOptions**: Excellent/Good/Fair/Poor/Very Poor (for performance evaluation)
3. **satisfactionOptions**: Very Satisfied/Satisfied/Neutral/Dissatisfied/Very Dissatisfied (for satisfaction surveys)

## Usage Examples

### In a Controller

```typescript
@Controller('leaders')
export class LeadersController {
  constructor(private systemPollsService: SystemPollsService) {}

  @Get(':id/polls')
  async getLeaderPolls(@Param('id') id: number) {
    return this.systemPollsService.getOrCreateSystemPolls(
      id, 
      RESOURCE_TYPE.LEADER
    );
  }
}
```

### In a Service

```typescript
@Injectable()
export class LeadersService {
  constructor(private systemPollsService: SystemPollsService) {}

  async getLeaderWithPolls(id: number) {
    const leader = await this.getLeader(id);
    const polls = await this.systemPollsService.getOrCreateSystemPolls(
      id, 
      RESOURCE_TYPE.LEADER
    );
    
    return { ...leader, polls };
  }
}
```

## Analytics and Statistics

### Get Poll Statistics

```typescript
const stats = await systemPollsService.getPollStatistics(
  123, 
  RESOURCE_TYPE.LEADER
);

// Returns:
// {
//   totalPolls: 8,
//   totalResponses: 150,
//   averageResponsesPerPoll: 19,
//   pollsWithResponses: 7,
//   pollsWithoutResponses: 1
// }
```

### Get Trending Polls

```typescript
// Get top 10 trending polls across all resource types
const trending = await systemPollsService.getTrendingPolls();

// Get top 5 trending leader polls
const trendingLeaders = await systemPollsService.getTrendingPolls(
  RESOURCE_TYPE.LEADER, 
  5
);
```

## Vote Count Management

The service automatically manages vote counts and percentages:

```typescript
// Update vote counts after a user responds to a poll
await systemPollsService.updatePollVoteCounts(pollId);
```

This method:
- Recalculates vote counts for each option
- Updates percentages based on total responses
- Handles options with zero votes
- Maintains data consistency

## Error Handling

The service includes comprehensive error handling:

- **Resource Validation**: Throws `BadRequestException` if resource doesn't exist
- **Transaction Safety**: Rolls back all changes if any poll creation fails
- **Logging**: Detailed logging for debugging and monitoring
- **Graceful Degradation**: Continues operation even if individual polls fail

## Best Practices

1. **Use Resource Validation**: Enable resource validation in production to ensure data integrity
2. **Handle Errors**: Always wrap service calls in try-catch blocks
3. **Cache Results**: Consider caching poll results for frequently accessed resources
4. **Monitor Performance**: Use the statistics methods to monitor poll engagement
5. **Update Vote Counts**: Call `updatePollVoteCounts` after poll responses are added/removed

## Configuration

Poll templates can be customized by modifying the `POLL_TEMPLATES` constant in `poll-templates.constant.ts`. Each template includes:

- Question text (English and local language)
- Title and description
- Poll type (RADIO, CHECKBOX, etc.)
- Option definitions with appropriate option types

## Dynamic Poll Generation

The service now includes advanced dynamic poll generation based on actual data relationships:

### **Leader Dynamic Polls**

When creating polls for a leader, the system automatically generates additional polls based on:

1. **Cabinet Member History**
   - Performance polls for each department they served in
   - Policy effectiveness during their tenure
   - Example: "How would you rate Finance Ministry performance under this leader?"

2. **Parliament Member History**
   - Parliament attendance rating
   - Parliamentary contribution effectiveness
   - Example: "How effective was this leader's contribution in parliament?"

3. **Party Leadership History**
   - Leadership performance within the party
   - Example: "How would you rate this leader's performance as Nepal Communist Party leader?"

### **Department Dynamic Polls**

For departments, the system creates:

1. **Minister Comparison Polls**
   - Compares all ministers who served in the department
   - Only created if multiple ministers have served
   - Example: "Which minister performed best in the Finance Ministry?"

### **Parliament Dynamic Polls**

For parliaments, the system generates:

1. **Most Effective Member Polls**
   - Allows voting for the most effective parliament member
   - Includes up to 5 top members as options
   - Example: "Who was the most effective member in this parliament?"

### **Government Dynamic Polls**

For governments, the system creates:

1. **Best Performing Minister Polls**
   - Compares cabinet members within the government
   - Includes up to 6 ministers as options
   - Example: "Who was the best performing minister in this government?"

### **Party Dynamic Polls**

For parties, the system generates:

1. **Best Party Leader Polls**
   - Compares all leaders who have led the party
   - Includes up to 5 leaders as options
   - Example: "Who was the best leader of this party?"

## Dynamic Poll Features

- **Contextual Questions**: Questions are dynamically generated based on actual roles and tenures
- **Intelligent Filtering**: Only creates comparison polls when there are multiple valid options
- **Localization**: All dynamic polls include both English and Nepali text
- **Unique Codes**: Each dynamic poll gets a unique code for tracking
- **Relationship-Based**: Polls are created based on actual database relationships

## Database Schema

The service works with these main tables:
- `polls`: Main poll records
- `poll_options`: Poll answer options
- `poll_responses`: User responses
- `poll_vote_counts`: Aggregated vote counts and percentages
- `cabinet_members`: For cabinet tenure-based polls
- `parliament_members`: For parliament tenure-based polls
- `party_leaders`: For party leadership-based polls
