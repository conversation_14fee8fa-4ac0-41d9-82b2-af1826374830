import {
  <PERSON>,
  Get,
  Post,
  Param,
  UseGuards,
  Req,
  Query,
  ParseIntPipe,
  ParseEnumPipe,
  Logger,
  Body,
} from '@nestjs/common';
import { SystemPollsService } from './system-polls.service';
import { RESOURCE_TYPE } from '@prisma/client';
import { PollsService } from './polls.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/modules/auth/jwt-auth.guard';
import { AllowAny } from 'src/modules/auth/all-user.decorator';

@ApiTags('Polls')
@Controller('polls')
export class PollsController {
  private readonly logger = new Logger(PollsController.name);

  constructor(
    private readonly systemPollsService: SystemPollsService,
    private readonly pollsService: PollsService,
  ) {}

  @Get(':resourceType/:resourceId')
  @ApiOperation({ summary: 'Get or create system polls for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiQuery({
    name: 'includeUserStatus',
    type: 'boolean',
    required: false,
    description: 'Include user response status',
  })
  @ApiQuery({
    name: 'validate',
    type: 'boolean',
    required: false,
    description: 'Validate if resource exists',
  })
  @ApiResponse({
    status: 200,
    description: 'System polls retrieved/created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid resource type or ID' })
  @ApiResponse({ status: 404, description: 'Resource not found' })
  @AllowAny()
  async getSystemPolls(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
    @Req() req: any,
    @Query('includeUserStatus') includeUserStatus?: string,
    @Query('validate') validate: boolean = true,
  ) {
    this.logger.log(`Getting system polls for ${resourceType}:${resourceId}`);
    console.log(req.user);
    const userId = req.user?.id;

    if (includeUserStatus === 'true' && userId) {
      return this.systemPollsService.getSystemPollsWithUserStatus(
        resourceId,
        resourceType,
        userId,
      );
    }

    return this.systemPollsService.getOrCreateSystemPolls(
      resourceId,
      resourceType,
      validate,
    );
  }

  @Get(':resourceType/:resourceId/results')
  @ApiOperation({ summary: 'Get system polls results for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'System polls results retrieved successfully',
  })
  async getSystemPollsResults(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    return this.systemPollsService.getSystemPollsResults(
      resourceId,
      resourceType,
    );
  }

  @Post(':resourceType/:resourceId/regenerate')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Regenerate system polls for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'System polls regenerated successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async regenerateSystemPolls(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    this.logger.log(
      `Regenerating system polls for ${resourceType}:${resourceId}`,
    );
    return this.systemPollsService.regenerateSystemPolls(
      resourceId,
      resourceType,
    );
  }

  @Post(':resourceType/:resourceId/create')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Force create system polls for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'System polls created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid resource type or ID' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'Polls already exist' })
  async forceCreateSystemPolls(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    this.logger.log(
      `Force creating system polls for ${resourceType}:${resourceId}`,
    );
    return this.systemPollsService.forceCreateSystemPolls(
      resourceId,
      resourceType,
    );
  }

  @Get(':resourceType/:resourceId/exists')
  @ApiOperation({ summary: 'Check if system polls exist for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Poll existence status returned' })
  async checkSystemPollsExist(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    const exists = await this.systemPollsService.systemPollsExist(
      resourceId,
      resourceType,
    );
    return { exists };
  }

  @Get(':resourceType/:resourceId/statistics')
  @ApiOperation({ summary: 'Get poll statistics for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Poll statistics retrieved successfully',
  })
  async getPollStatistics(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    return this.systemPollsService.getPollStatistics(resourceId, resourceType);
  }

  @Get('templates/:resourceType')
  @ApiOperation({ summary: 'Get available poll templates for a resource type' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiResponse({
    status: 200,
    description: 'Available templates retrieved successfully',
  })
  getAvailableTemplates(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
  ) {
    return this.systemPollsService.getAvailableTemplates(resourceType);
  }

  @Get('trending')
  @ApiOperation({ summary: 'Get trending system polls' })
  @ApiQuery({ name: 'resourceType', enum: RESOURCE_TYPE, required: false })
  @ApiQuery({
    name: 'limit',
    type: 'number',
    required: false,
    description: 'Number of polls to return (default: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Trending polls retrieved successfully',
  })
  async getTrendingPolls(
    @Query('resourceType') resourceType?: RESOURCE_TYPE,
    @Query('limit') limit?: string,
  ) {
    const parsedLimit = limit ? parseInt(limit) : 10;
    return this.systemPollsService.getTrendingPolls(resourceType, parsedLimit);
  }

  @Post(':pollId/vote')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Submit a vote for a poll' })
  @ApiParam({ name: 'pollId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Vote submitted successfully' })
  @ApiResponse({
    status: 400,
    description: 'Invalid vote data or poll expired',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Poll not found' })
  async submitVote(
    @Param('pollId', ParseIntPipe) pollId: number,
    @Body() body: { optionIds: number[] },
    @Req() req: any,
  ) {
    console.log(req.user);
    const userId = req.user?.id;

    this.logger.log(`User ${userId} voting on poll ${pollId}`);

    const result = await this.pollsService.submitResponse({
      pollId,
      userId,
      optionIds: body.optionIds,
    });

    // Update vote counts after successful vote
    await this.systemPollsService.updatePollVoteCounts(pollId);

    return result;
  }

  @Post('poll/:pollId/update-counts')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update vote counts for a poll' })
  @ApiParam({ name: 'pollId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Vote counts updated successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updatePollVoteCounts(@Param('pollId', ParseIntPipe) pollId: number) {
    await this.systemPollsService.updatePollVoteCounts(pollId);
    return { message: 'Vote counts updated successfully' };
  }

  @Get('resource/:resourceType/:resourceId/validate')
  @ApiOperation({ summary: 'Validate if a resource exists' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Resource validation result' })
  @ApiResponse({ status: 400, description: 'Resource does not exist' })
  async validateResource(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    const exists = await this.systemPollsService.validateResource(
      resourceId,
      resourceType,
    );
    return { exists, resourceType, resourceId };
  }
}
