# Dynamic Polls Examples

This document provides comprehensive examples of how the dynamic poll system works based on real data relationships.

## Leader Dynamic Polls Example

### <PERSON><PERSON><PERSON>: Leader with Multiple Roles
**Leader**: <PERSON><PERSON><PERSON> (Prachanda)
**Roles**: 
- Prime Minister (2008-2009, 2016-2017, 2022-2024)
- Nepal Communist Party Chairman
- Parliament Member (multiple terms)

### Generated Dynamic Polls:

#### 1. Cabinet Tenure Polls
```json
{
  "code": "CABINET_PERFORMANCE_123",
  "question": "How would you rate Prime Minister's Office performance under this leader?",
  "questionLocal": "यस नेताको नेतृत्वमा प्रधानमन्त्री कार्यालयको प्रदर्शनलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?",
  "title": "Prime Minister's Office Performance",
  "titleLocal": "प्रधानमन्त्री कार्यालय प्रदर्शन",
  "options": ["Excellent", "Good", "Fair", "Poor", "Very Poor"]
}
```

#### 2. Parliament Tenure Polls
```json
{
  "code": "PARLIAMENT_ATTENDANCE_456",
  "question": "How would you rate this leader's parliament attendance?",
  "questionLocal": "यस नेताको संसदमा उपस्थितिलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?",
  "title": "Parliament Attendance",
  "titleLocal": "संसदीय उपस्थिति",
  "options": ["Excellent", "Good", "Fair", "Poor", "Very Poor"]
}
```

#### 3. Party Leadership Polls
```json
{
  "code": "PARTY_LEADERSHIP_789",
  "question": "How would you rate this leader's performance as Nepal Communist Party leader?",
  "questionLocal": "नेपाल कम्युनिष्ट पार्टी नेताको रूपमा यस नेताको प्रदर्शनलाई तपाईं कसरी मूल्याङ्कन गर्नुहुन्छ?",
  "title": "Nepal Communist Party Leadership",
  "titleLocal": "नेपाल कम्युनिष्ट पार्टी नेतृत्व",
  "options": ["Excellent", "Good", "Fair", "Poor", "Very Poor"]
}
```

## Department Dynamic Polls Example

### Scenario: Finance Ministry
**Ministers who served**:
- Dr. Yuba Raj Khatiwada (2018-2021)
- Janardan Sharma (2021-2022)
- Bishnu Paudel (2022-2023)

### Generated Dynamic Poll:
```json
{
  "code": "DEPT_MINISTER_COMPARISON_101",
  "question": "Which minister performed best in this department?",
  "questionLocal": "यस विभागमा कुन मन्त्रीले सबैभन्दा राम्रो प्रदर्शन गरे?",
  "title": "Best Minister Comparison",
  "titleLocal": "उत्कृष्ट मन्त्री तुलना",
  "options": [
    "Dr. Yuba Raj Khatiwada",
    "Janardan Sharma", 
    "Bishnu Paudel"
  ]
}
```

## Parliament Dynamic Polls Example

### Scenario: House of Representatives (2017-2022)
**Notable Members**:
- Sher Bahadur Deuba
- KP Sharma Oli
- Pushpa Kamal Dahal
- Baburam Bhattarai
- Gagan Thapa

### Generated Dynamic Poll:
```json
{
  "code": "PARLIAMENT_EFFECTIVE_MEMBER_202",
  "question": "Who was the most effective member in this parliament?",
  "questionLocal": "यस संसदमा सबैभन्दा प्रभावकारी सदस्य को थिए?",
  "title": "Most Effective Member",
  "titleLocal": "सबैभन्दा प्रभावकारी सदस्य",
  "options": [
    "Sher Bahadur Deuba",
    "KP Sharma Oli",
    "Pushpa Kamal Dahal",
    "Baburam Bhattarai",
    "Gagan Thapa"
  ]
}
```

## Government Dynamic Polls Example

### Scenario: Oli Government (2018-2021)
**Cabinet Members**:
- KP Sharma Oli (PM)
- Ishwar Pokhrel (Deputy PM)
- Dr. Yuba Raj Khatiwada (Finance)
- Pradeep Gyawali (Foreign Affairs)
- Ram Bahadur Thapa (Home)

### Generated Dynamic Poll:
```json
{
  "code": "GOVT_BEST_MINISTER_303",
  "question": "Who was the best performing minister in this government?",
  "questionLocal": "यस सरकारमा सबैभन्दा राम्रो प्रदर्शन गर्ने मन्त्री को थिए?",
  "title": "Best Performing Minister",
  "titleLocal": "उत्कृष्ट प्रदर्शन गर्ने मन्त्री",
  "options": [
    "Ishwar Pokhrel",
    "Dr. Yuba Raj Khatiwada",
    "Pradeep Gyawali",
    "Ram Bahadur Thapa"
  ]
}
```

## Party Dynamic Polls Example

### Scenario: Nepali Congress
**Past Leaders**:
- Girija Prasad Koirala
- Sher Bahadur Deuba
- Ram Chandra Poudel
- Sushil Koirala

### Generated Dynamic Poll:
```json
{
  "code": "PARTY_BEST_LEADER_404",
  "question": "Who was the best leader of this party?",
  "questionLocal": "यस पार्टीको सबैभन्दा राम्रो नेता को थिए?",
  "title": "Best Party Leader",
  "titleLocal": "उत्कृष्ट पार्टी नेता",
  "options": [
    "Girija Prasad Koirala",
    "Sher Bahadur Deuba",
    "Ram Chandra Poudel",
    "Sushil Koirala"
  ]
}
```

## API Response Example

When you call the API for a leader with multiple roles:

```bash
GET /system-polls/LEADER/123
```

**Response:**
```json
{
  "standardPolls": [
    {
      "id": 1,
      "title": "Job Performance",
      "question": "Do you think this leader is doing a good job?",
      "type": "RADIO",
      "options": [...]
    }
  ],
  "dynamicPolls": [
    {
      "id": 15,
      "title": "Prime Minister's Office Performance",
      "question": "How would you rate Prime Minister's Office performance under this leader?",
      "type": "RADIO",
      "code": "CABINET_PERFORMANCE_123",
      "options": [...]
    },
    {
      "id": 16,
      "title": "Parliament Attendance",
      "question": "How would you rate this leader's parliament attendance?",
      "type": "RADIO", 
      "code": "PARLIAMENT_ATTENDANCE_456",
      "options": [...]
    }
  ],
  "totalPolls": 12,
  "dynamicPollsCount": 5
}
```

## Benefits of Dynamic Polls

1. **Contextual Relevance**: Polls are relevant to the actual roles and positions held
2. **Historical Comparison**: Enables comparison across different tenures and positions
3. **Data-Driven**: Based on real database relationships, not static templates
4. **Comprehensive Coverage**: Covers all aspects of a political figure's career
5. **User Engagement**: More interesting and relevant polls increase user participation

## Implementation Notes

- Dynamic polls are only created when there's sufficient data (multiple options)
- All dynamic polls include both English and Nepali translations
- Polls are cached and reused - not regenerated on every request
- Vote counts and percentages are automatically maintained
- Dynamic polls follow the same validation and security rules as standard polls
