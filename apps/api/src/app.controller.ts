import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { AppService } from './app.service';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PrismaService } from 'src/prisma.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { ConfigService } from '@nestjs/config';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { GeoWardsService } from './modules/geo-wards/geo-wards.service';
import { GeoMuncipalsService } from './modules/geo-muncipals/geo-muncipals.service';
import { RatingsService } from './modules/ratings/ratings.service';
import { MediasService } from './medias/medias.service';
import { RequestOriginCacheInterceptor } from './interceptors/RequestOriginCacheInterceptor';
import { ProjectsService } from './projects/projects.service';
import { DepartmentsService } from './modules/departments/departments.service';

@Controller('/')
@UseInterceptors(RequestOriginCacheInterceptor)
export class AppController {
  constructor(
    private readonly appService: AppService,
    private prismaService: PrismaService,
    private leaderService: LeadersService,
    private partiesService: PartiesService,
    private governmentsService: GovernmentsService,
    private parliamentsService: ParliamentsService,
    private electionsService: ElectionsService,
    private contentsService: ContentsService,
    private ratings: RatingsService,
    private aiService: AiService,
    private mostViewedService: most_viewedService,
    private configService: ConfigService,
    private recommendationService: RecommendationService,
    private geoWards: GeoWardsService,
    private geoMunicipals: GeoMuncipalsService,
    private mediasService: MediasService,
    private projectsService: ProjectsService,
    private departmentsService: DepartmentsService,
  ) {}

  @Get('/dashboard/analytics')
  async getHello(@Query() query: any) {
    const results = await Promise.allSettled([
      this.leaderService.analytics(query),
      this.partiesService.analytics(query),
      this.geoWards.analytics(),
      this.geoMunicipals.analytics(),
      this.electionsService.analytics(query),
      this.parliamentsService.analytics(query),
      this.governmentsService.analytics(query),
      this.contentsService.analytics(query),
      this.electionsService.subAnalytics(query),
      this.ratings.analytics(query),
      this.mediasService.analytics(query),
      this.projectsService.analytics(query),
      this.departmentsService.analytics(query),
    ]);

    const [
      leaderRes,
      partiesRes,
      wardsRes,
      municipalsRes,
      electionsRes,
      parliamentsRes,
      governmentsRes,
      contentsRes,
      electionSubsRes,
      ratingsRes,
      mediasRes,
      projectsRes,
      departmentsRes,
    ] = results;

    const extract = (res: PromiseSettledResult<any>) =>
      res.status === 'fulfilled' ? res.value : null;

    return {
      leaders: extract(leaderRes),
      parties: extract(partiesRes),
      wards: extract(wardsRes),
      municipals: extract(municipalsRes),
      elections: extract(electionsRes),
      parliaments: extract(parliamentsRes),
      governments: extract(governmentsRes),
      contents: extract(contentsRes),
      electionSubs: extract(electionSubsRes),
      ratings: extract(ratingsRes),
      medias: extract(mediasRes),
      projects: extract(projectsRes),
      departments: extract(departmentsRes),
    };
  }
}
