'use strict';

import puppeteer from 'puppeteer';
import * as fs from 'fs';
import axios from 'axios';
import * as path from 'path';
import { BraveSearch } from 'brave-search';

type ImageResult = { query: string; url: string };

export class ImageScraper {
  userAgent: string;
  scrollDelay: number;
  puppeteerOptions: { headless: boolean };
  tbs: string;
  safe: string;

  constructor({
    userAgent = [
      'Mozilla/5.0 (X11; Linux i686; rv:64.0) Gecko/20100101 Firefox/64.0',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ],
    scrollDelay = 500,
    puppeteer = { headless: true },
    tbs = {},
    safe = false,
  } = {}) {
    this.userAgent = Array.isArray(userAgent)
      ? userAgent[Math.floor(Math.random() * userAgent.length)]
      : userAgent;
    this.scrollDelay = scrollDelay;
    this.puppeteerOptions = puppeteer;
    this.tbs = this._parseRequestParameters(tbs);
    this.safe = safe ? '&safe=active' : '';
  }

  async downloadImages(
    queries: string | string[],
    limit = 5,
    directory = 'downloads',
  ) {
    const imageUrls = await this.getImageUrl(queries, limit);
    const downloadFolder = path.join(process.cwd(), directory);
    if (!fs.existsSync(downloadFolder)) fs.mkdirSync(downloadFolder);

    for (const [queryKey, images] of Object.entries(imageUrls)) {
      const queryDownloadPath = path.join(downloadFolder, queryKey);
      if (!fs.existsSync(queryDownloadPath)) fs.mkdirSync(queryDownloadPath);

      for (let i = 0; i < images.length; i++) {
        const { url } = images[i];
        const extension = path.extname(url.split('?')[0]) || '.jpg';
        const fileName = `${queryKey}_${i + 1}${extension}`;
        const filePath = path.join(queryDownloadPath, fileName);

        try {
          const response = await axios.get(url, {
            responseType: 'arraybuffer',
          });
          fs.writeFileSync(filePath, response.data);
          console.log(`Downloaded: ${fileName}`);
        } catch (err) {
          console.error(`Failed to download ${url}: ${err.message}`);
        }
      }
    }

    return imageUrls;
  }

  _mapCrawlerToSearchEngine(crawler: string) {
    const mapping = {
      google: this._fetchFromGoogle,
      bing: this._fetchFromBing,
      brave: this._braveSearch,
      wikimedia: this._fetchFromWikimedia,
    };
    return mapping[crawler] || this._braveSearch;
  }

  async getImageUrl(
    queries: string | string[],
    limit = 5,
    options: { crawler: string } = { crawler: 'brave' },
  ): Promise<{ [key: string]: ImageResult[] }> {
    const results: { [key: string]: ImageResult[] } = {};
    const queryArray = Array.isArray(queries) ? queries : [queries];
    const fetcher = this._mapCrawlerToSearchEngine(options.crawler);

    for (const query of queryArray) {
      const queryKey = query.replace(/\s/g, '');
      try {
        results[queryKey] = await fetcher(query, limit);
      } catch (err) {
        try {
          console.log(`Bing failed for "${query}", trying Bing...`);
          results[queryKey] = await this._fetchFromGoogle(query, limit);
        } catch {
          // throw new Error('Failed to fetch from Google');
          results[queryKey] = await this._fetchFromBing(query, limit);
          try {
          } catch {
            console.warn(`Bing failed for "${query}", trying Wikimedia...`);
            results[queryKey] = await this._fetchFromWikimedia(query, limit);
          }
        }
        console.error(`Failed to fetch from Brave: ${err.message}`);
      }
    }
    console.log(results);
    return results;
  }

  async _fetchFromGoogle(query: string, limit: number): Promise<ImageResult[]> {
    const browser = await puppeteer.launch({
      ...this.puppeteerOptions,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.setUserAgent(this.userAgent);

    const searchUrl = `https://www.google.com/search?${
      this.safe
    }&tbm=isch&tbs=${this.tbs}&q=${encodeURIComponent(query)}`;
    await page.goto(searchUrl);
    await this._scrollPage(page);

    console.log('searchUrl', searchUrl);
    const urls = await page.evaluate(() =>
      Array.from(document.querySelectorAll('img'))
        .map((img) => img.src)
        .filter(
          (src) => src && src.startsWith('http') && !src.includes('google'),
        ),
    );

    await browser.close();
    return urls.slice(0, limit).map((url) => ({ query, url }));
  }

  async _fetchFromBing(query: string, limit: number): Promise<ImageResult[]> {
    const browser = await puppeteer.launch({
      ...this.puppeteerOptions,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.setUserAgent(this.userAgent);

    const searchUrl = `https://www.bing.com/images/search?q=${encodeURIComponent(
      query,
    )}`;
    //www.bing.com/images/search?q=site%3A%5C%22onlinekhabar.com%5C%22+Balendra+Shah
    https: await page.goto(searchUrl);
    await this._scrollPage(page);

    const urls = await page.evaluate(() =>
      Array.from(document.querySelectorAll('img.mimg'))
        //@ts-expect-error
        .map((img) => img.src)
        .filter((src) => src && src.startsWith('http')),
    );

    await browser.close();
    return urls.slice(0, limit).map((url) => ({ query, url }));
  }

  async _fetchFromWikimedia(
    query: string,
    limit: number,
  ): Promise<ImageResult[]> {
    const url = `https://commons.wikimedia.org/w/api.php?action=query&generator=search&gsrsearch=${encodeURIComponent(
      query,
    )}&gsrlimit=${limit}&prop=imageinfo&iiprop=url&format=json&origin=*`;

    const response = await axios.get(url);
    const pages = response.data?.query?.pages || {};

    return Object.values(pages)
      .map((p: any) => ({ query, url: p.imageinfo?.[0]?.url }))
      .filter((r: ImageResult) => !!r.url);
  }

  async _scrollPage(page: puppeteer.Page) {
    for (let i = 0; i < 10; i++) {
      await page.evaluate(() => window.scrollBy(0, window.innerHeight));
      await new Promise((r) => setTimeout(r, this.scrollDelay));
    }
  }

  _parseRequestParameters(tbs: any): string {
    if (!tbs) return '';
    return encodeURIComponent(
      Object.entries(tbs)
        .filter(([, v]) => v)
        .map(([k, v]) => `${k}:${v}`)
        .join(','),
    );
  }

  async _braveSearch(query: string, limit: number) {
    const braveSearch = new BraveSearch(process.env.BRAVE_API_KEY);
    const imageSearchResults = await braveSearch.imageSearch(query, {
      count: 5,
      search_lang: 'en',
      country: 'US',
    });
    return imageSearchResults.results.map((result) => ({
      query,
      url: result.thumbnail.src,
    }));
  }
}
