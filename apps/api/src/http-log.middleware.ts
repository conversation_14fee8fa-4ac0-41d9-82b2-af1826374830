import { Injectable, Logger, NestMiddleware } from '@nestjs/common';

import { NextFunction, Request, Response } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
    private logger = new Logger('HTTP');

    use(request: Request, response: Response, next: NextFunction): void {
        const { ip, method, path: url, originalUrl, body, headers } = request;
        const userAgent = request.get('user-agent') || '';

        response.on('close', () => {
            const { statusCode } = response;
            const contentLength = response.get('content-length');

            const nextBody = { ...body };
            delete nextBody.password;
            delete nextBody.confirmPassword;
            delete nextBody.newPassword;
            delete nextBody.oldPassword;
            delete nextBody.token;
            delete nextBody.refreshToken;

            this.logger.log(
                `${method} ${originalUrl} ${JSON.stringify(body)} ${JSON.stringify(headers)} HTTP:${statusCode} ${contentLength} - ${userAgent} ${ip}`
            );
        });

        next();
    }
}