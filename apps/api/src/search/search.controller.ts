import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { SearchService } from './search.service';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';
import { EntityTypeEnum } from 'src/constants';
import { SearchDto } from './dtos/search.dto';
import { toLower } from 'lodash';

@Controller('search')
@UseInterceptors(RequestOriginCacheInterceptor)
export class SearchController {
  constructor(private searchService: SearchService) {}

  @Get('/')
  search(@Query() query: SearchDto) {
    const { search, entities } = query;
    if (!search) return [];

    return this.searchService.getResults(search, {
      entities: (entities?.split(',').map(toLower) as string[]) || [],
    });
  }
}
