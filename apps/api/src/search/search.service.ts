import { Injectable } from '@nestjs/common';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { PrismaService } from 'src/prisma.service';
import { GeoMuncipalsService } from 'src/modules/geo-muncipals/geo-muncipals.service';
import { GeoWardsService } from 'src/modules/geo-wards/geo-wards.service';
import { EntityTypeEnum } from 'src/constants';
import { toLower } from 'lodash';

@Injectable()
export class SearchService {
  constructor(
    private prismaService: PrismaService,
    private partiesService: PartiesService,
    private governmentsService: GovernmentsService,
    private parliamentsService: ParliamentsService,
    private electionsService: ElectionsService,
    private contentsService: ContentsService,
    private geoMunicipal: GeoMuncipalsService,
    private geoWards: GeoWardsService,
    private leaderService: LeadersService,
  ) {}

  async getResults(
    search: string,
    options: {
      entities?: string[];
    },
  ) {
    const { entities } = options;
    const defaultEmpty = {
      items: [],
      totalItems: 0,
    };
    console.log(entities);
    const limit = 5;
    const leaders =
      !entities.length || entities.includes(EntityTypeEnum.Leader.toLowerCase())
        ? await this.leaderService.findAll({ search: search, limit: 10 })
        : { ...defaultEmpty };

    const parties =
      !entities.length || entities.includes(EntityTypeEnum.Party.toLowerCase())
        ? await this.partiesService.findAll({
            search: search,
            limit,
          })
        : { ...defaultEmpty };
    const governments =
      !entities.length ||
      entities.includes(EntityTypeEnum.Government.toLowerCase())
        ? await this.governmentsService.index({
            search: search,
            limit,
          })
        : { ...defaultEmpty };
    const parliaments =
      !entities.length ||
      entities.includes(EntityTypeEnum.Parliament.toLowerCase())
        ? await this.parliamentsService.index({
            search: search,
            limit,
          })
        : { ...defaultEmpty };
    const elections =
      !entities.length ||
      entities.includes(EntityTypeEnum.Election.toLowerCase())
        ? await this.electionsService.index({
            search: search,
            limit,
          })
        : { ...defaultEmpty };
    const contents = { ...defaultEmpty };

    const municipals =
      !entities.length ||
      entities.includes(EntityTypeEnum.Municipal.toLowerCase())
        ? await this.geoMunicipal.index({
            search: search,
            limit: 2,
            //@ts-expect-error
            include: {
              wards: true,
            },
          })
        : { ...defaultEmpty };
    const wards =
      !entities.length || entities.includes(EntityTypeEnum.Ward.toLowerCase())
        ? {
            items: municipals.items.flatMap(({ wards, ...item }) => {
              return wards.map((ward) => ({
                ...ward,
                municipals: item,
              }));
            }),
          }
        : { ...defaultEmpty };

    const electionResultsData = {
      items: [],
      totalItems: 0,
    };
    if (leaders.items.length <= 2 && !entities.length) {
      parties.items.unshift(
        ...leaders.items
          .map((leader) =>
            leader.party_leaders.map((item) => item.party).flat(),
          )
          .flat(),
      );

      const electionResults =
        await this.prismaService.election_results.findMany({
          where: {
            // isElected: true,
            OR: [
              {
                leaders: {
                  name: {
                    contains: search,
                  },
                },
              },
            ],
          },
          include: {
            leaders: {
              include: {
                leaders_images: true,
              },
            },
            parties: true,
            candidacyType: true,
            elections: true,
            ward: true,
            districts: true,
            municipals: true,
            states: true,
          },
          take: 5,
        });
      municipals.items = municipals.items.concat(
        electionResults.map((result) => result.municipals).flat(),
      );

      // wards.items = electionResults
      //   .map((result) => ({
      //     ...result.ward,
      //     municipals: result.municipals,
      //   }))
      //   .flat()
      //   .concat(wards.items);

      console.log(municipals);
      electionResultsData.items = electionResults.map((result) => ({
        ...result,
        name: `${result.leaders.name} - ${result.elections.name} `,
        description: `${result.voteCount} votes /${
          result.candidacyType.name
        } / ${
          result.municipals?.name || result.districts?.name || result.area
        }`,
      }));
      electionResultsData.totalItems = electionResults.length;
    }

    const items = [
      leaders.items.map((resource) => ({
        resourceType: EntityTypeEnum.Leader,
        resource,
      })),
      parties.items.map((resource) => ({
        resourceType: EntityTypeEnum.Party,
        resource,
      })),
      governments.items.map((resource) => ({
        resourceType: EntityTypeEnum.Government,
        resource,
      })),
      parliaments.items.map((resource) => ({
        resourceType: EntityTypeEnum.Parliament,
        resource,
      })),
      elections.items.map((resource) => ({
        resourceType: EntityTypeEnum.Election,
        resource,
      })),
      contents.items.map((resource) => ({
        resourceType: EntityTypeEnum.Content,
        resource,
      })),
      municipals.items.map((resource) => ({
        resourceType: EntityTypeEnum.Municipal,
        resource,
      })),
      wards.items.map((resource) => ({
        resourceType: EntityTypeEnum.Ward,
        resource:
          resource && resource?.municipals?.localName
            ? {
                ...resource,
                name: `${resource?.municipals?.localName}, Ward ${resource.name}`,
              }
            : null,
      })),
      electionResultsData.items.map((resource) => ({
        resourceType: EntityTypeEnum.ElectionSub,
        resource,
      })),
    ]
      .flat()
      .filter((item) => item.resource && item.resource?.name !== 'null');

    return { items, totalItems: items.length };
  }
}
