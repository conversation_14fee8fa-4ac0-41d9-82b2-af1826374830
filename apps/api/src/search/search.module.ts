import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';
import { PrismaService } from 'src/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { SocialContentManagerService } from 'src/socialcontentmanager/socialcontentmanager.service';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { GeoMuncipalsService } from 'src/modules/geo-muncipals/geo-muncipals.service';
import { GeoWardsService } from 'src/modules/geo-wards/geo-wards.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { LeadersModule } from 'src/modules/leaders/leaders.module';
import { MediasService } from 'src/medias/medias.service';
import { ProjectsService } from 'src/projects/projects.service';
import { DepartmentsService } from 'src/modules/departments/departments.service';

@Module({
  controllers: [SearchController],
  providers: [
    SocialContentManagerService,
    PrismaService,
    RatingsService,
    PartiesService,
    GovernmentsService,
    ParliamentsService,
    ElectionsService,
    ContentsService,
    EntityReview,
    most_viewedService,
    RecommendationService,
    GeoWardsService,
    GeoMuncipalsService,
    LeadersService,
    AiService,
    SearchService,
    MediasService,
    ProjectsService,
    DepartmentsService,
  ],
  imports: [LeadersModule],
})
export class SearchModule {}
