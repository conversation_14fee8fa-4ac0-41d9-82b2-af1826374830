export const PASSWORD_REGEX = /^(?=.*).{8,}$/;
export const PASSWORD_WRONG_MESSAGE =
  'Password must have minimum eight characters.';
export const QUEUE_PROCESSES = 'processes';

export const CONTENT_TYPE = {
  SCANDAL: 'SCANDAL',
  CONTROVERSIES: 'CONTROVERSIES',
  ACHIEVEMENTS: 'ACHIEVEMENTS',
  MILESTONES: 'MILESTONES',
  ANNOUNCEMENTS: 'ANNOUNCEMENTS',
  PROMISES: 'PROMISES',
} as const;

export const CONTENT_STATUS = {
  PUBLISHED: 'PUBLISHED',
  DRAFT: 'DRAFT',
  PROVED: 'PROVED',
  REJECTED: 'REJECTED',
  ALLEGATION_PROVED: 'ALLEGATION_PROVED',
  ALLEGATION_REJECTED: 'ALLEGATION_REJECTED',
  COURT_PROVED: 'COURT_PROVED',
  COURT_REJECTED: 'COURT_REJECTED',
  ONGOING: 'ONGOING',
  INCOMING: 'INCOMING',
  COMPLETED: 'COMPLETED',
  POSITIVE: 'POSITIVE',
  NEGATIVE: 'NEGATIVE',
  INCOMPLETE: 'INCOMPLETE',
} as const;
export const RESOURCE_TYPE = {
  LEADER: 'LEADER',
  PARTY: 'PARTY',
  GOVERNMENT: 'GOVERNMENT',
} as const;

export const QUEUE_AI_SUMMARY = 'QUEUE_AI_SUMMARY';
export const QUEUE_AI_IMAGE_FETCHER = 'QUEUE_AI_IMAGE_FETCHER';
export const QUEUE_SEARCH_RESULT = 'QUEUE_SEARCH_RESULT';

export enum EntityTypeEnum {
  Parliament = 'Parliament',
  Rating = 'Rating',
  Government = 'Government',
  Leader = 'Leader',
  Party = 'Party',
  Department = 'Department',
  Municipal = 'MUNICIPAL',
  Ward = 'WARD',
  ElectionSub = 'ELECTION_SUB',
  Election = 'Election',
  Content = 'Content',
  Media = 'Media',
  Project = 'PROJECT',
  ALL = 'ALL',
}
