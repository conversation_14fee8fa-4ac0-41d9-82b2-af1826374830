import { INestApplication, Injectable, OnModuleInit } from '@nestjs/common';
import { capitalize } from 'lodash';
import { Prisma, PrismaClient } from '@prisma/client';
import kyselyExtension from 'prisma-extension-kysely';
import type { DB } from '../prisma/db/types';
import {
  <PERSON><PERSON><PERSON>,
  MysqlAdapter,
  MysqlIntrospector,
  MysqlQueryCompiler,
} from 'kysely';
// import { PrismaClient } from '@prisma/client/extension';
interface PaginationOptions {
  page?: number;
  limit?: number;
  sort_by?: string;
}

interface SearchOptions {
  search?: string;
}

interface FilteringOptions {
  [key: string]: any;
}
// interface GroupByConfig<T> {
//   by: Array<keyof Prisma.groupby>;
//   having?: Prisma.PostScalarWhereWithAggregatesInput;
//   orderBy?: Prisma.PostGroupByOrderByInput;
//   take?: number;
//   skip?: number;
//   _avg?: {
//     // Field names that you want to calculate average on
//     fieldName: keyof Prisma.PostAvgAggregateInputType;
//   };
//   _count?: {
//     // Field names that you want to calculate count on
//     fieldName: keyof Prisma.PostCountAggregateInputType;
//     distinct?: boolean;
//   };
// }
const prisma = new PrismaClient();
type DynamicModel = keyof PrismaClient;
export type IndexOptions<T> = PaginationOptions &
  SearchOptions &
  DynamicFilter<T>;
type DynamicFilter<T> = {
  [K in keyof T]?:
    | T[K]
    | {
        equals?: T[K];
        in?: T[K][];
        not?: T[K];
        notIn?: T[K][];
        contains?: T[K];
        startsWith?: T[K];
        endsWith?: T[K];
        lt?: T[K];
        lte?: T[K];
        gt?: T[K];
        gte?: T[K];
      };
};

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  getExtendedClient() {
    return this.$extends(
      kyselyExtension({
        kysely: (driver) =>
          new Kysely<DB>({
            dialect: {
              // This is where the magic happens!
              createDriver: () => driver,
              // Don't forget to customize these to match your database!
              createAdapter: () => new MysqlAdapter(),
              createIntrospector: (db) => new MysqlIntrospector(db),
              createQueryCompiler: () => new MysqlQueryCompiler(),
            },
            plugins: [
              // Add your favorite plugins here!
            ],
          }),
      }),
    );
  }
  onModuleInit() {}

  public async index<T extends DynamicModel>(
    model: T,
    filter: DynamicFilter<PrismaClient[T]> & { searchFields?: string[] },
    options?: IndexOptions<PrismaClient[T]> & { select?: any },
  ) {
    const {
      page = 1,
      limit = 10,
      search,
      ...additionalFilters
    } = options || {};
    const skip = (page - 1) * limit;
    const take = +limit;
    //@ts-expect-error
    let { searchFields = undefined, ...where }: PrismaClient[T] = { ...filter };

    if (search) {
      const searchableFields =
        searchFields ||
        Prisma.dmmf.datamodel.models
          .find(
            (m) =>
              m.name ===
              capitalize(
                //@ts-expect-error
                model,
              ),
          )
          .fields.map((item) => item.name);

      const breakDownSearch = [search];
      where = {
        ...where,
        OR: breakDownSearch.flatMap((search) =>
          searchableFields.map((field) => ({
            [field]: {
              contains: search,
              startsWith: search,
            },
          })),
        ),
      };
    }

    //@ts-expect-error
    const totalItems = await this[model].count({ where });

    //@ts-expect-error
    if (additionalFilters.select) {
      //@ts-expect-error
      delete additionalFilters.include;
    }
    //@ts-expect-error
    const items = await this[model].findMany({
      where,
      //@ts-expect-error
      orderBy: options?.sort,
      skip,
      take,
      ...additionalFilters,
    });

    return { totalItems, items };
  }

  async paginatedGroupBy<T extends DynamicModel>(
    model: T,
    groupByOptions: any,
    options?: IndexOptions<PrismaClient[T]> & { searchFields?: string[] },
  ) {
    const {
      page = 1,
      limit = 10,
      sort_by,
      search,
      ...additionalFilters
    } = options || {};

    const skip = (page - 1) * limit;
    const take = +limit;

    let { searchFields = [] } = options;
    let { where, having } = groupByOptions;
    if (search) {
      const searchableFields =
        searchFields ||
        Prisma.dmmf.datamodel.models
          .find(
            (m) =>
              m.name ===
              capitalize(
                //@ts-expect-error
                model,
              ),
          )
          .fields.map((item) => item.name);

      where = {
        ...groupByOptions.where,
        OR: searchableFields.map((field) => ({
          [field]: { contains: search },
        })),
      };
    }

    //@ts-expect-error
    const totalItems = await this[model].groupBy({
      ...groupByOptions,
      where,
      having,
      // having: {
      //   value: {
      //     _avg: {
      //       gt: 3,
      //     },
      //   },
      // },
    });

    //@ts-expect-error
    const items = await this[model].groupBy({
      ...groupByOptions,
      where,
      having,
      // having: {
      //   value: {
      //     _avg: {
      //       gt: 3,
      //     },
      //   },
      // },
      skip,
      take,
    });

    return {
      items,
      totalItems: totalItems.length,
    };
  }
}
