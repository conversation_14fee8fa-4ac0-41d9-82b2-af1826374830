import { Injectable } from '@nestjs/common';
import { CreateCompareDto } from './dto/create-compare.dto';
import { UpdateCompareDto } from './dto/update-compare.dto';

@Injectable()
export class CompareService {
  create(createCompareDto: CreateCompareDto) {
    return 'This action adds a new compare';
  }

  findAll() {
    return `This action returns all compare`;
  }

  findOne(id: number) {
    return `This action returns a #${id} compare`;
  }

  update(id: number, updateCompareDto: UpdateCompareDto) {
    return `This action updates a #${id} compare`;
  }

  remove(id: number) {
    return `This action removes a #${id} compare`;
  }
}
