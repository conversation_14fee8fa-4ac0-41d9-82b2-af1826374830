import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { CompareService } from './compare.service';
import { CreateCompareDto } from './dto/create-compare.dto';
import { UpdateCompareDto } from './dto/update-compare.dto';

@Controller('compare')
export class CompareController {
  constructor(private readonly compareService: CompareService) {}

  @Post()
  create(@Body() createCompareDto: CreateCompareDto) {
    return this.compareService.create(createCompareDto);
  }
}
