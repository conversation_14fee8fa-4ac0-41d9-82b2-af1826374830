import { Injectable } from '@nestjs/common';
import { CommentableType, RESOURCE_TYPE } from '@prisma/client';
import { AiService } from 'src/ai/ai.service';
import { EntityTypeEnum } from 'src/constants';
import { ContentsService } from 'src/contents/contents.service';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { PrismaService } from 'src/prisma.service';
import { DEFAULT_SUMMARY } from 'src/utils';

@Injectable()
export class MediasService {
  constructor(
    private prismaService: PrismaService,
    private ratingService: RatingsService,
    private contentsService: ContentsService,
    private aiService: AiService,
    private entityReview: EntityReview,
  ) {}

  findAll(query: PaginationSortSearchDto = {}) {
    return this.entityReview.findAll(
      {
        model: CommentableType.MEDIA,
        name: 'medias',
      },
      {
        ...query,
        //@ts-expect-error
        include: {},
      },
    );
  }

  async findOne(id: number) {
    const media = await this.prismaService.medias.findFirstOrThrow({
      where: {
        id,
      },
    });
    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: media.id + '',
      rateOnType: RESOURCE_TYPE.MEDIA,
    });
    const stats = await this.contentsService.getStats(
      media.id,
      RESOURCE_TYPE.MEDIA,
    );
    let summary = (
      await this.contentsService.getContents({
        resourceId: media.id,
        resourceType: RESOURCE_TYPE.MEDIA,
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: media.id,
        resourceType: RESOURCE_TYPE.MEDIA,
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    const response = { ...media, stats, rating };
    if (!summary || !summaryNP) {
      try {
        summary = DEFAULT_SUMMARY;
        await this.aiService.queueSummaryBuilder(
          {
            id: media.id,
            type: EntityTypeEnum.Media,
          },
          EntityTypeEnum.Media + media.id,
        );
      } catch (e) {
        console.log(e);
      }
    }
    return {
      ...response,
      summary,
      summaryNP,
    };
  }

  async analytics(query: { partyId?: number } & PaginationSortSearchDto) {
    let limit = +query.limit || 10;
    let page = query.page || '1';

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentlyAdded = await this.prismaService.medias.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      where: {
        createdAt: { gt: oneWeekAgo },
      },
    });

    let most_viewed = await this.findAll({
      top: 'views',
      limit: limit,
      page,
    });
    let mostLiked = await this.findAll({
      top: 'rates',
      limit: limit,
      page,
    });

    return {
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
    };
  }
}
