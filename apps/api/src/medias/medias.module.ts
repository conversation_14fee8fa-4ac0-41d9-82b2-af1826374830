import { Module } from '@nestjs/common';
import { MediasService } from './medias.service';
import { MediasController } from './medias.controller';
import { PrismaService } from 'src/prisma.service';
import { AiService } from 'src/ai/ai.service';
import { ContentsService } from 'src/contents/contents.service';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';

@Module({
  providers: [
    MediasService,
    PrismaService,
    RatingsService,
    ContentsService,
    AiService,
    EntityReview,
    most_viewedService,
  ],
  controllers: [MediasController],
})
export class MediasModule {}
