import { Controller, Get, Param, Query } from '@nestjs/common';
import { MediasService } from './medias.service';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';

@Controller('medias')
export class MediasController {
  constructor(private readonly mediasService: MediasService) {}

  @Get()
  findAll(@Query() query: PaginationSortSearchDto = {}) {
    return this.mediasService.findAll(query);
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number } & PaginationSortSearchDto) {
    return this.mediasService.analytics(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.mediasService.findOne(+id);
  }
}
