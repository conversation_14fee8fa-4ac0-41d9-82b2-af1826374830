import { <PERSON><PERSON><PERSON> } from 'ollama';
import { Inject, Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import axios from 'axios';
import { PrismaService } from 'src/prisma.service';
//@ts-ignore
import { Cache } from 'cache-manager';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectQueue } from '@nestjs/bullmq';
import { QUEUE_AI_SUMMARY } from 'src/constants';
import { Queue } from 'bullmq';
import { unregisteredRedisConfig } from 'config/redis.configuration';
import { BraveSearch } from 'brave-search';

@Injectable()
export class AiService {
  openai: OpenAI;
  ollamaClient: Ollama;
  modelArray: string[] = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o'];
  private logger: Logger = new Logger('AiService');

  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    @InjectQueue(QUEUE_AI_SUMMARY)
    private summaryBuilderQueue: Queue,
  ) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_API_BASE_URL,
    });
    this.ollamaClient = new Ollama({ host: process.env.OLLAMA_HOST });
  }

  queueSummaryBuilder(payload, name?: string) {
    const queue = this.summaryBuilderQueue
      .add(
        name || 'summary-builder',
        {
          payload: payload,
        },
        {
          ...unregisteredRedisConfig.queue,
          jobId: name + 'summary-builder' + payload.id,
          attempts: 1,
          backoff: {
            type: 'fixed',
            delay: 48 * 3600,
          },
        },
      )
      .catch((e) => {
        this.logger.error(e);
        throw new Error('Unable to process and add information to our system.');
      });
    return queue;
  }

  async generateText(prompt: string, model: string = 'gpt-3.5-turbo') {
    //@ts-ignore
    const completion = await this.openai.chat.completions.create({
      messages: [{ role: 'system', content: 'You are a helpful assistant.' }],
      model: 'deepseek-v3',
    });

    return completion;
  }

  async queryGroq(prompt) {
    if (!prompt) return;
    const isCached = await this.cacheManager.get(prompt);
    if (isCached) return isCached;

    const response = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: 'llama3-70b-8192', // Or "llama3-8b-8192"
        messages: [
          {
            role: 'user',
            content:
              prompt +
              ` also dont use word "Here is a summary of the leader" or anything like that, just give direct answer`,
          },
        ],
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
        },
      },
    );
    this.cacheManager.set(
      prompt,
      response.data.choices[0].message.content,
      36000,
    );
    return response.data.choices[0].message.content;
  }

  async generateOverviewSummaryOfLeader(
    response: any,
    options = { language: 'english' },
  ) {
    const summaryAi = await this.ollama(`
            Below information is about a leader. so please generate a summary of the leader.
            Information should include his details, political journey which can be taken from party_leaders.
            his ratings and reviews, scandals and everything that make senses for reader. Do mention about milestones, achievements
            this leader has accomplished, big small major incidents. you can use internet to get those details as well.
            also summarize about elections. also please dont use word "Here is a summary of the leader" or anything like that, just give direct answer,
            and in  language: ${options.language}
            ${JSON.stringify(response)}
           `);

    return summaryAi;
  }

  async generateOverviewSummaryOfParty(
    response: any,
    options = { language: 'english' },
  ) {
    const summaryAi = await this.ollama(`
            Below information is about a political party. so please generate a summary of the political party.
            Information should include his details .
            his ratings and reviews, scandals and everything that make senses for reader. Also you can add any additional information.
           in language: ${
             options.language
           } also dont use word "Here is a summary of the leader" or anything like that, just give direct answer
            
            ${JSON.stringify(response)}
           `);

    return summaryAi;
  }

  async generateOverviewSummaryOfGovernment(
    response: any,
    options = { language: 'english' },
  ) {
    const summaryAi = await this.ollama(`
            Below information is about a governnment. so please generate a summary of this governnment.
            Information should include his details .
            his ratings and reviews, scandals and everything that make senses for reader. Also you can add any additional information.
            add information about Department Distributions, cabiner members, which party is majority among them.
            in language: ${
              options.language
            } also dont use word "Here is a summary of the leader" or anything like that, just give direct answer
            
            ${JSON.stringify(response)}
           `);

    return summaryAi;
  }

  async generateOverviewSummaryOfParliament(
    response: any,
    options = { language: 'english' },
  ) {
    const summaryAi = await this.ollama(`
            Below information is about a parliament. so please generate a summary of this parliament.
            Information should include his details there ratings and reviews, scandals and everything that make senses for reader. Also you can add any additional information.
            add information about Department Distributions, cabiner members, which party is majority among them.
           in language: ${
             options.language
           } also dont use word "Here is a summary of the leader" or anything like that, just give direct answer
            ${JSON.stringify(response)}
           `);

    return summaryAi;
  }

  async transliterate(text: string, language: string = 'nepali') {
    const summaryAi = await this.queryGroq(`
            Transliterate this text to ${language}. and dont use any generic ai bot messages like "Here is the transliterated text in Nepali:", just give me content only
            ${text}
           `);

    return summaryAi;
  }

  async generateOverviewSummaryOfContent(
    response: any,
    options = { language: 'english' },
  ) {
    const summaryAi = await this.ollama(`
            Below information is about a content. so please generate a summary of this content.
            Information should senses for reader and better if it is in points plus few paragraphs. Also you can add any additional information.
            And if there are any links to other resources, please add them as well.
            Ddont use word "Here is a summary of the content" or anything like that, just give direct answer and add no notes
            ${JSON.stringify(response)}
           `);

    return summaryAi;
  }

  async ollama(prompt: string) {
    // const response = await this.ollamaClient.chat({
    //   model: 'llama3.2',
    //   messages: [{ role: 'user', content: prompt }],
    // });
    const response = await this.ollamaClient.chat({
      model: 'gemma:2b-instruct',
      messages: [{ role: 'user', content: prompt }],
      // stream: true,
    });
    // for await (const part of response) {
    //   process.stdout.write(part.message.content);
    // }
    // return '';
    return response.message.content;
  }

  getNewsFromBrave(query: string, options: any = {}) {
    const braveSearch = new BraveSearch(process.env.BRAVE_API_KEY);
    const { summary, webSearch } = braveSearch.getSummarizedAnswer(query, {
      count: 10, // Number of search results to return
      search_lang: 'en', // Optional: Language of the search results (default is "en")
      country: 'US', // Optional: Country for the search results (default is "us")
      text_decorations: false, // Optional: Whether to include text decorations (default is true)
      spellcheck: false, // Optional: Whether to enable spellcheck (default is true)
      extra_snippets: true, // Optional: Whether to include extra snippets (default is false)
      ...options,
    });

    return { summary, webSearch };
  }
}
