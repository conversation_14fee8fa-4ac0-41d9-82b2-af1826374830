import { Controller, Get, Query } from '@nestjs/common';
import { AiService } from './ai.service';

@Controller('ai')
export class AiController {
  constructor(private readonly aiService: AiService) {}

  @Get('/')
  get(@Query('prompt') prompt: any) {
    return this.aiService.queryGroq(prompt);
  }
  @Get('/ollama')
  ollama(@Query('prompt') prompt: any) {
    return this.aiService.ollama(prompt);
  }
}
