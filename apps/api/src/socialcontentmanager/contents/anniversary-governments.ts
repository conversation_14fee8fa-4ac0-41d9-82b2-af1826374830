import { ISocialMediaContent, ISocialMediaContentResponseOptions, ISocialMediaContentResponseType } from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import { getImageUrlWithFallback } from 'src/utils';

export class AnniversaryGovernments extends ISocialMediaContent {
    static contentName = SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS

    async getRecords(options?: ISocialMediaContentResponseOptions): Promise<any[]> {
        return this.getGovernmentAnniversariesToday()
    }

    async getContent(): ISocialMediaContentResponseType {
        const today = new Date();
        const month = today.getMonth() + 1;
        const day = today.getDate();

        // Find governments with start date matching today's month and day
        const governments = await this.getGovernmentAnniversariesToday();



        const contents = governments.map((govt: any) => {
            const years = today.getFullYear() - new Date(govt.startedAt).getFullYear();
            return {
                image: govt.headId ? getImageUrlWithFallback(govt.head?.img, govt.head.ecCandidateID) : null,
                content: this.generateGovernmentAnniversaryPost(govt),
                comment: `Learn more about ${govt.governmentName} on Nepal Tracks - https://www.nepaltracks.com/governments/${govt.id}`
            };
        });

        return contents;
    }


    async getGovernmentAnniversariesToday() {
        const today = new Date();
        const currentYear = today.getFullYear();
        const currentMonth = today.getMonth() + 1;
        const currentDay = today.getDate();

        const governments = await this.params.prismaService.governments.findMany({
            where: {
                // startedAt: {
                //     not: null,
                // },
            },
            include: {
                head: {
                    include: {
                        party_leaders: {
                            include: {
                                party: true,
                            },
                        },
                    },
                }
            },
        });

        return governments
            .filter((gov) => {
                const start = new Date(gov.startedAt);
                return (
                    start.getDate() === currentDay &&
                    start.getMonth() + 1 === currentMonth &&
                    currentYear > start.getFullYear()
                );
            })
            .map((gov) => {
                const startedAt = new Date(gov.startedAt);
                const years = currentYear - startedAt.getFullYear();

                return {
                    head: gov.head,
                    id: gov.id,
                    headId: gov.headId,
                    governmentName: gov.name || 'a Nepalese',
                    leaderName: gov.head?.name || 'an unknown leader',
                    leaderLocalName: gov.head?.localName || '',
                    startedAt,
                    years,
                    isCurrent: !gov.endAt,
                    endedAt: gov.endAt ? new Date(gov.endAt) : null,
                };
            });
    }


    generateGovernmentAnniversaryPost(gov) {
        const {
            years,
            governmentName,
            leaderName,
            partyName,
            startedAt,
            isCurrent,
            endedAt
        } = gov;

        const startDateStr = startedAt.toLocaleDateString('en-GB'); // e.g., 04/05/2021




        if (isCurrent) {
            return `
        🇳🇵 आज ${startDateStr} मा गठन भएको ${governmentName} सरकारले ${years} वर्ष पुरा गरेको छ। प्रधानमन्त्री ${leaderName} को नेतृत्वमा बनेको यस सरकारलाई कस्तो मूल्यांकन गर्नुहुन्छ?
      
तपाईंको विचारहरू कमेन्टमा लेख्नुहोस्।
        
🎉 Today marks the ${years} year anniversary of the ${governmentName} government, formed on ${startDateStr} under the leadership of ${leaderName} .
This administration has completed ${years} year(s) in office. We look back on its journey, policies, and impact across the country.
        
What are your thoughts on the government's performance so far? Let us know in the comments below.
#GovernmentAnniversary #NepalPolitics #राजनीति`;
        }

        const baseText = `📅 ${governmentName || 'A government'} formed under ${leaderName}.`;
        const english = isCurrent
            ? `Today marks ${years} years since the formation of this government.`
            : `Today marks ${years} years since this government was formed. It concluded its term on ${endedAt?.toLocaleDateString('en-GB')}.`;

        const nepali = isCurrent
            ? `आज ${leaderName} नेतृत्वको सरकारको ${years} पुगेको छ।`
            : `आज ${leaderName} नेतृत्वको सरकार गठन भएको ${years} years पुगेको छ। कार्यकाल समाप्ति मिति: ${endedAt?.toLocaleDateString('ne-NP')}.`;

        return `${baseText}\n\n🇬🇧 ${english}\n🇳🇵 ${nepali}`;

    }


}