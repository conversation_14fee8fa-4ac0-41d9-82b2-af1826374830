import {
  ISocialMediaContent,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import {
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';
import { truncate } from 'lodash';

export class RandomFromRecommendation extends ISocialMediaContent {
  static contentName = SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION;

  async getContent(): ISocialMediaContentResponseType {
    const randomFromRecommendation =
      await this.params.recommendation.getRecommendations('leaders');
    const leader = randomFromRecommendation[0];
    const party = leader.party_leaders?.[0]?.partyId
      ? await this.params.parties.getPartyById(
          leader.party_leaders?.[0]?.partyId,
        )
      : null;

    const [summary, electionResult] = await Promise.all([
      this.params.aiService.generateOverviewSummary<PERSON>f<PERSON><PERSON>er(leader),
      this.params.prismaService.election_results.findFirst({
        where: { leaderId: leader.id },
        orderBy: { voteCount: 'desc' },
        include: {
          states: true,
          elections: true,
          districts: true,
          municipals: true,
          candidacyType: true,
          parties: true,
          ward: true,
        },
      }),
    ]);

    const electionDetails = electionResult
      ? `🗳️ चुनावी जानकारी:
निर्वाचन: ${electionResult.elections.name} (${formatToStandardReadableDate(
          electionResult.elections.year,
        )})
पद: ${
          electionResult.candidacyType.localName ||
          electionResult.candidacyType.name
        }
क्षेत्र: ${electionResult.districts?.name || ''} ${
          electionResult.municipals?.name || ''
        } ${electionResult.area || ''}
पार्टी: ${
          electionResult.parties?.localName ||
          electionResult.parties?.name ||
          'स्वतन्त्र'
        }
प्राप्त मत: ${electionResult.voteCount} (${
          electionResult.isElected ? 'विजेता' : 'प्रतिस्पर्धी'
        })`
      : '';

    const district = electionResult?.districts;
    const municipal = electionResult?.municipals;
    const ward = electionResult?.ward;

    return [
      {
        comment: `Learn more about ${
          leader.localName || leader.name
        } on Nepal Tracks - https://www.nepaltracks.com/leaders/${leader.id}`,
        image: getImageUrlWithFallback(leader.img, leader.ecCandidateID),
        content: `
🏘️ तपाईंको नेता चिन्नुहोस् 🏘️ (${
          ward ? `वार्ड ${ward?.localName || ward?.name},` : ''
        } ${municipal ? (municipal?.localName || municipal?.name) + ',' : ''} ${
          district ? district?.localName || district?.name : ''
        } - ${electionResult?.area || ''}, ${
          electionResult?.states?.localName ||
          electionResult?.states?.name ||
          ''
        } )
    
नाम: ${leader.localName || leader.name}
पार्टी: ${party?.localName || party?.name || 'स्वतन्त्र'}

${electionDetails}
    
${summary ? truncate(summary, { length: 500 }) : ''}
    
    
उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।
   
https://www.nepaltracks.com/leaders/${leader.id}
#NepalTracks #NepalPolitics #KnowYourLeader
          `.trim(),
      },
    ];
  }
}
