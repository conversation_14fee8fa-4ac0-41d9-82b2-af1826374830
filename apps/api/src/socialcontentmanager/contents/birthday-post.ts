import { getImageUrlWithFallback } from "src/utils";
import { ISocialMediaContent, ISocialMediaContentResponseOptions, ISocialMediaContentResponseType, } from "./ISocialMediaContent"
import { SocialMediaContentTypeEnum } from "./SocialMediaContentTypeEnum"

export class BirthdayPost extends ISocialMediaContent {

    static contentName = SocialMediaContentTypeEnum.BIRTHDAY_POST

    async getRecords(options?: ISocialMediaContentResponseOptions): Promise<any[]> {
        const today = new Date();
        const month = today.getMonth() + 1;
        const day = today.getDate();
        const records = await this.params.leaders.getBirthdayLeadersRaw();
        return Array.isArray(records) ? records : [];
    }

    async getContent(): ISocialMediaContentResponseType {
        const today = new Date();
        const month = today.getMonth() + 1;
        const day = today.getDate();


        const leaders = await this.params.leaders.getBirthdayLeadersRaw()
        //@ts-expect-error
        const contents = leaders.map((item: any) => {
            return {
                comment: `Read more about ${item.localName} on Nepal Tracks - https://www.nepaltracks.com/leaders/${item.id}`,
                image: getImageUrlWithFallback(item.img, item.ecCandidateID),
                content: `
                🎉 जन्मदिनको शुभकामना 🎉

आज ${month} -${day} जन्मदिन मनाउँदै हुनुहुन्छ:
🇳🇵 ${item.localName} ${item.partyShortName}

 ${item.address ? `📍 स्थान: ${item.address}` : ''}
 ${item.nameOfInst ? `🎓 शिक्षा: ${item.nameOfInst}` : ''}
 ${item.metadata.fatherName ? `👨‍👩‍👧‍👦 पिता: ${item.metadata.fatherName}` : ''}
 ${item.metadata.spouseName ? `👫 जीवनसाथी: ${item.metadata.spouseName}` : ''}

राजनीतिक योगदानका लागि धन्यवाद। तपाईंको दीर्घायु र सफल जीवनको कामना! 🙏



🎉 Happy Birthday 🎉

Celebrating the birthday today, ${month} -${day}:
🇳🇵 ${item.name} ${item.name}

${item.address ? `📍 place: ${item.address}` : ''}
${item.nameOfInst ? `🎓 Education: ${item.nameOfInst}` : ''}
${item.metadata.fatherName ? `👨‍👩‍👧‍👦 Father: ${item.metadata.fatherName}` : ''}
${item.metadata.spouseName ? `👫 Spouse:  ${item.metadata.spouseName}` : ''}

Thank you for your political contributions. Wishing you long life and continued success! 🙏

#HappyBirthday #NepalTracks #RajnitiReport
`
            }
        })
        return contents
    }
}