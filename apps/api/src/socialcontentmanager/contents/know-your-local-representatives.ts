import { CandidacyTypeEnum } from '@prisma/client';
import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import {
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';

export class KnowYourLocalRepresentatives extends ISocialMediaContent {
  getRandomCandidateType() {
    const candidacyTypes = [
      CandidacyTypeEnum.MAYOR,
      CandidacyTypeEnum.DEPUTY_MAYOR,
      CandidacyTypeEnum.CHAIRPERSON,
      CandidacyTypeEnum.DEPUTY_CHAIRPERSON,
    ];
    const randomType =
      candidacyTypes[Math.floor(Math.random() * candidacyTypes.length)];
    return randomType;
  }
  async getRecords(
    options?: ISocialMediaContentResponseOptions,
  ): Promise<any[]> {
    const limit = options?.limit || 1;
    const randomType = Array.from({ length: 4 }, () =>
      this.getRandomCandidateType(),
    );

    // Count eligible local reps
    const totalCount = await this.params.prismaService.election_results.count({
      where: {
        isElected: true,
        candidacyType: {
          name: {
            in: randomType,
          },
        },
        municipals: {
          is: {},
        },
      },
    });

    if (!totalCount) return null;

    // Step 2: Calculate random offset safely
    const maxOffset = Math.max(0, totalCount - limit);
    const randomOffset = options?.page
      ? options.page * limit
      : Math.floor(Math.random() * (maxOffset + 1)); // safe random offset

    const results = await this.params.prismaService.election_results.findMany({
      skip: randomOffset,
      take: limit || 1,
      where: {
        isElected: true,
        candidacyType: {
          name: {
            in: randomType,
          },
        },
        municipals: {
          is: {},
        },
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        parties: true,
        candidacyType: true,
        elections: true,
        municipals: {
          include: {
            districts: true,
            states: true,
          },
        },
      },
    });

    return results;
  }
  static contentName =
    SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES;

  async getContent(): ISocialMediaContentResponseType {
    const randomType = this.getRandomCandidateType();
    const [electedRep] = await this.getRecords();
    if (!electedRep) return null;

    const leader = electedRep.leaders;
    const party = electedRep.parties;
    const election = electedRep.elections;
    const municipal = electedRep.municipals;
    const district = municipal?.districts;
    const state = municipal?.states;

    return [
      {
        comment: `Learn more about ${
          leader.localName || leader.name
        } on Nepal Tracks - https://www.nepaltracks.com/leaders/${leader.id}`,
        image: getImageUrlWithFallback(leader.img, leader.ecCandidateID),
        content: `
🏙️ तपाईंको स्थानीय प्रतिनिधि चिन्नुहोस् 🏙️ (${
          municipal ? (municipal?.localName || municipal?.name) + ',' : ''
        } ${district ? district?.localName || district?.name : ''} - ${
          electedRep?.area || ''
        }, ${state?.localName || state?.name || ''} )

पद: ${randomType
          .replace(/_/g, ' ')
          .toLowerCase()
          .replace(/\b\w/g, (c) => c.toUpperCase())}
नाम: ${leader.localName || leader.name}
पार्टी: ${party?.localName || party?.name || 'स्वतन्त्र'}
प्राप्त मत: ${electedRep.voteCount?.toLocaleString('en-US') || 'N/A'}
नगरपालिका: ${municipal.localName || municipal.name}
जिल्ला: ${district?.localName || district?.name}
प्रदेश: ${state?.localName || state?.name}
चुनाव: ${election?.name} (${formatToStandardReadableDate(election?.year)})

उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।

https://www.nepaltracks.com/leaders/${leader.id}
https://www.nepaltracks.com/municipals/${municipal.id}

#NepalTracks #LocalRepresentatives #NepalPolitics
            `.trim(),
      },
    ];
  }
}
