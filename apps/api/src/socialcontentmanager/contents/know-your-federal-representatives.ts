import { CandidacyTypeEnum } from '@prisma/client';
import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import {
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';
import { truncate } from 'lodash';

function formatNumber(num: number): string {
  return num?.toLocaleString('en-US') || 'N/A';
}

export class KnowYourFederalRepresentatives extends ISocialMediaContent {
  static contentName =
    SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES;

  async getRecords(
    options?: ISocialMediaContentResponseOptions,
  ): Promise<any[]> {
    const limit = options?.limit || 1;
    const candidacyType = CandidacyTypeEnum.LAW_MAKER;

    // Step 1: Count how many matching results are there
    const totalCount = await this.params.prismaService.election_results.count({
      where: {
        isElected: true,
        candidacyType: {
          name: candidacyType,
        },
      },
    });

    if (!totalCount) return null;

    // Step 2: Generate random offset
    // Step 2: Calculate random offset safely
    const maxOffset = Math.max(0, totalCount - limit);
    const randomOffset = options?.page
      ? options.page * limit
      : Math.floor(Math.random() * (maxOffset + 1)); // safe random offset

    // Step 3: Pick one randomly
    const results = await this.params.prismaService.election_results.findMany({
      skip: randomOffset,
      take: limit || 1,
      where: {
        isElected: true,
        candidacyType: {
          name: candidacyType,
        },
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        candidacyType: true,
        parties: true,
        elections: true,
        districts: true,
        states: true,
        municipals: true,
        ward: true,
      },
    });

    return results;
  }

  async getContent(): ISocialMediaContentResponseType {
    // Step 3: Pick one randomly
    const [result] = await this.getRecords();
    if (!result) return null;

    const leader = result.leaders;
    const party = result.parties;
    const election = result.elections;

    const addressParts = [
      result.ward?.name,
      result.municipals?.localName,
      result.districts?.localName,
      result.states?.localName,
    ].filter(Boolean);
    const address = addressParts.join(', ');

    let summary: string | null = null;
    try {
      summary = await this.params.aiService.generateOverviewSummaryOfLeader(
        leader,
      );
    } catch (e) {
      console.warn(`Failed to generate AI summary: ${e.message}`);
    }

    return [
      {
        comment: `Learn more about ${
          leader.localName || leader.name
        } on Nepal Tracks - https://www.nepaltracks.com/leaders/${leader.id}`,
        image: getImageUrlWithFallback(leader.img, leader.ecCandidateID),
        content: `
    🏛️ तपाईंको संघीय प्रतिनिधि चिन्नुहोस् 🏛️ (${
      result.districts?.localName
    } - ${result.area}, ${result.states?.localName})
    
    प्रतिनिधि सभा सदस्य: ${leader.localName || leader.name}
    पार्टी: ${party?.localName || party?.name || 'स्वतन्त्र'}
    प्राप्त मत: ${formatNumber(result.voteCount)}
    चुनाव: ${election?.name} (${formatToStandardReadableDate(election?.year)})
    ठेगाना: ${address || 'उपलब्ध छैन'}
    
    ${summary ? truncate(summary, { length: 500 }) : ''}
    
    उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।
    https://www.nepaltracks.com/leaders/${leader.id}
    #NepalTracks #FederalRepresentative #NepalPolitics
            `.trim(),
      },
    ];
  }
}
