import {
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';
import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import { CandidacyTypeEnum } from '@prisma/client';

export class KnowYourProvincialRepresentatives extends ISocialMediaContent {
  static contentName =
    SocialMediaContentTypeEnum.KNOW_YOUR_PROVINCIAL_REPRESENTATIVES;

  async getRecords(
    options?: ISocialMediaContentResponseOptions,
  ): Promise<any[]> {
    const limit = options?.limit || 1;
    const candidacyType = CandidacyTypeEnum.PROVINCIAL_LAW_MAKER;

    const totalCount = await this.params.prismaService.election_results.count({
      where: {
        isElected: true,
        candidacyType: { name: candidacyType },
      },
    });

    if (!totalCount) return null;

    // Step 2: Calculate random offset safely
    const maxOffset = Math.max(0, totalCount - limit);
    const randomOffset = options?.page
      ? options.page * limit
      : Math.floor(Math.random() * (maxOffset + 1)); // safe random offset

    const results = await this.params.prismaService.election_results.findMany({
      take: limit || 1,
      skip: randomOffset,
      where: {
        isElected: true,
        candidacyType: { name: candidacyType },
      },
      include: {
        candidacyType: true,
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        parties: true,
        elections: true,
        states: true,
        districts: true,
      },
    });

    return results;
  }

  async getContent(): ISocialMediaContentResponseType {
    const [result] = await this.getRecords();

    if (!result) return null;

    const leader = result.leaders;
    const party = result.parties;
    const election = result.elections;
    const state = result.states;
    const district = result.districts;

    return [
      {
        comment: `Learn more about ${
          leader.localName || leader.name
        } on Nepal Tracks - https://www.nepaltracks.com/leaders/${leader.id}`,
        image: getImageUrlWithFallback(leader.img, leader.ecCandidateID),
        content: `
    🏞️ तपाईंको प्रदेश सभा प्रतिनिधि चिन्नुहोस् 🏞️ (${
      state?.localName || state?.name
    } - ${result?.area})
    
    नाम: ${leader.localName || leader.name}
    पार्टी: ${party?.localName || party?.name || 'स्वतन्त्र'}
    प्राप्त मत: ${result.voteCount?.toLocaleString('en-US') || 'N/A'}
    प्रदेश: ${state?.localName || state?.name || 'N/A'}
    जिल्ला: ${district?.localName || district?.name || 'N/A'}
    चुनाव: ${election.name} (${formatToStandardReadableDate(election.year)})
    
    उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।
    
    https://www.nepaltracks.com/leaders/${leader.id}
    #NepalTracks #ProvincialPolitics #NepalProvince
            `.trim(),
      },
    ];
  }
}
