import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/prisma.service';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { CONTENT_TYPE } from '@prisma/client';

export interface ISocialMediaContentParams {
  leaders: LeadersService;
  parties: PartiesService;
  governments: GovernmentsService;
  parliaments: ParliamentsService;
  elections: ElectionsService;
  contents: ContentsService;
  entityReview: EntityReview;
  aiService: AiService;
  mostViewedService: most_viewedService;
  configService: ConfigService;
  recommendation: RecommendationService;
  prismaService: PrismaService;
}

export interface ISocialMediaContentResponse {
  content: string;
  image?: string;
  comment?: string;
}
export type ISocialMediaContentResponseOptions = {
  limit?: number;
  offset?: number;
  in?: number[];
  notIn?: number[];
  contentTypeNotIn?: CONTENT_TYPE[];
  contentTypeIn?: CONTENT_TYPE[];
  category?: string;
  page?: number;
};

export type ISocialMediaContentResponseType = Promise<
  ISocialMediaContentResponse[] | null
>;

export abstract class ISocialMediaContent {
  constructor(protected params: ISocialMediaContentParams) {}

  abstract getContent(
    options?: ISocialMediaContentResponseOptions,
  ): ISocialMediaContentResponseType;

  getRecords(options?: ISocialMediaContentResponseOptions): Promise<any[]> {
    return Promise.resolve([]);
  }

  getContentsFromRecords(
    records: any[],
    options?: ISocialMediaContentResponseOptions,
  ): ISocialMediaContentResponseType {
    return Promise.resolve([]);
  }
}
