import { formatDistance } from 'date-fns';
import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import {
  formatAppDate,
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';

export class RandomElectionResults extends ISocialMediaContent {
  static contentName = SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS;

  async getRecords(
    options?: ISocialMediaContentResponseOptions,
  ): Promise<any[]> {
    try {
      const allElCodes =
        await this.params.prismaService.election_results.findMany({
          distinct: ['elCode'],
          // skip: Math.floor(Math.random() * 100) * 100, // ← this can go up to 148,500
          select: { elCode: true },
          where: {
            elections: {
              electionType: {
                in:
                  options?.page && options?.page > 15
                    ? undefined
                    : ['GENERAL_PARLIAMENT_ELECTION'],
              },
            },
          },
        });

      const randomElection =
        allElCodes[Math.floor(Math.random() * allElCodes.length)];

      const electionResults =
        await this.params.prismaService.election_results.findMany({
          where: {
            elCode: randomElection.elCode,
          },
          orderBy: {
            voteCount: 'desc',
          },
          take: options?.limit || 2,
          skip: options?.offset || 0,
          include: {
            leaders: {
              include: {
                leaders_images: true,
              },
            },
            parties: true,
            elections: true,
            districts: true,
            municipals: true,
            candidacyType: true,
          },
        });
      return electionResults;
    } catch (err) {
      return [];
    }
  }

  async getContent(): ISocialMediaContentResponseType {
    // Step 2: Get top 2 candidates for that elCode
    const electionResults = await this.getRecords({ limit: 2 });

    if (!electionResults || electionResults.length === 0) {
      return null;
    }

    const result =
      electionResults.find((item) => item.isElected) || electionResults[0];
    const nearestOpponent = electionResults.find((item) => !item.isElected);

    return [
      {
        comment: `Learn more about ${
          result.leaders.localName || result.leaders.name
        } on Nepal Tracks - https://www.nepaltracks.com/elections/${
          result.elections.id
        }/sub/${result.elCode}/${result.candidacyTypeId}`,
        image: [
          getImageUrlWithFallback(
            result.leaders.img,
            result.leaders.ecCandidateID,
          ),
          getImageUrlWithFallback(
            nearestOpponent.leaders.img,
            nearestOpponent.leaders.ecCandidateID,
          ),
        ].join(','),
        content: `
🗳️ अघिल्ला चुनावी परिणाममा फर्केर हेर्दा 🗳️

चुनाव: ${result.elections.name} (${formatToStandardReadableDate(
          result.elections.year,
        )})
पद: ${result.candidacyType.localName || result.candidacyType.name}
प्राप्त मत: ${result.voteCount}
विजेता: ${result.leaders.localName || result.leaders.name}
पार्टी: ${result.parties?.localName || result.parties?.name || 'स्वतन्त्र'}
क्षेत्र: ${[result.districts?.name, result.municipals?.name, result.area]
          .filter(Boolean)
          .join(' ')}

निकटतम प्रतिस्पर्धी:
नाम: ${nearestOpponent.leaders.localName || nearestOpponent.leaders.name}
पार्टी: ${
          nearestOpponent.parties?.localName ||
          nearestOpponent.parties?.name ||
          'स्वतन्त्र'
        }
क्षेत्र: ${[
          nearestOpponent.districts?.name,
          nearestOpponent.municipals?.name,
          nearestOpponent.area,
        ]
          .filter(Boolean)
          .join(' ')}
प्राप्त मत: ${nearestOpponent.voteCount}

उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।
थप जान्न: https://www.nepaltracks.com/elections/${result.elections.id}/sub/${
          result.elCode
        }/${result.candidacyTypeId}

#NepalTracks #ElectionResults #NepalPolitics
    `.trim(),
      },
    ];
  }
}
