import { ISocialMediaContent, ISocialMediaContentResponseOptions, ISocialMediaContentResponseType } from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';

export class AnniversaryParties extends ISocialMediaContent {
    static contentName = SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES;

    async getRecords(options?: ISocialMediaContentResponseOptions): Promise<any[]> {
        return this.getPartyAnniversariesToday()
    }

    async getContent(): ISocialMediaContentResponseType {
        const today = new Date();
        const currentYear = today.getFullYear();

        const parties = await this.getPartyAnniversariesToday();

        const contents = await Promise.all(parties.map(async (party: any) => {
            const years = currentYear - new Date(party.establishedDate).getFullYear();
            let nextContent = this.generatePartyAnniversaryPost(party);
            try {
                const ai = await this.params.aiService.queryGroq(`This post is about anniversary of political party. summarize this ${(nextContent)} and Write a short summary of ${party.name} party. Include its history, notable leaders, and current stance on major issues. Also mention any upcoming events or milestones or scandals. 
                It should be in both  english and nepali, first paragraph should be in nepali and second in english. Title should english and nepali side by side. This is also for facebook so make sure it is formatted correctly for facebook and use emojis if possible.`);

                nextContent = ai
            }
            catch (e) {
                console.log(e);
            }
            return {
                image: party.logo ? (party.logo) : null,
                content: nextContent,
                comment: `Read more about ${party.name} on Nepal Tracks - https://www.nepaltracks.com/parties/${party.id}`
            };
        }));

        return contents;
    }

    async getPartyAnniversariesToday() {
        const today = new Date();
        const currentMonth = today.getMonth() + 1;
        const currentDay = today.getDate();

        const parties = await this.params.prismaService.parties.findMany({
            where: {
            },
            include: {
                party_leaders: {
                    where: {
                        role: 'PRESIDENT',
                        endDate: {
                            not: null,
                        }
                    },
                    include: {
                        leader: true,
                    },
                },
            },
        });

        return parties
            .filter((party) => {
                const established = new Date(party.startDate);
                return (
                    established.getDate() === currentDay &&
                    established.getMonth() + 1 === currentMonth &&
                    today.getFullYear() > established.getFullYear()
                );
            })
            .map((party) => {
                const established = new Date(party.startDate);
                const years = today.getFullYear() - established.getFullYear();

                return {
                    id: party.id,
                    name: party.name || 'a political party',
                    shortName: party.localName || '',
                    establishedDate: established,
                    years,
                    isActive: !party.endDate,
                    leaderName: party.party_leaders?.[0]?.leader?.name || 'Unknown',
                    logo: party.logo || null,
                };
            });
    }

    generatePartyAnniversaryPost(party) {
        const {
            name,
            shortName,
            leaderName,
            establishedDate,
            years
        } = party;

        const establishedDateStr = establishedDate.toLocaleDateString('en-GB');

        return `🎉 आज ${establishedDateStr} मा स्थापना भएको ${name} (${shortName}) पार्टीले आफ्नो ${years} औं वार्षिकोत्सव मनाइरहेको छ। अध्यक्ष ${leaderName} को नेतृत्वमा यो पार्टीले नेपालको राजनीतिमा महत्वपूर्ण भूमिका खेलेको छ।

यसको योगदान, यात्राको समीक्षा, र भविष्यको दृष्टिकोणबारे तपाईंको विचारहरू कमेन्टमा लेख्नुहोस्।


🎉Today marks the ${years} year anniversary of the formation of ${name} (${shortName}), established on ${establishedDateStr}. Under the leadership of ${leaderName}, the party has played a significant role in shaping Nepal's political landscape.


Let us take a moment to reflect on its achievements, evolution, and vision for the future.


#PartyAnniversary #NepalPolitics #राजनीति`;
    }
}
