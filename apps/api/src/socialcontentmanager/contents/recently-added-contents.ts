import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import { getImageUrlWithFallback } from 'src/utils';
import { truncate } from 'lodash';

export class RecentlyAddedContents extends ISocialMediaContent {
  static contentName = SocialMediaContentTypeEnum.TOP_RATED_CONTENTS;

  getRecords(options?: ISocialMediaContentResponseOptions): Promise<any[]> {
    return this.params.contents.getRecentlyAddedContents(options);
  }

  async getContent(
    options?: ISocialMediaContentResponseOptions,
  ): ISocialMediaContentResponseType {
    const records = await this.getRecords(options);
    return this.getContentsFromRecords(records, options);
  }

  async getContentsFromRecords(
    records: any[],
    options?: ISocialMediaContentResponseOptions,
  ): ISocialMediaContentResponseType {
    return records.map((content: any) => ({
      image: getImageUrlWithFallback(
        content.resource?.img || content.resource?.logo,
        content.resource?.ecCandidateID,
      ),
      comment: `Learn more about ${content.title} on Nepal Tracks - https://www.nepaltracks.com/contents/${content.resourceType}/${content.contentType}/${content.id}`,
      content: `${content.title}

${content.summary ? truncate(content.summary, { length: 5000 }) : ''}

${truncate(content.content, { length: 500 })}
            

https://www.nepaltracks.com/contents/${content.resourceType}/${
        content.contentType
      }/${content.id}
#NepalTracks #NepalPolitics #TopRatedContents
                        `.trim(),
    }));
  }
}
