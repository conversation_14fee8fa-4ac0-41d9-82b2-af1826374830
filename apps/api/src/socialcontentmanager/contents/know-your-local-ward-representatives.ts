import { CandidacyTypeEnum } from '@prisma/client';
import {
  ISocialMediaContent,
  ISocialMediaContentResponseOptions,
  ISocialMediaContentResponseType,
} from './ISocialMediaContent';
import { SocialMediaContentTypeEnum } from './SocialMediaContentTypeEnum';
import {
  formatToStandardReadableDate,
  getImageUrlWithFallback,
} from 'src/utils';

export class KnowYourLocalWardRepresentatives extends ISocialMediaContent {
  static contentName =
    SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES;

  getRandomCandidateType() {
    const candidacyTypes = [
      CandidacyTypeEnum.WARD_CHAIRPERSON,
      CandidacyTypeEnum.WARD_MEMBER,
      CandidacyTypeEnum.WARD_MEMBER_DALIT,
      CandidacyTypeEnum.WARD_MEMBER_FEMALE,
    ];
    const randomType =
      candidacyTypes[Math.floor(Math.random() * candidacyTypes.length)];
    return randomType;
  }

  async getRecords(
    options?: ISocialMediaContentResponseOptions,
  ): Promise<any[]> {
    const limit = options?.limit || 1;
    const randomType = Array.from({ length: 4 }, () =>
      this.getRandomCandidateType(),
    );

    // const randomType = listOfCandidacyTypes[Math.floor(Math.random() * listOfCandidacyTypes.length)];
    // const randomType = listOfCandidacyTypes[0];
    const totalCount = await this.params.prismaService.election_results.count({
      where: {
        isElected: true,
        candidacyType: {
          name: {
            in: randomType,
          },
        },
        municipals: {
          is: {},
        },
      },
    });

    if (!totalCount) return null;

    // Step 2: Calculate random offset safely
    const maxOffset = Math.max(0, totalCount - limit);
    const randomOffset = options?.page
      ? options.page * limit
      : Math.floor(Math.random() * (maxOffset + 1)); // safe random offset

    const results = await this.params.prismaService.election_results.findMany({
      skip: randomOffset,
      take: limit || 1,
      where: {
        isElected: true,
        candidacyType: {
          name: {
            in: randomType,
          },
        },
        municipals: {
          is: {},
        },
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        parties: true,
        elections: true,
        candidacyType: true,
        states: true,
        ward: true,
        municipals: {
          include: {
            districts: true,
            states: true,
          },
        },
      },
    });

    return results;
  }
  async getContent(): ISocialMediaContentResponseType {
    const results = await this.getRecords();
    if (!results?.length) {
      return null;
    }

    const result = results[0];
    const leader = result.leaders;
    const party = result.parties;
    const district = result.municipals?.districts;
    const states = result?.states;
    const municipal = result.municipals;
    const ward = result.ward;
    return [
      {
        comment: `Learn more about ${
          leader.localName || leader.name
        } on Nepal Tracks - https://www.nepaltracks.com/leaders/${leader.id}`,
        image: getImageUrlWithFallback(leader.img, leader.ecCandidateID),
        content: `
    🏙️ तपाईंको स्थानीय प्रतिनिधि चिन्नुहोस् 🏙️ (${
      ward ? `वार्ड ${ward?.localName || ward?.name},` : ''
    } ${municipal ? (municipal?.localName || municipal?.name) + ',' : ''} ${
          district ? district?.localName || district?.name : ''
        } - ${result?.area || ''}, ${states?.localName || states?.name || ''} )
    
    पद: ${result.candidacyType.localName}
    नाम: ${leader.localName || leader.name}
    पार्टी: ${party?.localName || party?.name || 'स्वतन्त्र'}
    प्राप्त मत: ${result.voteCount?.toLocaleString('en-US') || 'N/A'}
    नगरपालिका: ${municipal?.localName || municipal?.name}
    जिल्ला: ${district?.localName || district?.name}
    प्रदेश: ${states?.localName || states?.name}
    चुनाव: ${result.elections.name} (${formatToStandardReadableDate(
          result.elections.year,
        )})
    
    उहाँले गरेका कामहरू र जनतालाई दिनुभएको वाचा/प्रतिज्ञाहरूको समीक्षा गर्नुहोस्।
    
    https://www.nepaltracks.com/leaders/${leader.id}
    ${
      result.ward?.id
        ? `https://www.nepaltracks.com/wards/${result.ward.id}`
        : `https://www.nepaltracks.com/municipals/${result.municipals.id}`
    }
    
    #NepalTracks #LocalRepresentatives #NepalPolitics
            `.trim(),
      },
    ];
  }
}
