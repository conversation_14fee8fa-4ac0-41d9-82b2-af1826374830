import { Injectable, Logger } from '@nestjs/common';
import { CONTENT_TYPE, contents, RESOURCE_TYPE } from '@prisma/client';
import axios from 'axios';
import { PrismaService } from 'nestjs-prisma';

@Injectable()
export class RedditPostService {
  private readonly subreddit = 'nepaltracks';
  private readonly logger = new Logger(RedditPostService.name);

  constructor(private readonly prisma: PrismaService) {}

  async fetchAndSaveHighUpvotePosts(): Promise<void> {
    try {
      const redditUrl = `https://www.reddit.com/r/${this.subreddit}/hot.json?limit=50`;
      const metaUrl = `https://www.reddit.com/r/${this.subreddit}/about.json`;

      const [postsResp, metaResp] = await Promise.all([
        axios.get(redditUrl),
        axios.get(metaUrl),
      ]);

      const totalMembers = metaResp.data.data.subscribers;
      const posts = postsResp.data.data.children;

      for (const post of posts) {
        try {
          const data = post.data;

          const estimatedUpvotes = Math.round(data.ups * data.upvote_ratio);
          // if (data.ups < 10) continue;
          const requiredVotes = totalMembers * 0.6;

          if (estimatedUpvotes < requiredVotes) continue;

          const parsed = this.parseTitle(data.title);
          if (!parsed) {
            this.logger.warn(`Skipped post due to title format: ${data.title}`);
            continue;
          }

          const {
            contentType,
            resourceType,
            resourceId,
            parentContentId,
            actualTitle,
          } = parsed;

          const nextContentType =
            contentType ||
            ((data.link_flair_text?.toUpperCase() ??
              'ANNOUNCEMENT') as CONTENT_TYPE);
          const isLinkPost = !data.is_self;

          if (
            contentType === 'MISC' ||
            contentType === 'DISCUSSIONS' ||
            contentType === 'DEFAULT' ||
            contentType === 'SUMMARY' ||
            contentType === 'SUMMARY_NP'
          )
            continue;

          const payload: contents = {
            resourceType: resourceType as RESOURCE_TYPE,
            resourceId: parseInt(resourceId),
            contentType: nextContentType as CONTENT_TYPE,
            contentStatus: 'NONE',
            title: actualTitle,
            content: isLinkPost ? null : data.selftext,
            cmsLink: isLinkPost ? data.url : null,
            code: data.id,
            id: 0, // Default value for id ACHIEVEMENTS
            isDeleted: true, // Default value for isDeleted
            slug: '', // Default value for slug
            createdAt: new Date(), // Default value for createdAt
            updatedAt: new Date(), // Default value for updatedAt
            eventDate: null, // Default value for eventDate
            eventDueDate: null, // Default value for eventDueDate
            eventEndDate: null, // Default value for eventEndDate
            parentContentId: isNaN(+parentContentId) ? null : +parentContentId, // Default value for parentContentId
            summary: '', // Default value for summary
            metadata: {
              upvotes: data.ups,
              upvoteRatio: data.upvote_ratio,
              author: data.author,
            },
          };

          delete payload.id;
          this.logger.log(`Saving post: ${data.id} - ${actualTitle}`);
          await this.prisma.contents.upsert({
            where: { code: data.id },
            create: payload,
            update: payload,
          });

          this.logger.log(`Saved post: ${data.id} - ${actualTitle}`);
        } catch (e) {
          this.logger.error(
            `Failed to save post: ${post.id} - ${post.data.title}`,
          );
          this.logger.error(e);
        }
      }
    } catch (err) {
      this.logger.error(err);
    }
  }

  private parseTitle(title: string): {
    contentType: string;
    resourceType: string;
    resourceId: string;
    actualTitle: string;
    parentContentId?: string;
  } | null {
    const regex = /^\[([A-Z]+)-([A-Z]+)-(\d+)(?::(\d+))?\]\s+(.*)$/i;
    const match = title.match(regex);

    if (!match) return null;

    const [
      ,
      contentType,
      resourceType,
      resourceId,
      parentContentId,
      actualTitle,
    ] = match;

    return {
      contentType,
      resourceType,
      resourceId,
      parentContentId,
      actualTitle,
    };
  }
}
