import { MiddlewareConsumer, Module } from '@nestjs/common';
import { SocialContentManagerService } from './socialcontentmanager.service';
import { SocialcontentmanagerController } from './socialcontentmanager.controller';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { PrismaService } from 'src/prisma.service';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { MediasService } from 'src/medias/medias.service';
import { RedditPostService } from './reddit-post.service';
import { HttpService } from '@nestjs/axios';
import { BasicAuthMiddleware } from 'src/basic-auth.middleware';
import { ProjectsService } from 'src/projects/projects.service';
import { DepartmentsService } from 'src/modules/departments/departments.service';

@Module({
  providers: [
    SocialContentManagerService,
    PrismaService,
    RatingsService,
    LeadersService,
    PartiesService,
    GovernmentsService,
    ParliamentsService,
    ElectionsService,
    ContentsService,
    EntityReview,
    most_viewedService,
    AiService,
    RecommendationService,
    MediasService,
    RedditPostService,
    ProjectsService,
    DepartmentsService,
  ],
  controllers: [SocialcontentmanagerController],
})
export class SocialcontentmanagerModule {
  configure(consumer: MiddlewareConsumer) {}
}
