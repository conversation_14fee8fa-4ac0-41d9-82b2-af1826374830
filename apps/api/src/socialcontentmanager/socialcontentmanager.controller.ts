import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Controller,
  Get,
  Param,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { SocialContentManagerService } from './socialcontentmanager.service';
import { SocialMediaContentTypeEnum } from './contents/SocialMediaContentTypeEnum';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { isAppEnvDev } from 'src/utils';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';
import { PrismaService } from 'src/prisma.service';
import { ContentsService } from 'src/contents/contents.service';
import { CONTENT_TYPE, RESOURCE_TYPE } from '@prisma/client';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { InjectQueue } from '@nestjs/bullmq';
import { QUEUE_AI_IMAGE_FETCHER, QUEUE_SEARCH_RESULT } from 'src/constants';
import { Queue } from 'bullmq';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { RedditPostService } from './reddit-post.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';

@Controller('socialcontentmanager')
@UseInterceptors(RequestOriginCacheInterceptor)
export class SocialcontentmanagerController {
  constructor(
    private readonly socialContentManagerService: SocialContentManagerService,
    private prismaService: PrismaService,
    private contentsService: ContentsService,
    @InjectQueue(QUEUE_AI_IMAGE_FETCHER)
    private queueService: Queue,
    private configService: ConfigService,
    private redditPostService: RedditPostService,
    private leadersService: LeadersService,
    @InjectQueue(QUEUE_SEARCH_RESULT)
    private queueServiceNews: Queue,
  ) {}

  @CacheKey('socialcontentmanager-analytics')
  @CacheTTL(20000)
  @Get('/analytics')
  analytics(@Query() query: { partyId?: number } & PaginationSortSearchDto) {
    return this.socialContentManagerService.analytics(query);
  }

  @Get('/recently-added-contents')
  @Cron(CronExpression.EVERY_HOUR)
  // @Cron(CronExpression.EVERY_5_SECONDS)
  async postRecentlyAddedContents(params?: { postSummary?: boolean }) {
    if (isAppEnvDev()) return;
    let postContentType: SocialMediaContentTypeEnum;

    const contents = [SocialMediaContentTypeEnum.RECENTLY_ADDED_CONTENTS];
    postContentType = contents[Math.floor(Math.random() * contents.length)];
    const postedItems = await this.prismaService.kvStore.findFirst({
      where: {
        key: 'recently-posted-contents',
      },
    });

    const postedItemsIds = (postedItems?.value || []) as number[];
    const content =
      this.socialContentManagerService.getContentClassObjectByType(
        postContentType,
      );
    const filter = {
      notIn: postedItemsIds,
      limit: 10,
      contentTypeNotIn: (params?.postSummary
        ? undefined
        : ['SUMMARY', 'SUMMARY_NP']) as CONTENT_TYPE[],
      contentTypeIn: (params?.postSummary
        ? ['SUMMARY', 'SUMMARY_NP']
        : undefined) as CONTENT_TYPE[],
    };
    const records = await content.getRecords(filter);
    if (!records.length) {
      this.postRecentlyAddedContents({ postSummary: true });
      return;
    }

    const [item, ...items] = records;
    const response = await content.getContentsFromRecords([item]);

    if (!response.length) return;
    const deduped = Array.from(new Set([...postedItemsIds, item.id]));
    await this.prismaService.kvStore.upsert({
      update: {
        value: deduped,
      },
      create: {
        key: 'recently-posted-contents',
        value: deduped,
      },
      where: {
        key: 'recently-posted-contents',
      },
    });
    await this.socialContentManagerService.postToFacebookWithImage(response[0]);
  }

  @Get('/half-hour-cron')
  @Cron(CronExpression.EVERY_30_MINUTES)
  // @Cron(CronExpression.EVERY_5_SECONDS)
  async postToFacebookHalfHour() {
    if (isAppEnvDev()) return;

    await this.contentWithoutContent();
    await this.representativesPoster();

    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const halfHourSlot = minute < 30 ? 0 : 30;
    const postContentType = this.getContentTypeForSlot(hour, halfHourSlot);

    if (!postContentType) {
      console.log(`No task scheduled for ${hour}:${minute}`);
      return;
    }

    return this.socialContentManagerService.post(postContentType);
  }

  private getContentTypeForSlot(
    hour: number,
    half: 0 | 30,
  ): SocialMediaContentTypeEnum | null {
    const key = `${hour}:${half}`;

    const scheduleMap: Record<string, SocialMediaContentTypeEnum> = {
      '0:0': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '0:30': SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
      '1:0': SocialMediaContentTypeEnum.TOP_RATED_LEADER,
      '1:30': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '2:0': SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
      '2:30': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '3:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '3:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      '4:0': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      '4:30': SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
      '5:0': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '5:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      '6:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '6:30': SocialMediaContentTypeEnum.TOP_RATED_LEADER,
      '7:0': SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES,
      '7:30': SocialMediaContentTypeEnum.BIRTHDAY_POST,
      '8:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '8:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      '9:0': SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS,
      '9:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      '10:0': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '10:30': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '11:0': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      '11:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      '12:0': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '12:30': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '13:0': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      '13:30': SocialMediaContentTypeEnum.KNOW_YOUR_PROVINCIAL_REPRESENTATIVES,
      '14:0': SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
      '14:30': SocialMediaContentTypeEnum.TOP_RATED_LEADER,
      '15:0': SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
      '15:30': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '16:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '16:30': SocialMediaContentTypeEnum.TOP_RATED_PARTY,
      '17:0': SocialMediaContentTypeEnum.TOP_RATED_LEADER,
      '17:30': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '18:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '18:30': SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
      '19:0': SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES,
      '19:30': SocialMediaContentTypeEnum.BIRTHDAY_POST,
      '20:0': SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS,
      '20:30': SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      '21:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '21:30': SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      '22:0': SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      '22:30': SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
      '23:0': SocialMediaContentTypeEnum.TOP_RATED_PARTY,
      '23:30': SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
    };

    return scheduleMap[key] ?? null;
  }

  @Get('/test-post/:type')
  testPost(@Param('type') type: string) {
    const postContentType = SocialMediaContentTypeEnum[type];
    return this.socialContentManagerService.getContentsByType(postContentType);
  }

  @Get('/test-post-send/:type')
  testPostSend(@Param('type') type: string) {
    const postContentType = SocialMediaContentTypeEnum[type];
    return this.socialContentManagerService.post(postContentType);
  }

  private contentWithoutContent = async () => {
    try {
      const contents =
        await this.contentsService.getContentsWithCMSLinkAndNoContent({
          limit: 10,
          contentTypeNotIn: ['SUMMARY', 'SUMMARY_NP'],
        });
      for (let i = 0; i < contents.length; i++) {
        const content = contents[i];
        await this.contentsService.saveContentFromCMSLink(content);
      }
      return contents;
    } catch (e) {
      console.log(e);
    }
  };

  private representativesPoster = async () => {
    const currentHour = new Date().getHours();
    let postContentType1: SocialMediaContentTypeEnum;

    const contents = [
      SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      SocialMediaContentTypeEnum.TOP_RATED_LEADER,
      SocialMediaContentTypeEnum.TOP_RATED_CONTENTS,
      SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
      SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES,
      SocialMediaContentTypeEnum.BIRTHDAY_POST,
      SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS,
      SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      SocialMediaContentTypeEnum.KNOW_YOUR_PROVINCIAL_REPRESENTATIVES,
      SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
      SocialMediaContentTypeEnum.TOP_RATED_PARTY,
    ];
    postContentType1 = contents[Math.floor(Math.random() * contents.length)];

    await this.socialContentManagerService.post(postContentType1);
  };

  @Get('/test-post-image')
  @Cron(CronExpression.EVERY_WEEKEND)
  async testPostImage(
    @Param('type') type: string,
    @Query('leaders') leadersRaw: string,
    @Query('parties') partiesRaw: string,
    @Query('crawler') crawler: string,
    @Query('action') action?: string,
  ) {
    let dashboardQuery;
    const allLeaders = new Map();

    const categories = [
      'mostLiked',
      // 'most_viewed',
      // 'recentlyAdded'
    ];
    if (!leadersRaw && !partiesRaw) {
      dashboardQuery = await axios.get(
        isAppEnvDev()
          ? `http://localhost:${this.configService.get(
              'APP_PORT',
            )}/api/v1/dashboard/analytics`
          : 'https://api.nepaltracks.com/api/v1/dashboard/analytics',
        {
          params: {},
        },
      );
      const data = dashboardQuery.data?.data;
      const leaders = data.leaders;
      categories.forEach((category) => {
        leaders[category].items.forEach((item: any) => {
          allLeaders.set(item.id, item);
        });
      });
    }
    if (leadersRaw && !partiesRaw) {
      const formattedLeaders = leadersRaw.split(',');
      const leaders = await this.leadersService.getLeadersByIds(
        formattedLeaders.map((item) => +item),
      );
      leaders.forEach((item) => {
        allLeaders.set(item.id, item);
      });
    }

    const onlyLeaders = Array.from(allLeaders.values());
    if (!action || action === 'image-fetcher') {
      await this.queueService.addBulk(
        onlyLeaders.map((item: any) => ({
          name: 'image-fetcher',
          data: {
            payload: {
              entityId: item.id,
              entityType: 'leaders',
              entityName: item.name,
              keyword: item.name,
              crawler: crawler,
            },
          },
        })),
      );
    }

    if (!action || action === 'search-result') {
      await this.queueServiceNews.addBulk(
        onlyLeaders.map((item: any) => ({
          name: 'search-result',
          data: {
            payload: {
              resourceId: item.id,
              resourceType: RESOURCE_TYPE.LEADER,
              contentType: CONTENT_TYPE.NEWS,
              query: item.name,
            },
          },
        })),
      );
    }
    return onlyLeaders.map((item: any) => item.id);
  }

  @Get('/reddit-posts')
  @Cron(CronExpression.EVERY_DAY_AT_10AM)
  async fetchAndSaveHighUpvotePosts() {
    return this.redditPostService.fetchAndSaveHighUpvotePosts();
  }
}
