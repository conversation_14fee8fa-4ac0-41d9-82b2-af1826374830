import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ContentsService } from 'src/contents/contents.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';
import { PrismaService } from 'src/prisma.service';
import { LeadersService } from 'src/modules/leaders/leaders.service';
import { PartiesService } from 'src/modules/parties/parties.service';
import { GovernmentsService } from 'src/modules/governments/governments.service';
import { ParliamentsService } from 'src/parliaments/parliaments.service';
import { ElectionsService } from 'src/modules/elections/elections.service';
import { ConfigService } from '@nestjs/config';
import { BirthdayPost } from './contents/birthday-post';
import { SocialMediaContentTypeEnum } from './contents/SocialMediaContentTypeEnum';
import {
  ISocialMediaContent,
  ISocialMediaContentResponse,
  ISocialMediaContentResponseOptions,
} from './contents/ISocialMediaContent';
import { AnniversaryParties } from './contents/anniversary-parties';
import { AnniversaryGovernments } from './contents/anniversary-governments';
import { KnowYourLocalRepresentatives } from './contents/know-your-local-representatives';
import { RandomElectionResults } from './contents/random-election-results';
import { KnowYourFederalRepresentatives } from './contents/know-your-federal-representatives';
import { KnowYourProvincialRepresentatives } from './contents/know-your-provincial-representatives';
import { KnowYourLocalWardRepresentatives } from './contents/know-your-local-ward-representatives';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { RandomFromRecommendation } from './contents/random-from-recommendation';
import { TopRatedLeader } from './contents/top-rated-leader';
import { TopRatedParty } from './contents/top-rated-party';
import { TopRatedContents } from './contents/top-rated-contents';
import { TwitterApi } from 'twitter-api-v2';
import { truncate } from 'lodash';
import { RecentlyAddedContents } from './contents/recently-added-contents';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';

@Injectable()
export class SocialContentManagerService {
  private readonly logger = new Logger(SocialContentManagerService.name);
  private postMappedToClass: { [key: string]: ISocialMediaContent };
  private twitterClient: TwitterApi;

  private mappedClasses: [
    SocialMediaContentTypeEnum,
    new (args: any) => ISocialMediaContent,
  ][] = [
    [SocialMediaContentTypeEnum.BIRTHDAY_POST, BirthdayPost],
    [SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES, AnniversaryParties],
    [
      SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS,
      AnniversaryGovernments,
    ],
    [
      SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
      KnowYourLocalRepresentatives,
    ],
    [
      SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
      KnowYourFederalRepresentatives,
    ],
    [
      SocialMediaContentTypeEnum.KNOW_YOUR_PROVINCIAL_REPRESENTATIVES,
      KnowYourProvincialRepresentatives,
    ],
    [
      SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
      KnowYourLocalWardRepresentatives,
    ],
    [SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS, RandomElectionResults],
    [
      SocialMediaContentTypeEnum.RANDOM_FROM_RECOMMENDATION,
      RandomFromRecommendation,
    ],
    [SocialMediaContentTypeEnum.TOP_RATED_LEADER, TopRatedLeader],
    [SocialMediaContentTypeEnum.TOP_RATED_PARTY, TopRatedParty],
    [SocialMediaContentTypeEnum.TOP_RATED_CONTENTS, TopRatedContents],
    [SocialMediaContentTypeEnum.RECENTLY_ADDED_CONTENTS, RecentlyAddedContents],
  ];
  constructor(
    private prismaService: PrismaService,
    private leaderService: LeadersService,
    private partiesService: PartiesService,
    private governmentsService: GovernmentsService,
    private parliamentsService: ParliamentsService,
    private electionsService: ElectionsService,
    private contentsService: ContentsService,
    private entityReview: EntityReview,
    private aiService: AiService,
    private mostViewedService: most_viewedService,
    private configService: ConfigService,
    private recommendationService: RecommendationService,
  ) {
    this.postMappedToClass = {};
    for (const [key, PostClass] of this.mappedClasses) {
      //@ts-expect-error
      this.postMappedToClass[key] = PostClass;
    }

    this.twitterClient = new TwitterApi({
      appKey: this.configService.getOrThrow('TWITTER_API_KEY'),
      appSecret: this.configService.getOrThrow('TWITTER_API_SECRET'),
      accessToken: this.configService.getOrThrow('TWITTER_ACCESS_TOKEN'),
      accessSecret: this.configService.getOrThrow('TWITTER_ACCESS_SECRET'),
    });
  }

  getContentClassObjectByType(
    type: SocialMediaContentTypeEnum,
  ): ISocialMediaContent {
    const PostClass = this.postMappedToClass[type];
    if (!PostClass) {
      throw new Error(`Post type ${type} not found`);
    }
    //@ts-expect-error
    const content = new PostClass({
      leaders: this.leaderService,
      parties: this.partiesService,
      governments: this.governmentsService,
      parliaments: this.parliamentsService,
      elections: this.electionsService,
      contents: this.contentsService,
      entityReview: this.entityReview,
      aiService: this.aiService,
      mostViewedService: this.mostViewedService,
      configService: this.configService,
      recommendation: this.recommendationService,
      prismaService: this.prismaService,
    });
    return content;
  }

  async getContentsByType(
    type: SocialMediaContentTypeEnum,
    options?: ISocialMediaContentResponseOptions,
  ) {
    const content = this.getContentClassObjectByType(type);
    let contents = await content.getContent(options);
    return contents;
  }

  async post(type: SocialMediaContentTypeEnum) {
    const contents = await this.getContentsByType(type);
    const responses = [];
    for (let i = 0; i < contents.length; i++) {
      // Introduce delay for each post
      const item = contents[i];
      const delayMs = i * 60000; // Increment delay for each post (3 minutes)
      responses.push(this.postWithDelay(item, delayMs));
    }

    const d = await Promise.all(responses); // Wait for all posts to complete

    return d;
  }

  async postToFacebook(message: string): Promise<any> {
    try {
      const url = `https://graph.facebook.com/v19.0/${this.configService.getOrThrow(
        'APP_FACEBOOK_PAGE_ID',
      )}/feed`;

      const { data } = await axios.post(url, null, {
        params: {
          message: 'from tjhe api',
          access_token: this.configService.getOrThrow(
            'APP_FACEBOOK_PAGE_ACCESS_TOKEN',
          ),
        },
      });

      this.logger.log(`Posted to Facebook: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(
        'Failed to post to Facebook',
        error?.response?.data || error.message,
      );
      throw error;
    }
  }

  async extendFacebookAccessToken(
    appId: string,
    appSecret: string,
    shortLivedToken: string,
  ): Promise<any> {
    const url = 'https://graph.facebook.com/v19.0/oauth/access_token';

    try {
      const { data } = await axios.get(url, {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: process.env.FACEBOOK_APP_ID,
          client_secret: process.env.FACEBOOK_APP_SECRET,
          fb_exchange_token: this.configService.getOrThrow(
            'APP_FACEBOOK_PAGE_ACCESS_TOKEN',
          ),
        },
      });

      this.logger.log('Extended access token retrieved successfully');
      return data; // contains access_token and expires_in
    } catch (error) {
      this.logger.error(
        'Failed to extend access token',
        error?.response?.data || error.message,
      );
      throw error;
    }
  }

  async postToFacebookWithImage({
    image,
    content,
  }: ISocialMediaContentResponse): Promise<any> {
    try {
      let photoUploadResponse, photoId;
      const photoIds: string[] = [];
      if (image) {
        try {
          const images = image.split(',');

          for (const img of images) {
            const photoUploadUrl = `https://graph.facebook.com/v19.0/${this.configService.getOrThrow(
              'APP_FACEBOOK_PAGE_ID',
            )}/photos`;

            const photoUploadResponse = await axios.post(photoUploadUrl, null, {
              params: {
                url: img,
                published: false,
                access_token: this.configService.getOrThrow(
                  'APP_FACEBOOK_PAGE_ACCESS_TOKEN',
                ),
              },
            });

            photoIds.push(photoUploadResponse.data.id);
          }
        } catch (e) {
          console.log(e);
        }
      }
      // Step 2: Create post with attached photo
      const postUrl = `https://graph.facebook.com/v19.0/${this.configService.getOrThrow(
        'APP_FACEBOOK_PAGE_ID',
      )}/feed`;
      const postResponse = await axios.post(postUrl, null, {
        params: {
          message: content,
          attached_media:
            image && photoIds.length
              ? JSON.stringify(photoIds.map((id) => ({ media_fbid: id })))
              : undefined,
          access_token: this.configService.getOrThrow(
            'APP_FACEBOOK_PAGE_ACCESS_TOKEN',
          ),
        },
      });

      this.logger.log(
        `Posted image to Facebook: ${JSON.stringify(postResponse.data)}`,
      );
      return postResponse.data;
    } catch (error) {
      this.logger.error(
        'Failed to post image to Facebook',
        error?.response?.data || error.message,
      );
      throw error;
    }
  }

  async postToTwitter(
    post: ISocialMediaContentResponse,
    in_reply_to_status_id?: string,
  ) {
    try {
      const { content: tweetText, image: mediaUrl, comment } = post;
      const texts = tweetText.split('\n');
      const hashtags = texts.pop();
      const maxTweetLength = 280;
      const separator = '\n';
      const reservedLength = hashtags.length + separator.length + 3; // 3 for the ellipsis
      // Trim the tweet text to make room for ellipsis and hashtags
      const trimmedTweet = tweetText.slice(0, maxTweetLength - reservedLength);

      // Add ellipsis and then the hashtags
      let text = in_reply_to_status_id
        ? tweetText
        : trimmedTweet +
          (in_reply_to_status_id ? '' : '...') +
          separator +
          hashtags;
      const images = mediaUrl?.split(',') || [];

      let mediaIds = [];
      if (images.length) {
        for (const img of images) {
          try {
            const response = await axios.get(img, {
              responseType: 'arraybuffer',
            });

            const mediaBuffer = Buffer.from(response.data, 'binary');

            const mediaId = await this.twitterClient.v1.uploadMedia(
              mediaBuffer,
              {
                type: 'jpg', // Change to 'png' or 'gif' based on the file type
              },
            );
            mediaIds.push(mediaId);
          } catch (err) {
            console.log(err);
          }
        }
      }

      const response = await this.twitterClient.v2.tweet(
        truncate(text, { length: 280 }),
        {
          media: mediaIds.length
            ? {
                media_ids: mediaIds.slice(0, 4) as
                  | [string]
                  | [string, string]
                  | [string, string, string]
                  | [string, string, string, string],
              }
            : undefined,
          reply: in_reply_to_status_id && {
            in_reply_to_tweet_id: in_reply_to_status_id,
          },
        },
      );
      if (!in_reply_to_status_id) {
        await this.postToTwitter({ content: post.comment }, response.data.id);
      }
      console.log('Tweet posted:', response);
    } catch (err) {
      console.error('Error posting tweet:', err);
    }
  }
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Function to handle posting with image and delay
  async postWithDelay(post: ISocialMediaContentResponse, delayMs = 180000) {
    // Wait for the delay before posting
    await this.delay(delayMs);
    // Now post to Facebook with image and content
    const response = await this.postToFacebookWithImage(post);
    if (post.comment) {
      await this.postComment(response.id, post.comment);
    }
    await this.postToTwitter(post);
    return response;
  }

  async postComment(postId, commentText) {
    const url = `https://graph.facebook.com/${postId}/comments?message=${encodeURIComponent(
      commentText,
    )}&access_token=${this.configService.getOrThrow(
      'APP_FACEBOOK_PAGE_ACCESS_TOKEN',
    )}`;

    // Make a POST request to Facebook Graph API
    const response = await fetch(url, {
      method: 'POST',
    });

    const data = await response.json();
    if (data.error) {
      console.error('Error posting comment:', data.error);
    } else {
      console.log('Successfully posted comment:', data);
    }
  }

  async analytics(query: { partyId?: number } & PaginationSortSearchDto) {
    let limit = +query.limit || 10;
    let page = query.page || '1';
    const category = query.category as
      | 'knowYourProvincialRepresentatives'
      | 'randomElectionResults'
      | 'anniversaryParties'
      | 'birthdayPost'
      | 'anniversaryGovernments'
      | 'knowYourLocalRepresentatives'
      | 'knowYourLocalWardRepresentatives'
      | 'knowYourFederalRepresentatives';

    const localRepresentatives =
      !category || category === 'knowYourLocalRepresentatives'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_REPRESENTATIVES,
          ).getRecords({
            limit: query?.limit ? +query?.limit : 10,
            offset: 0,
            page: query?.page ? +query?.page : undefined,
          })
        : [];

    const fedRepresentatives =
      !category || category === 'knowYourFederalRepresentatives'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.KNOW_YOUR_FEDERAL_REPRESENTATIVES,
          ).getRecords({
            limit: query?.limit ? +query?.limit : 10,
            offset: 0,
            page: query?.page ? +query?.page : undefined,
          })
        : [];

    const proRepresentatives =
      !category || category === 'knowYourProvincialRepresentatives'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.KNOW_YOUR_PROVINCIAL_REPRESENTATIVES,
          ).getRecords({
            limit: query?.limit ? +query?.limit : 10,
            offset: 0,
            page: query?.page ? +query?.page : undefined,
          })
        : [];

    const localWardRepresentatives =
      !category || category === 'knowYourLocalWardRepresentatives'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.KNOW_YOUR_LOCAL_WARD_REPRESENTATIVES,
          ).getRecords({
            limit: query?.limit ? +query?.limit : 10,
            offset: 0,
            page: query?.page ? +query?.page : undefined,
          })
        : [];
    const randomElectionResults =
      !category || category === 'randomElectionResults'
        ? await Promise.all(
            Array(10)
              .fill(true)
              .map(async (_, i) => {
                const results = await this.getContentClassObjectByType(
                  SocialMediaContentTypeEnum.RANDOM_ELECTION_RESULTS,
                ).getRecords({
                  limit: 2,
                  category,
                  page: query?.page ? +query?.page : undefined,
                });
                if (!results?.length) return null;
                return {
                  ...results[0],
                  second: results?.[1],
                };
              })
              .filter(Boolean),
          )
        : [];
    const anniversaryParties =
      !category || category === 'anniversaryParties'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.ANNIVERSARY_PARTIES,
          ).getRecords({ limit: 10, offset: 0 })
        : [];
    const birthdayPost =
      !category || category === 'birthdayPost'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.BIRTHDAY_POST,
          ).getRecords({ limit: 10, offset: 0 })
        : [];
    const anniversaryGovernments =
      !category || category === 'anniversaryGovernments'
        ? await this.getContentClassObjectByType(
            SocialMediaContentTypeEnum.ANNIVERSARY_GOVERNMENTS,
          ).getRecords({ limit: 10, offset: 0 })
        : [];

    return {
      anniversaryParties: anniversaryParties,
      birthdayPost: birthdayPost,
      anniversaryGovernments: anniversaryGovernments,
      knowYourLocalRepresentatives: localRepresentatives,
      knowYourLocalWardRepresentatives: localWardRepresentatives,
      knowYourFederalRepresentatives: fedRepresentatives,
      randomElectionResults: randomElectionResults,
      knowYourProvincialRepresentatives: proRepresentatives,
    };
  }
}
