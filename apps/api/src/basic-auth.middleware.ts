import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import basicAuth from 'express-basic-auth';
import * as pathToRegexp from 'path-to-regexp';

@Injectable()
export class BasicAuthMiddleware implements NestMiddleware {
  private routesToProtect = [
    '/reddit-posts',
    '/test-post-image',
    '/test-post-send/:type',
    '/test-post/:type',
    '/half-hour-cron',
    '/socialcontentmanager/test-post-image',
  ];

  private matchRoute(path: string): boolean {
    return this.routesToProtect.some((route) => {
      const keys = [];
      const regexp = pathToRegexp.pathToRegexp(route, keys);
      return regexp.test(path);
    });
  }

  use(request: Request, response: Response, next: NextFunction) {
    // if (!this.matchRoute(request.path)) {
    //   return next(); // Don't apply auth on unrelated routes
    // }

    return basicAuth({
      users: {
        [process.env.BASIC_AUTH_USER || 'mukhiya']:
          process.env.BASIC_AUTH_PASSWORD ||
          process.env.CLOUDFLARE_TURNSTILE_PRIVATE_KEY,
      },
      challenge: true,
      //@ts-expect-error
    })(request, response, next);
  }
}
