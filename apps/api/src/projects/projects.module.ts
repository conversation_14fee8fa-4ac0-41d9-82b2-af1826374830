import { Modu<PERSON> } from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { ProjectsController } from './projects.controller';
import { PrismaService } from 'src/prisma.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { most_viewedService } from 'src/modules/most-viewed/most-viewed.service';

@Module({
  controllers: [ProjectsController],
  providers: [
    ProjectsService,
    PrismaService,
    EntityReview,
    RatingsService,
    ContentsService,
    AiService,
    most_viewedService,
  ],
})
export class ProjectsModule {}
