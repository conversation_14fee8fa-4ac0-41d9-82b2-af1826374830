import { Injectable } from '@nestjs/common';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { PrismaService } from 'src/prisma.service';
import { EntityReview } from 'src/modules/ratings/EntityReview';
import { CommentableType, RESOURCE_TYPE } from '@prisma/client';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';
import { RatingsService } from 'src/modules/ratings/ratings.service';
import { ContentsService } from 'src/contents/contents.service';
import { AiService } from 'src/ai/ai.service';
import { DEFAULT_SUMMARY } from 'src/utils';
import { EntityTypeEnum } from 'src/constants';

@Injectable()
export class ProjectsService {
  constructor(
    private prismaService: PrismaService,
    private entityReview: EntityReview,
    private ratingService: RatingsService,
    private contentsService: ContentsService,
    private aiService: AiService,
  ) {}

  findAll(query: PaginationSortSearchDto = {}) {
    return this.entityReview.findAll(
      {
        model: CommentableType.PROJECT,
        name: 'projects',
      },
      {
        ...query,
        //@ts-expect-error
        include: {},
      },
    );
  }

  async findOne(id: number) {
    const project = await this.prismaService.projects.findFirstOrThrow({
      where: {
        id,
      },
    });
    const projectsBy = await this.prismaService.projects_by.findMany({
      where: {
        projectId: id,
      },
      include: {
        leaders: {
          include: {
            leaders_images: true,
          },
        },
        party: true,
        governments: true,
      },
    });
    const rating = await this.ratingService.ratingAnalysis({
      rateOnId: project.id + '',
      rateOnType: RESOURCE_TYPE.PROJECT,
    });
    const stats = await this.contentsService.getStats(
      project.id,
      RESOURCE_TYPE.PROJECT,
    );
    let summary = (
      await this.contentsService.getContents({
        resourceId: project.id,
        resourceType: RESOURCE_TYPE.PROJECT,
        contentType: 'SUMMARY',
      })
    )?.items?.[0]?.content;

    let summaryNP = (
      await this.contentsService.getContents({
        resourceId: project.id,
        resourceType: RESOURCE_TYPE.PROJECT,
        contentType: 'SUMMARY_NP',
      })
    )?.items?.[0]?.content;

    const response = { ...project, stats, rating };
    if (!summary || !summaryNP) {
      try {
        summary = DEFAULT_SUMMARY;
        await this.aiService.queueSummaryBuilder(
          {
            id: project.id,
            type: EntityTypeEnum.Project,
          },
          EntityTypeEnum.Project + project.id,
        );
      } catch (e) {
        console.log(e);
      }
    }
    return {
      ...response,
      projectsBy: projectsBy
        .map((item) => {
          const resourcesInvolved = [
            [RESOURCE_TYPE.LEADER, item.leaders],
            [RESOURCE_TYPE.PARTY, item.party],
            [RESOURCE_TYPE.GOVERNMENT, item.governments],
          ];

          return resourcesInvolved
            .map(([resourceType, resource]) => ({
              resource,
              resourceType,
            }))
            .filter((item) => item.resource);
        })
        .flat(),
      summary,
      summaryNP,
    };
  }

  async analytics(query: { partyId?: number } & PaginationSortSearchDto) {
    let limit = +query.limit || 10;
    let page = query.page || '1';

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentlyAdded = await this.prismaService.projects.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      where: {
        createdAt: page ? { gt: oneWeekAgo } : undefined,
      },
    });

    let most_viewed = await this.findAll({
      top: 'views',
      limit: limit,
      page,
    });
    let mostLiked = await this.findAll({
      top: 'rates',
      limit: limit,
      page,
    });

    return {
      most_viewed,
      recentlyAdded: { items: recentlyAdded },
      mostLiked,
    };
  }
}
