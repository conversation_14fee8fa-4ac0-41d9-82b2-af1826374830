import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { PaginationSortSearchDto } from 'src/modules/leaders/dto/index.dto';

@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}
  @Get()
  findAll(@Query() query: PaginationSortSearchDto = {}) {
    return this.projectsService.findAll(query);
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number }) {
    return this.projectsService.analytics(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.projectsService.findOne(+id);
  }
}
