import React, { useEffect, useState, useRef, useCallback } from 'react';
// @ts-expect-error
import { FormGroup, SelectAsync } from '@adminjs/design-system';
// @ts-expect-error
import { ApiClient, BasePropertyComponentProps } from 'adminjs';
import axios from 'axios';

const api = new ApiClient();

function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      return new Promise<ReturnType<T>>((resolve) => {
        timeoutRef.current = setTimeout(async () => {
          const result = await callback(...args);
          resolve(result);
        }, delay);
      });
    },
    [callback, delay],
  );
}

const LeadersDropDown = (props: BasePropertyComponentProps) => {
  const { name } = props.property;
  const resourceId = props.property.reference; // Get target resource
  const initialId = props.record.params?.[name];

  const [selectedOption, setSelectedOption] = useState<any>(null);

  // Load label for initial value
  useEffect(() => {
    const loadInitialValue = async () => {
      if (initialId) {
        const response = await api.recordAction({
          resourceId,
          recordId: initialId,
          actionName: 'show',
        });

        const record = response.data.record;

        if (record) {
          const option = {
            value: record.params.id,
            label: record.title,
          };
          setSelectedOption(option);
          props.onChange(name, record.params.id);
        }
      }
    };

    loadInitialValue();
  }, [initialId, name, resourceId]);

  // Sync value changes with record
  useEffect(() => {
    props.onChange(name, selectedOption?.value || null);
  }, [selectedOption]);

  const loadOptions = async (inputValue: string) => {
    const response = await axios.get(
      `/api/v1/search?search=${inputValue}&entities=leader`,
    );
    const records = response.data?.data?.items || [];

    return records.map((record: any) => ({
      value: record.resource.id,
      label: `${record.resource.name} (${record.resource.party_leaders
        ?.map((item: any) => item.party.name)
        .join(', ')})`,
    }));
  };

  // Debounce it with 300ms delay
  const debouncedLoadOptions = useDebouncedCallback(loadOptions, 300);

  return (
    <FormGroup>
      <SelectAsync
        value={selectedOption}
        onChange={(option) => setSelectedOption(option)}
        // @ts-expect-error
        loadOptions={debouncedLoadOptions}
        isClearable
      />
    </FormGroup>
  );
};

export default LeadersDropDown;
