import React from 'react';

const ImageComponent = ({ record, property }) => {
  const url = record?.params?.[property.path];

  if (url) {
    return React.createElement('img', {
      src: url,
      alt: 'Preview',
      style: {
        maxWidth: '100px',
        maxHeight: '80px',
        objectFit: 'contain',
      },
    });
  }

  return React.createElement('span', null, 'No image');
};

export default ImageComponent;
