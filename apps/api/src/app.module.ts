import {
  <PERSON><PERSON><PERSON><PERSON>ceptor,
  Logger,
  MiddlewareConsumer,
  Module,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CommentsModule } from './modules/comments/comments.module';
import { GovernmentsModule } from './modules/governments/governments.module';
import { LeadersModule } from './modules/leaders/leaders.module';
import { PartiesModule } from './modules/parties/parties.module';
import { PrismaService } from './prisma.service';
import { AuthModule } from './modules/auth/auth.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { RatingsModule } from './modules/ratings/ratings.module';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { TransformInterceptor } from './response-transform.interceptor';
import { most_viewedLogMiddlware } from './middleware/MostViewedLogMiddleware';
import { most_viewedModule } from './modules/most-viewed/most-viewed.module';
import { MostViewEventListeners } from './events/listeners/MostViewEventListeners';
import { most_viewedService } from './modules/most-viewed/most-viewed.service';
import { ElectionsModule } from './modules/elections/elections.module';
import { GeoStatesModule } from './modules/geo-states/geo-states.module';
import { GeoDistrictsModule } from './modules/geo-districts/geo-districts.module';
import { GeoMuncipalsModule } from './modules/geo-muncipals/geo-muncipals.module';
import { GeoWardsModule } from './modules/geo-wards/geo-wards.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { PrismaModule, loggingMiddleware } from 'nestjs-prisma';
import { FeedModule } from './feed/feed.module';
import { GeoWardsService } from './modules/geo-wards/geo-wards.service';
import { UsersModule } from './users/users.module';
import { EmailsModule } from './modules/emails/emails.module';
import { UnverifiedUserCreatedListener } from './events/listeners/UnverifiedUserCreatedListener';
import { EmailsService } from './modules/emails/emails.service';
import { ParliamentsModule } from './parliaments/parliaments.module';
import { AdminModule } from './admin/admin.module';
import { ContentsModule } from './contents/contents.module';
import { AiModule } from './ai/ai.module';
import { RecommendationService } from './recommendation/recommendation.service';
import { RecommendationModule } from './recommendation/recommendation.module';
import { LeadersService } from './modules/leaders/leaders.service';
import { EntityReview } from './modules/ratings/EntityReview';
import { RatingsService } from './modules/ratings/ratings.service';
import { ContentsService } from './contents/contents.service';
import { AiService } from './ai/ai.service';
import { PartiesService } from './modules/parties/parties.service';
import { RequestOriginCacheInterceptor } from './interceptors/RequestOriginCacheInterceptor';
import { GovernmentsService } from './modules/governments/governments.service';
import { ElectionsService } from './modules/elections/elections.service';
import { createKeyv } from '@keyv/redis';
import { Keyv } from 'keyv';
import { CacheableMemory } from 'cacheable';
import { CacheModule } from '@nestjs/cache-manager';
import { ProxyAwareThrottlerGuard } from './throttler-behind-proxy.guard';
import { ThrottlerModule } from '@nestjs/throttler';
import { TurnstileModule } from 'nest-cloudflare-turnstile';
import { SocialcontentmanagerModule } from './socialcontentmanager/socialcontentmanager.module';
import { SocialContentManagerService } from './socialcontentmanager/socialcontentmanager.service';
import { ParliamentsService } from './parliaments/parliaments.service';
import { ScheduleModule } from '@nestjs/schedule';
import { apiIdWrapper, isAppEnvDev } from './utils';
import { SearchModule } from './search/search.module';
import { AppLoggerMiddleware } from './http-log.middleware';
import redisStore from 'cache-manager-redis-store';
import redisConfiguration from 'config/redis.configuration';
import { GeoMuncipalsService } from './modules/geo-muncipals/geo-muncipals.service';
import bcrypt from 'bcrypt';
import { SummaryBuilderWorker } from './processors/summary-builder.worker';
import { BullBoardModule } from './bull-board/bull-board.module';
import { QueueModule } from './queue/queue.module';
import { CaslModule } from './casl/casl.module';
import { MediasModule } from './medias/medias.module';
import { MediasService } from './medias/medias.service';
import { ImageFetcherWorker } from './processors/image-fetcher.worker';
import { BasicAuthMiddleware } from './basic-auth.middleware';
import { CompareModule } from './compare/compare.module';
import { SearchResultWorker } from './processors/search-result.worker';
import { ProjectsModule } from './projects/projects.module';
import { ProjectsService } from './projects/projects.service';
import { DepartmentsService } from './modules/departments/departments.service';
import { PollsModule } from './polls/polls.module';

const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  password: 'password',
};

const authenticate = async (email: string, password: string) => {
  if (email === DEFAULT_ADMIN.email && password === DEFAULT_ADMIN.password) {
    return Promise.resolve(DEFAULT_ADMIN);
  }
  return null;
};

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: '.env',
      isGlobal: true,
      cache: true,
      load: [redisConfiguration],
    }),
    ScheduleModule.forRoot(),
    ThrottlerModule.forRoot({
      ttl: 60000,
      limit: 5000,
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          store: redisStore,
          // Store-specific configuration:
          host: configService.get('redis.host', 'localhost'),
          port: configService.get('redis.port', 6379), //configService.getOrThrow('redis.port'),
          db: 1, //!isDev() ? process.env.REDIS_DB_INDEX : undefined,
          max: 5000,
          ttl: 3600 / 2,
        };
      },
    }),
    EventEmitterModule.forRoot(),
    CommentsModule,
    GovernmentsModule,
    PartiesModule,
    AuthModule,
    DepartmentsModule,
    RatingsModule,
    most_viewedModule,
    ElectionsModule,
    GeoStatesModule,
    GeoDistrictsModule,
    GeoMuncipalsModule,
    GeoWardsModule,
    AnalyticsModule,
    PrismaModule.forRoot({
      isGlobal: true,
      prismaServiceOptions: {
        middlewares: [
          // configure your prisma middleware
          loggingMiddleware({
            logger: new Logger('PrismaMiddleware'),
            logLevel: 'log',
          }),
        ],
      },
    }),
    FeedModule,
    GeoWardsModule,
    UsersModule,
    EmailsModule,
    ParliamentsModule,
    AdminModule,
    ContentsModule,
    AiModule,
    RecommendationModule,
    TurnstileModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        const secretKey = config.get<string>(
          'CLOUDFLARE_TURNSTILE_PRIVATE_KEY',
        );
        if (!secretKey) {
          throw new Error('Missing Cloudflare Turnstile secret');
        }

        return {
          secretKey,
          tokenResponse: (req) => req.body.turnstileToken,
        };
      },
    }),
    SocialcontentmanagerModule,
    // import('@adminjs/nestjs').then(({ AdminModule }) =>
    //   import('@adminjs/prisma').then(
    //     ({ Database, Resource, getModelByName }) => {
    //       return import('adminjs').then(({ AdminJS, ComponentLoader }) => {
    //         const componentLoader = new ComponentLoader();
    //         const Components = {
    //           ImageComponent: componentLoader.add(
    //             'ImageComponent',
    //             './adminjs/components/ImageComponent.tsx',
    //           ),
    //           LeadersDropDown: componentLoader.add(
    //             'CustomDropDown',
    //             './adminjs/components/LeadersDropDown.tsx',
    //           ),
    //           // other custom components
    //         };
    //         AdminJS.registerAdapter({ Database, Resource });
    //         return AdminModule.createAdminAsync({
    //           useFactory: () => {
    //             const prisma = new PrismaService();
    //             const models = [
    //               'leaders',
    //               'leaders_images',
    //               'parties',
    //               'users',
    //               'party_leaders',
    //               'parliaments',
    //               'parliament_members',
    //               'governments',
    //               'elections',
    //               'election_results',
    //               'contents',
    //               'states',
    //               'districts',
    //               'municipals',
    //               'wards',
    //               'candidacy_types',
    //               'projects',
    //               'projects_by',
    //               'medias',
    //               'ratings',
    //               'cabinet_members',
    //               'departments',
    //               'most_viewed',
    //               'user_address',
    //               'kvStore',
    //             ];
    //             return {
    //               adminJsOptions: {
    //                 branding: {
    //                   companyName: 'NepalTracks Admin.',
    //                 },
    //                 rootPath: '/api/v1/xyz-mukhiya',
    //                 logoutPath: '/api/v1/xyz-mukhiya/exit',
    //                 loginPath: '/api/v1/xyz-mukhiya/sign-in',
    //                 componentLoader,
    //                 resources: models.map((model) => {
    //                   const baseResource = {
    //                     resource: {
    //                       model: getModelByName(model),
    //                       client: prisma,
    //                     },

    //                     options: {},
    //                   };

    //                   // Customize specific models
    //                   if (model === 'leaders_images') {
    //                     baseResource.options = {
    //                       // listProperties: ['id', 'leaders', 'url', 'enabled',"isDefault"],
    //                       properties: {
    //                         url: {
    //                           type: 'string',
    //                           components: {
    //                             list: Components.ImageComponent, // see "Writing your own Components"
    //                             show: Components.ImageComponent,
    //                           },
    //                         },
    //                       },
    //                     };
    //                   }
    //                   if (model === 'cabinet_members') {
    //                     baseResource.options = {
    //                       listProperties: [
    //                         'id',
    //                         'party',
    //                         'government',
    //                         'department',
    //                         'leaders',
    //                         'role',
    //                         'rank',
    //                         'position',
    //                         'startedAt',
    //                         'endAt',
    //                       ],
    //                       properties: {
    //                         leaders: {
    //                           type: 'string',
    //                           components: {
    //                             edit: Components.LeadersDropDown, // see "Writing your own Components"
    //                           },
    //                         },
    //                         img: {
    //                           type: 'string',
    //                           components: {
    //                             list: Components.ImageComponent, // see "Writing your own Components"
    //                             show: Components.ImageComponent,
    //                           },
    //                         },
    //                       },
    //                     };
    //                   }

    //                   return baseResource;
    //                 }),
    //               },
    //               auth: isAppEnvDev()
    //                 ? undefined
    //                 : {
    //                     authenticate: async (
    //                       username: string,
    //                       password: string,
    //                     ) => {
    //                       try {
    //                         const adminApp =
    //                           await prisma.kvStore.findFirstOrThrow({
    //                             where: {
    //                               key: apiIdWrapper(username),
    //                             },
    //                           });
    //                         if (!adminApp) return null;
    //                         const apiKey = password;
    //                         const isMatched = await bcrypt
    //                           //@ts-ignore
    //                           .compare(apiKey, adminApp.value?.value);
    //                         if (!isMatched) return null;
    //                         return {
    //                           email: '<EMAIL>',
    //                           id: username,
    //                         };
    //                       } catch (err) {
    //                         return null;
    //                       }
    //                     },
    //                     cookieName: 'adminjs',
    //                     cookiePassword: 'secret',
    //                   },
    //               sessionOptions: {
    //                 resave: true,
    //                 saveUninitialized: true,
    //                 secret: 'secret',
    //               },
    //             };
    //           },
    //         });
    //       });
    //     },
    //   ),
    // ),
    BullBoardModule,
    QueueModule,
    SearchModule,
    LeadersModule,
    CaslModule,
    MediasModule,
    CompareModule,
    ProjectsModule,
    PollsModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ProxyAwareThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: CacheInterceptor,
    // },
    AppService,
    RequestOriginCacheInterceptor,
    most_viewedService,
    PrismaService,
    MostViewEventListeners,
    UnverifiedUserCreatedListener,
    GeoWardsService,
    EmailsService,
    Logger,
    RecommendationService,
    GovernmentsService,
    ContentsService,
    LeadersService,
    EntityReview,
    RatingsService,
    AiService,
    PartiesService,
    ElectionsService,
    SocialContentManagerService,
    ParliamentsService,
    GeoWardsService,
    GeoMuncipalsService,
    SummaryBuilderWorker,
    SearchResultWorker,
    ImageFetcherWorker,
    MediasService,
    ProjectsService,
    DepartmentsService,
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AppLoggerMiddleware).forRoutes('*');
    consumer
      .apply(BasicAuthMiddleware)
      .forRoutes(
        'api/v1/xyz-mukhiya',
        'socialcontentmanager/reddit-posts',
        'socialcontentmanager/test-post-image',
        'socialcontentmanager/test-post-send/:type',
        'socialcontentmanager/test-post/:type',
        'socialcontentmanager/half-hour-cron',
        'socialcontentmanager/socialcontentmanager/test-post-image',
      );

    consumer
      .apply(most_viewedLogMiddlware)
      .forRoutes(
        'leaders/:id',
        'parties/:id',
        'parliament/:id',
        'contents/:id',
        'governments/:id',
        'wards/:id',
        'municipals/:id',
        'elections/:id',
        'medias/:id',
        'projects/:id',
        'departments/:id',
      );
  }
}
