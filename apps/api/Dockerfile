
FROM node:alpine3.16 AS builder
RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app
RUN yarn global add turbo
COPY . .
RUN ls
RUN turbo prune --scope=api --docker

FROM node:alpine3.16 AS installer
RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app

COPY .gitignore .gitignore
COPY --from=builder /app/apps/api/.env /app/apps/api/.env
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/yarn.lock ./yarn.lock
RUN yarn install

COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json


RUN yarn run prisma

RUN yarn turbo run build --filter=api...

FROM node:alpine3.16 AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nepaltracks-api
RUN adduser --system --uid 1001 nepaltracks-api
USER nepaltracks-api
COPY --from=installer /app .
COPY --chown=nepaltracks-api:nepaltracks-api entrypoint.sh entrypoint.sh
COPY --from=installer --chown=nextjs:nodejs /app/apps/api/.env ./apps/api/.env
RUN chmod +x ./entrypoint.sh

ENTRYPOINT ["./entrypoint.sh"]
CMD [ "node" ,"apps/api/dist/src/main.js" ]
