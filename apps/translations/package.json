{"name": "esim-spreadsheet", "version": "1.0.0", "private": true, "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "copy-langs": "rm -rf ../web/src/locales/* && cp -r lang/i18n/* ../web/src/locales"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"marked": "15.0.8", "base-64": "^1.0.0", "dot-prop-immutable": "^2.1.1", "google-spreadsheet": "^3.3.0", "node-fetch": "^3.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"dot-prop-immutable": "^2.1.1", "html-entities": "^2.4.0", "markdown-to-jsx": "^7.4.1", "marked": "^12.0.0"}}