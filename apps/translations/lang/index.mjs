import * as fs from "fs";
import { GoogleSpreadsheet } from "google-spreadsheet";
import * as path from "path";
import { fileURLToPath } from "url";
import { set } from "dot-prop-immutable";
import { marked } from "marked";
import { decode } from "html-entities";

const header = ["Key", "English", "Nepali"];

const fileNameMap = {
  English: "en",
  Nepali: "np",
};
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SHEET_ID = "10zC7TL78SPOxFMRqPrzQ920Jpv2wBFnFXvr976SPorE";

const token = JSON.parse(
  fs.readFileSync("./apps/translations/token.json", "utf-8")
);
const doc = new GoogleSpreadsheet(SHEET_ID);
// Initialize Auth - see https://theoephraim.github.io/node-google-spreadsheet/#/getting-started/authentication
await doc.useServiceAccountAuth({
  // env var values are copied from service account credentials generated by google
  // see "Authentication" section in docs for more info
  client_email: token.client_email,
  private_key: token.private_key,
});
await doc.loadInfo();

const createNewTranslationFormat = async (title) => {
  const newSheet = await doc.addSheet({
    title,
  });
  newSheet.setHeaderRow(langs);
};

const saveSheetAsTranslatedJSON = async (index) => {
  const sheet = doc.sheetsByIndex[index];
  const rows = await sheet.getRows();
  let langObj = {};
  const langs = header.slice(1, header.length);
  langs.forEach((lang) => {
    rows.forEach((row) => {
      const key = !row.Key
        ? row.Key
        : row.Key.startsWith(sheet.title + ".")
        ? row.Key
        : sheet.title + "." + row.Key;

      langObj = set(
        langObj,
        `${lang}.` + key,
        decode(
          marked
            .parse(row[lang]?.replace?.(/\\n|\n/g, "<br/>") || "", {
              disableParsingRawHTML: true,
            })
            .replace(/<p>|<\/p>/gi, ""),
          { level: "html5" }
        )
      );
    });
  });

  return {
    name: sheet.title,
    langObj,
  };
};

// console.log(saveSheetAsTranslatedJSON(1));

async function* saveAsFile() {
  try {
    const sheetcount = doc.sheetCount;
    for (let i = 0; i < sheetcount; i++) {
      const langObj = await saveSheetAsTranslatedJSON(i);

      const langs = Object.keys(langObj.langObj);
      langs.forEach((lang) => {
        const folderName = fileNameMap[lang];
        if (!fs.existsSync(path.join(__dirname, "/i18n/" + folderName))) {
          fs.mkdirSync(path.join(__dirname, "/i18n/" + folderName));
        }
        let content = langObj.langObj[lang];
        content = content[langObj.name] ?? content;
        fs.writeFileSync(
          path.join(__dirname, "/i18n/" + folderName, langObj.name + ".json"),
          JSON.stringify(content),
          "utf-8"
        );
      });
      yield {
        sheet: i,
      };
    }
  } catch (err) {
    console.log(err);
  }
}

async function save() {
  for await (const file of saveAsFile()) {
    console.log("saved file", file);
  }
}
save().catch((err) => {
  console.log(err);
});
