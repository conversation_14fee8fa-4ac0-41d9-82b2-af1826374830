const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Load translation keys and values
const translationsPath = "apps/web/src/locales/en/common.json";
const translations = JSON.parse(fs.readFileSync(translationsPath, "utf8"));

// Create a map of values to keys for lookup
const valueToKeyMap = {};
Object.entries(translations).forEach(([key, value]) => {
  // Clean up the value (remove trailing newlines, etc.)
  const cleanValue = value.toString().trim().replace(/\\n$/, "");
  valueToKeyMap[cleanValue.toLowerCase()] = key;
});

// Function to find the closest matching key for a text
function findMatchingKey(text) {
  const cleanText = text.trim().toLowerCase();

  // Direct match
  if (valueToKeyMap[cleanText]) {
    return valueToKeyMap[cleanText];
  }

  // Try without punctuation
  const noPuncText = cleanText.replace(/[^\w\s]/g, "");
  if (valueToKeyMap[noPuncText]) {
    return valueToKeyMap[noPuncText];
  }

  // Find closest match
  let bestMatch = null;
  let bestScore = 0;

  Object.entries(valueToKeyMap).forEach(([value, key]) => {
    if (cleanText.includes(value) || value.includes(cleanText)) {
      const score =
        Math.min(cleanText.length, value.length) /
        Math.max(cleanText.length, value.length);
      if (score > bestScore) {
        bestScore = score;
        bestMatch = key;
      }
    }
  });

  return bestScore > 0.7 ? bestMatch : null;
}

// Process files
function processFiles() {
  // Find all React files
  const files = glob.sync("apps/web/src/**/*.{jsx,tsx,js,ts}");
  let replacementCount = 0;

  files.forEach((file) => {
    let content = fs.readFileSync(file, "utf8");
    let modified = false;

    // Replace text in JSX
    // Look for text between tags
    const jsxTextRegex = />([^<>{}\n]+)</g;
    content = content.replace(jsxTextRegex, (match, text) => {
      const trimmedText = text.trim();
      if (trimmedText.length < 2 || /^[0-9\s]+$/.test(trimmedText)) {
        return match; // Skip very short text or numbers
      }

      const key = findMatchingKey(trimmedText);
      if (key) {
        replacementCount++;
        modified = true;
        return `>{t("common:${key}")}<`;
      }
      return match;
    });

    // Replace string literals that might be UI text
    // This is more complex and might need manual review
    const stringLiteralRegex = /(["'])([^"']+)\1/g;
    content = content.replace(stringLiteralRegex, (match, quote, text) => {
      // Skip if it looks like a variable, path, URL, etc.
      if (
        text.includes(".") ||
        text.includes("/") ||
        text.startsWith("http") ||
        text.length < 4 ||
        /^[0-9\s]+$/.test(text)
      ) {
        return match;
      }

      const key = findMatchingKey(text);
      if (key) {
        replacementCount++;
        modified = true;
        return `{t("common:${key}")}`;
      }
      return match;
    });

    // Save modified file
    if (modified) {
      fs.writeFileSync(file, content, "utf8");
      console.log(`Updated: ${file}`);
    }
  });

  console.log(`Total replacements: ${replacementCount}`);
}

// Check if the file has t function imported
function ensureImport() {
  const files = glob.sync("apps/web/src/**/*.{jsx,tsx,js,ts}");

  files.forEach((file) => {
    let content = fs.readFileSync(file, "utf8");

    // Check if the file contains translation references but no import
    if (
      content.includes('{t("common:') &&
      !content.includes("import { useTranslation }")
    ) {
      // Add import at the top of the file
      content = `import { useTranslation } from 'next-i18next';\n${content}`;

      // Add hook in component
      content = content.replace(
        /function\s+(\w+)\s*\([^)]*\)\s*{/g,
        (match) => {
          return `${match}\n  const { t } = useTranslation();`;
        }
      );

      fs.writeFileSync(file, content, "utf8");
      console.log(`Added translation import to: ${file}`);
    }
  });
}

// Run the script
processFiles();
ensureImport();
