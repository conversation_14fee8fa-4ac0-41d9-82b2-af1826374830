const axios = require("axios");
const fs = require("fs");
const path = require("path");

const BASE_URL = "http://localhost:3434";
const LIMIT = 5000;
const MAX_PER_FILE = 45000;

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// API maps to Frontend
const resourceToUrlMap = {
  "geo-municipals": "municipals",
  "geo-wards": "wards",
  "governments/level/federal": "governments",
  "governments/level/provincial": "governments",
  "governments/level/local": "governments",
  "parliaments/level/centre": "parliaments",
  "elections/results": (resource) =>
    `elections/${resource.electionId}/sub/${resource.elCode}/${resource.candidacyTypeId}`,
};

const resourceToFileMap = {
  "geo-municipals": "municipals",
  "geo-wards": "wards",
  "governments/level/federal": "governments",
  "governments/level/provincial": "governments",
  "governments/level/local": "governments",
  "parliaments/level/centre": "parliaments",
  "elections/results": "election-results",
};
const getLeadersPage = async (resource, page) => {
  const res = await axios.get(`${BASE_URL}/api/v1/${resource}`, {
    params: { limit: LIMIT, page },
  });
  return res.data?.data?.items || res.data?.data || [];
};

const getTotalItems = async (resource) => {
  const res = await axios.get(`${BASE_URL}/api/v1/${resource}`, {
    params: { limit: 1 },
  });
  return (
    res.data?.data?.totalItems ||
    res.data?.data?.length ||
    res.data?.data?.items.length ||
    0
  );
};

const generateUrls = async (resource, query) => {
  const totalItems = await getTotalItems(resource);
  const totalPages = Math.ceil(totalItems / LIMIT);
  let page = query?.page || 1;
  let fileIndex = page;
  let fileLineCount = 0;
  let totalFetched = 0;

  const filename = resourceToFileMap[resource] || resource;
  let writeStream = fs.createWriteStream(
    path.join(__dirname, `${filename}-urls-${fileIndex}.txt`)
  );

  while (page <= totalPages) {
    console.log(`Fetching page ${page} of ${resource}...`);
    const items = await getLeadersPage(resource, page);
    if (!items.length) break;
    for (const item of items) {
      const resourceName =
        resourceToUrlMap[resource]?.(item) ||
        resourceToUrlMap[resource] ||
        resource;
      const url = resourceToUrlMap[resource]?.(item)
        ? `https://www.nepaltracks.com/${resourceName}`
        : `https://www.nepaltracks.com/${resourceName}/${item.id}`;
      writeStream.write(url + "\n");
      fileLineCount++;
      totalFetched++;

      if (fileLineCount >= MAX_PER_FILE) {
        writeStream.end();
        console.log(
          `✅ File ${fileIndex} for ${filename} done with ${fileLineCount} URLs.`
        );
        fileIndex++;
        fileLineCount = 0;
        writeStream = fs.createWriteStream(
          path.join(__dirname, `${filename}-urls-${fileIndex}.txt`)
        );
      }
    }

    page++;
    await sleep(5000); // ⏱️ Wait for 5 seconds between page requests
  }

  writeStream.end();
  console.log(
    `🎉 All done for ${resource}. Total URLs written: ${totalFetched}`
  );
};

const resources = [
  "elections/results",
  // "leaders",
  // "parties",
  //   "governments/level/federal",
  // "parliaments/level/centre",
  //   "elections",
  //   "contents",
  //   "geo-municipals",
  //   "geo-wards",
];

(async function () {
  for (const resource of resources) {
    try {
      await generateUrls(resource, { page: 1 });
    } catch (err) {
      console.log(err);
    }
  }
})();
