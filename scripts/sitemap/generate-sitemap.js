const fs = require("fs");
const path = require("path");
const readline = require("readline");
const { simpleSitemapAndIndex } = require("sitemap");

const sourceDir = path.resolve(__dirname, "../../scripts/sitemap/sources");
const destinationDir = path.resolve(__dirname, "../../apps/web/public");
const hostname = "https://www.nepaltracks.com";

// Ensure destination directory exists
fs.mkdirSync(destinationDir, { recursive: true });

async function processTxtFile(txtPath) {
  const rl = readline.createInterface({
    input: fs.createReadStream(txtPath),
    crlfDelay: Infinity,
  });

  const urls = [];

  for await (const line of rl) {
    const trimmed = line.split(",");
    if (!trimmed) continue;
    const [link, modifiedTime] = trimmed;
    // 🛠️ Customize here
    urls.push({
      url: link,
      changefreq: "weekly",
      priority: 0.7,
      lastmod: modifiedTime || new Date().toISOString(),
    });
  }

  console.log(urls);

  return urls;
}

(async () => {
  const txtFiles = fs
    .readdirSync(sourceDir)
    .filter((file) => path.extname(file).toLowerCase() === ".txt")
    .map((file) => path.join(sourceDir, file));

  const all = await Promise.all(txtFiles.map(processTxtFile));
  console.log(all);
  await simpleSitemapAndIndex({
    limit: 10000,
    hostname,
    destinationDir,
    sourceData: all.flat(),
  });

  console.log("✅ Sitemaps and index generated successfully.");
})();
