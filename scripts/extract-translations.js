const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Function to generate a translation key from text
function generate<PERSON><PERSON>(text) {
  // Convert text to snake_case
  let key = text
    .toLowerCase()
    .replace(/[^\w\s]/g, "")
    .trim()
    .replace(/\s+/g, "_");

  return key;
}

// Function to check if a string looks like code
function looksLikeCode(text) {
  // Patterns that suggest code rather than user-facing text
  const codePatterns = [
    /[{}()<>]/, // Brackets and parentheses
    /\w+\.\w+/, // Something like object.property
    /\w+\(/, // Function calls
    /import|export|const|let|var|function|return|if|else|for|while/, // JS keywords
    /={.*}/, // JSX props
    /\${.*}/, // Template literals
    /^\s*[a-z]+[A-Z][a-z]+/, // camelCase
    /[a-zA-Z]+_[a-zA-Z]+/, // snake_case
    /^[<>{}[\]]/, // Starts with brackets
    /[;:]/, // Semicolons or colons
    /&&|\|\|/, // Logical operators
    /=>/, // Arrow functions
    /\s*\w+\s*:\s*\w+/, // Object properties
    /\d+px/, // CSS measurements
    /^[\.#]\w+/, // CSS selectors
    /^https?:\/\//, // URLs
    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Emails
  ];

  return codePatterns.some((pattern) => pattern.test(text));
}

// Function to extract text from JSX/TSX files
function extractTextFromReactFiles() {
  const files = glob.sync("apps/web/src/**/*.{jsx,tsx,js,ts}");
  const translations = {};

  // Regex to find text between JSX tags
  const jsxTextRegex = />([^<>{}\n]+)</g;

  // Regex to find string literals that might be UI text
  const stringLiteralRegex = /["']([^"']+)["']/g;

  files.forEach((file) => {
    const content = fs.readFileSync(file, "utf8");

    // Extract text between JSX tags
    let match;
    while ((match = jsxTextRegex.exec(content)) !== null) {
      const text = match[1].trim();
      if (
        text &&
        text.length > 1 &&
        !/^[0-9\s]+$/.test(text) &&
        !looksLikeCode(text)
      ) {
        const key = generateKey(text);
        translations[key] = text;
      }
    }

    // Extract string literals that might be UI text
    while ((match = stringLiteralRegex.exec(content)) !== null) {
      const text = match[1].trim();
      if (
        text &&
        text.length > 3 &&
        !/^[0-9\s]+$/.test(text) &&
        !text.includes(".") &&
        !text.includes("/") &&
        !text.startsWith("http") &&
        !looksLikeCode(text) &&
        /[a-zA-Z]/.test(text) // Contains at least one letter
      ) {
        const key = generateKey(text);
        translations[key] = text;
      }
    }
  });

  return translations;
}

// Function to extract text from HTML files
function extractTextFromHtmlFiles() {
  const files = glob.sync("apps/web/src/**/*.html");
  const translations = {};

  // Regex to find text between HTML tags
  const htmlTextRegex = />([^<>{}\n]+)</g;

  files.forEach((file) => {
    const content = fs.readFileSync(file, "utf8");

    let match;
    while ((match = htmlTextRegex.exec(content)) !== null) {
      const text = match[1].trim();
      if (
        text &&
        text.length > 1 &&
        !/^[0-9\s]+$/.test(text) &&
        !looksLikeCode(text)
      ) {
        const key = generateKey(text);
        translations[key] = text;
      }
    }
  });

  return translations;
}

// Main function
function main() {
  const reactTranslations = extractTextFromReactFiles();
  const htmlTranslations = extractTextFromHtmlFiles();

  // Merge translations
  const translations = { ...reactTranslations, ...htmlTranslations };

  // Create directory if it doesn't exist
  const outputDir = path.join("apps/web/src/locales/en");
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Write to JSON file
  fs.writeFileSync(
    path.join(outputDir, "extracted.json"),
    JSON.stringify(translations, null, 2)
  );

  console.log(
    `Extracted ${
      Object.keys(translations).length
    } text strings to apps/web/src/locales/en/extracted.json`
  );
}

main();
