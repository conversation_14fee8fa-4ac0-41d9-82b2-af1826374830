# DataTable Component Usage Guide

## Overview

The `DataTable` component is a reusable component that abstracts away the common pattern of:
1. Fetching data from an API endpoint
2. Displaying the data in a table
3. Handling loading states
4. Supporting pagination

## Basic Usage

```tsx
import DataTable from "@/components/DataTable";
import { Avatar, Group, Text } from "@mantine/core";
import Link from "next/link";

const GovernmentPartiesTable = ({ governmentId, level }) => {
  return (
    <DataTable
      endpoint={`governments/${level}/${governmentId}/parties`}
      queryKey={`governmentParties-${governmentId}`}
      title="Coalition Parties"
      columns={[
        {
          id: "party",
          label: "Party",
          renderCell: (_, row) => (
            <Link href={`/parties/${row.party.id}`}>
              <Group gap="sm">
                <Avatar size="md" src={row.party.logo} />
                <Text fw={500}>{row.party.name}</Text>
              </Group>
            </Link>
          ),
        },
        {
          id: "seats",
          label: "Seats",
        },
      ]}
    />
  );
};
```

## Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| `endpoint` | `string` | API endpoint to fetch data from | Required |
| `queryKey` | `string` | Unique key for react-query cache | Required |
| `columns` | `DataTableColumn[]` | Column definitions for the table | Required |
| `queryParams` | `Record<string, any>` | Additional query parameters to pass to the API | `{}` |
| `limit` | `number` | Number of items per page | `10` |
| `initialPage` | `number` | Initial page number | `1` |
| `title` | `string` | Title to display above the table | `undefined` |
| `minHeight` | `number` | Minimum height of the table | `500` |
| `showPagination` | `boolean` | Whether to show pagination | `true` |

## Column Definition

Each column is defined with the following properties:

```ts
interface DataTableColumn {
  id: string;
  label: string | JSX.Element;
  renderCell?: (value: any, data: unknown) => JSX.Element;
}
```

- `id`: The key in the data object to display in this column
- `label`: The column header text or element
- `renderCell`: Optional function to customize how the cell is rendered. If not provided, the raw value will be displayed.

## Examples

### Basic Table

```tsx
<DataTable
  endpoint="leaders"
  queryKey="leaders"
  columns={[
    { id: "localName", label: "Name" },
    { id: "birthDate", label: "Birth Date" },
    { id: "address", label: "Address" },
  ]}
/>
```

### Table with Custom Cell Rendering

```tsx
<DataTable
  endpoint="elections/1/candidates"
  queryKey="electionCandidates"
  columns={[
    {
      id: "name",
      label: "Candidate",
      renderCell: (_, row) => (
        <Link href={`/leaders/${row.id}`}>
          <Group gap="sm">
            <Avatar size="md" src={row.img} />
            <Text fw={500}>{row.name}</Text>
          </Group>
        </Link>
      ),
    },
    {
      id: "votes",
      label: "Votes",
      renderCell: (value) => <Badge>{value}</Badge>,
    },
    {
      id: "isElected",
      label: "Status",
      renderCell: (value) => (
        value ? <Badge color="green">Elected</Badge> : <Badge color="red">Not Elected</Badge>
      ),
    },
  ]}
/>
```

### Table with Additional Query Parameters

```tsx
<DataTable
  endpoint="leaders"
  queryKey="filteredLeaders"
  queryParams={{
    partyId: 123,
    isActive: true,
  }}
  columns={[
    { id: "localName", label: "Name" },
    { id: "position", label: "Position" },
  ]}
/>
```

### Table without Pagination

```tsx
<DataTable
  endpoint="parties/top"
  queryKey="topParties"
  showPagination={false}
  minHeight={200}
  columns={[
    { id: "name", label: "Party" },
    { id: "members", label: "Members" },
  ]}
/>
```

## Benefits

- **Reduced Boilerplate**: No need to write the same data fetching and table rendering code repeatedly
- **Consistent UI**: All tables in the application have the same look and feel
- **Improved Maintainability**: Changes to table styling or behavior can be made in one place
- **Type Safety**: TypeScript types ensure correct usage
